module git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing

go 1.15

require (
	git.garena.com/shopee/bg-logistics/dms/go-proto v0.0.10
	git.garena.com/shopee/bg-logistics/go/chassis v0.4.3-r.11
	git.garena.com/shopee/bg-logistics/go/chassis-saturn-server v1.0.8-r.6
	git.garena.com/shopee/bg-logistics/go/go-elasticsearch/v7 v7.0.1-r.4
	git.garena.com/shopee/bg-logistics/go/go-redis v0.0.1-r.4
	git.garena.com/shopee/bg-logistics/go/gocommon v0.4.2-r.28
	git.garena.com/shopee/bg-logistics/go/redislock v0.0.1-r.0.7.2
	git.garena.com/shopee/bg-logistics/go/scormv2 v0.0.1-r.26
	git.garena.com/shopee/bg-logistics/go/scsps v0.0.1-r.1
	git.garena.com/shopee/bg-logistics/go/ssc-gohbase v0.0.1-r.3
	git.garena.com/shopee/bg-logistics/logistics/comparator v0.0.8
	git.garena.com/shopee/bg-logistics/logistics/fulfilment_event_trace v0.0.23
	git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache v0.2.0-r.9
	git.garena.com/shopee/bg-logistics/logistics/logistics-lane-fulfilment-system/protocol v0.14.6
	git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common v0.0.5
	git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol v1.0.72
	git.garena.com/shopee/bg-logistics/logistics/proto-center/v16 v16.30.5
	git.garena.com/shopee/bg-logistics/logistics/proto-center/v21 v21.1.4
	git.garena.com/shopee/bg-logistics/logistics/proto-center/v3 v3.59.5
	git.garena.com/shopee/bg-logistics/logistics/proto-center/v4 v4.27.5
	git.garena.com/shopee/bg-logistics/logistics/spex-proto-center v0.0.8
	git.garena.com/shopee/bg-logistics/techplatform/stability-assurance-platform/pkg v0.0.21
	git.garena.com/shopee/platform/service-governance/viewercontext v1.0.13
	github.com/360EntSecGroup-Skylar/excelize/v2 v2.3.1
	github.com/Shopify/sarama v1.30.1
	github.com/agiledragon/gomonkey/v2 v2.12.0
	github.com/aws/aws-sdk-go v1.44.0
	github.com/bytedance/sonic v1.10.0
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/emicklei/go-restful v2.16.0+incompatible
	github.com/gammazero/workerpool v1.1.2
	github.com/go-chassis/go-archaius v1.6.0-beta1
	github.com/go-faker/faker/v4 v4.0.0-beta.4
	github.com/gogo/protobuf v1.3.2
	github.com/golang/protobuf v1.5.2
	github.com/google/uuid v1.6.0
	github.com/google/wire v0.6.0
	github.com/hashicorp/golang-lru v0.6.0
	github.com/howeyc/crc16 v0.0.0-20171223171357-2b2a61e366a6
	github.com/json-iterator/go v1.1.12
	github.com/juju/ratelimit v1.0.2
	github.com/klauspost/compress v1.14.2
	github.com/mailru/dbr v3.0.0+incompatible
	github.com/mailru/go-clickhouse v1.8.0
	github.com/modern-go/reflect2 v1.0.2
	github.com/mroth/weightedrand v0.4.1
	github.com/oliveagle/jsonpath v0.0.0-20180606110733-2e52cf6e6852
	github.com/panjf2000/ants/v2 v2.8.1
	github.com/patrickmn/go-cache v2.1.0+incompatible
	github.com/pkg/errors v0.9.1
	github.com/processout/grpc-go-pool v1.2.1
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/satori/go.uuid v1.2.0
	github.com/shakinm/xlsReader v0.9.12
	github.com/shopspring/decimal v1.3.1
	github.com/spf13/cast v1.4.1
	github.com/stretchr/testify v1.8.1
	github.com/xdg/scram v1.0.3
	github.com/xuri/efp v0.0.0-20220407160117-ad0f7a785be8 // indirect
	go.uber.org/atomic v1.9.0
	go.uber.org/ratelimit v0.2.0
	golang.org/x/image v0.0.0-20211028202545-6944b10bf410 // indirect
	golang.org/x/time v0.3.0
	google.golang.org/grpc v1.45.0
	google.golang.org/protobuf v1.28.1
	gopkg.in/DATA-DOG/go-sqlmock.v1 v1.3.0 // indirect
	gopkg.in/go-playground/validator.v9 v9.31.0
	gorm.io/driver/mysql v1.3.4
	modernc.org/mathutil v1.4.1
)

replace google.golang.org/grpc v1.45.0 => google.golang.org/grpc v1.41.0

replace git.garena.com/shopee/platform/splog v1.4.7 => git.garena.com/shopee/platform/splog v1.4.12
