package sls

import "git.garena.com/shopee/data-infra/dataservice-sdk-golang/dataservice"

const (
	CountModeFull      = "Full"      // 指定时间范围内, 全量加载
	CountModeIncrement = "Increment" // 近两个月, 指定的 Supplier Id 下的数据, 做增量加载
)

type CashbackPullBySystemCountRpcJobConfig struct {
	PullStartTime                   uint32 `yaml:"pull_start_time"`                       // 业务单表扫描的时间范围
	PullEndTime                     uint32 `yaml:"pull_end_time"`                         // 业务单表扫描的时间范围
	CountMode                       string `yaml:"count_mode"`                            // 枚举: Full,Increment; 分别代表全量加载、增量加载
	QueryLimit                      int    `yaml:"query_limit"`                           // 每次查询 DB 的数据量, 默认每次 2000 条
	PullBySystemDataApiParallelNum  int    `yaml:"pull_by_system_data_api_parallel_num"`  //
	PullBySystemDataApiQueryPattern string `yaml:"pull_by_system_data_api_query_pattern"` //
	DataApiMockRequestId            string `yaml:"data_api_mock_request_id"`              //
	IncrementScanLowerBound         uint32 `yaml:"increment_scan_lower_bound"`            // 单位:s, 表示增量扫描的时间范围, 从当前时间往前推多少秒; 默认值为 2x30x24x3600, 即近两个月
	PoolSize                        int    `yaml:"pool_size"`                             //
	SleepTime                       int    `yaml:"sleep_time"`                            // 单位:s
}

func (cfg CashbackPullBySystemCountRpcJobConfig) GetPoolSize() int {
	const DefaultPoolSize = 20
	if cfg.PoolSize == 0 {
		return DefaultPoolSize
	}
	return cfg.PoolSize
}

func (cfg CashbackPullBySystemCountRpcJobConfig) GetSleepTime() int {
	const DefaultSleepTime = 10
	if cfg.SleepTime == 0 {
		return DefaultSleepTime
	}
	return cfg.SleepTime
}

func (cfg CashbackPullBySystemCountRpcJobConfig) GetIncrementScanLowerBound() uint32 {
	const DefaultIncrementScanLowerBound = 2 * 30 * 24 * 3600
	if cfg.IncrementScanLowerBound == 0 {
		return DefaultIncrementScanLowerBound
	}
	return cfg.IncrementScanLowerBound
}

func (cfg CashbackPullBySystemCountRpcJobConfig) GetQueryLimit() int {
	if cfg.QueryLimit == 0 {
		return 2000
	}
	return cfg.QueryLimit
}

func (cfg CashbackPullBySystemCountRpcJobConfig) GetPullBySystemDataApiParallelNum() int {
	const DefaultPullBySystemDataApiParallelNum = 5
	if cfg.PullBySystemDataApiParallelNum == 0 {
		return DefaultPullBySystemDataApiParallelNum
	}
	return cfg.PullBySystemDataApiParallelNum
}

func (cfg CashbackPullBySystemCountRpcJobConfig) GetPullBySystemDataApiQueryPattern() dataservice.QueryPattern {

	//
	const DefaultQueryPattern = dataservice.OLAP

	//
	if cfg.PullBySystemDataApiQueryPattern == "" {
		return DefaultQueryPattern
	}
	return dataservice.QueryPattern(cfg.PullBySystemDataApiQueryPattern)
}
