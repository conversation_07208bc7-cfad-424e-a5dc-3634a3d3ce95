package sls

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/utils/str_conv"
	"strings"
	"time"
)

const (
	DefaultArchiveRecordNum       = 100000
	DefaultArchiveQueryBatchSize  = 1000
	DefaultDeleteRecordNum        = 100000 // default:10w
	DefaultDeleteBatchSize        = 1000
	DefaultBatchIntervalSleepTime = 10
	DefaultRetryTime              = 3
	DefaultRetryTimeInterval      = 50
)

var DefaultArchiveFeeTypeList = []enum.FeeType{enum.ESF, enum.SellerASF, enum.ThreePLASF}
var DefaultArchiveStartTimeMap = map[enum.FeeType]uint32{
	enum.ESF:        3 * MonthInSecond, // 3 个月
	enum.SellerASF:  6 * MonthInSecond, // 6 个月
	enum.ThreePLASF: 8 * MonthInSecond, // 8 个月
}

type RateArchiveConfig struct {
	ArchiveFeeTypeList     string `yaml:"archive_fee_type_list"` // 多个 FeeType 使用,分割
	ArchiveStartTime       string `yaml:"archive_start_time"`    // 归档时间, 配置格式: {"1": 100}, 表示 FeeType = 1, start time 距今超过 100s, 可以被归档
	ArchiveRecordNum       int64  `yaml:"archive_record_num"`    // 默认 50w, 每次执行归档任务时, 最多归档多少条数据
	ArchiveQueryBatchSize  int64  `yaml:"archive_query_batch_size"`
	DeleteRecordNum        int64  `yaml:"delete_record_num"` // 默认 10w, 每次执行归档任务时, 最多删除多少条数据
	DeleteBatchSize        int64  `yaml:"delete_batch_size"`
	BatchIntervalSleepTime int64  `yaml:"batch_interval_sleep_time"` // 单位:ms, 默认值为 10

	// 重试配置
	RetryTime           int                     `yaml:"retry_time"`          // 默认 3 次
	RetryTimeInterval   int                     `yaml:"retry_time_interval"` // 单位:ms, 默认 50
	ArchiveStartTimeMap map[enum.FeeType]uint32 `yaml:"-"`
}

func (c *RateArchiveConfig) GetArchiveFeeTypeList() []enum.FeeType {
	if c == nil || c.ArchiveFeeTypeList == "" {
		return DefaultArchiveFeeTypeList
	}

	var list []enum.FeeType
	for _, val := range strings.Split(c.ArchiveFeeTypeList, ",") {
		list = append(list, enum.FeeType(str_conv.ParseInt64MuteErr(val)))
	}
	return list
}

func (c *RateArchiveConfig) GetArchiveStartTimeMap() map[enum.FeeType]uint32 {
	if c == nil || c.ArchiveStartTime == "" {
		return DefaultArchiveStartTimeMap
	}
	return c.ArchiveStartTimeMap
}

func (c *RateArchiveConfig) GetArchiveRecordNum() int64 {
	if c == nil || c.ArchiveRecordNum == 0 {
		return DefaultArchiveRecordNum
	}
	return c.ArchiveRecordNum
}

func (c *RateArchiveConfig) GetArchiveQueryBatchSize() int64 {
	if c == nil || c.ArchiveQueryBatchSize == 0 {
		return DefaultArchiveQueryBatchSize
	}
	return c.ArchiveQueryBatchSize
}

func (c *RateArchiveConfig) GetDeleteRecordNum() int64 {
	if c == nil || c.DeleteRecordNum == 0 {
		return DefaultDeleteRecordNum
	}
	return c.DeleteRecordNum
}

func (c *RateArchiveConfig) GetDeleteBatchSize() int64 {
	if c == nil || c.DeleteBatchSize == 0 {
		return DefaultDeleteBatchSize
	}
	return c.DeleteBatchSize
}

func (c *RateArchiveConfig) GetBatchIntervalSleepTime() int64 {
	if c == nil || c.BatchIntervalSleepTime == 0 {
		return DefaultBatchIntervalSleepTime
	}
	return c.BatchIntervalSleepTime
}

func (c *RateArchiveConfig) GetRetryTime() int {
	if c == nil || c.RetryTime == 0 {
		return DefaultRetryTime
	}
	return c.RetryTime
}

func (c *RateArchiveConfig) GetRetryTimeInterval() time.Duration {
	if c == nil || c.RetryTimeInterval == 0 {
		return time.Millisecond * DefaultRetryTime
	}
	return time.Millisecond * time.Duration(c.RetryTimeInterval)
}
