package valueutil

import "strconv"

func NewInt64(v int64) *int64 {
	return &v
}
func NewInt32(v int32) *int32 {
	return &v
}
func NewInt8(v int8) *int8 {
	return &v
}
func NewUint32(v uint32) *uint32 {
	return &v
}
func NewUint64(v uint64) *uint64 {
	return &v
}
func NewString(v string) *string {
	return &v
}
func NewBool(b bool) *bool {
	return &b
}
func NewFloat64(v float64) *float64 {
	return &v
}
func NewFloat32(v float32) *float32 {
	return &v
}

func SliceConvertInt32ToInt64(vals []int32) []int64 {
	res := make([]int64, len(vals))
	for i := range vals {
		res[i] = int64(vals[i])
	}
	return res
}

func SliceConvertInt64ToInt32(vals []int64) []int32 {
	res := make([]int32, len(vals))
	for i := range vals {
		res[i] = int32(vals[i])
	}
	return res
}

func SliceConvertInt64ToInt64(vals []int64) []int64 {
	res := make([]int64, len(vals))
	for i := range vals {
		res[i] = int64(vals[i])
	}
	return res
}

func ParseFloat64(s string, bitSize int) float64 {
	f, _ := strconv.ParseFloat(s, bitSize)
	return f
}
