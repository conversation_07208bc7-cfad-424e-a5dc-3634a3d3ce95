package enum

import "fmt"

// this file contains fulfillment resource type/subtype etc.
// should be kept in-sync with fulfillment system
type ResourceSubType uint64

// line sub type
const (
	LineSubTypeDefault    ResourceSubType = 0
	C_FL                  ResourceSubType = 1 << 0
	C_LM                  ResourceSubType = 1 << 1
	C_FM                  ResourceSubType = 1 << 2
	C_LineHaul            ResourceSubType = 1 << 3
	C_FLAndLM             ResourceSubType = 1 << 4
	C_FMAndFLAndLM        ResourceSubType = 1 << 5
	L_FM                  ResourceSubType = 1 << 6
	L_LM                  ResourceSubType = 1 << 7
	L_FMAndLM             ResourceSubType = 1 << 8
	L_LineHaul            ResourceSubType = 1 << 9
	L_SelfBuild           ResourceSubType = 1 << 10
	L_MM                  ResourceSubType = 1 << 11
	C_ExportAndImport_ILH ResourceSubType = 1 << 12
	C_Export_ILH          ResourceSubType = 1 << 13
	C_Import_ILH          ResourceSubType = 1 << 14
)

// site sub type
const (
	SiteSubTypeDefault ResourceSubType = 0
	// ShipSite sub type
	SelfPostSite   ResourceSubType = 1 // 自寄点
	NormalShipSite ResourceSubType = 2 // 其他发货地

	// JointSite sub type
	TWS                 ResourceSubType = 3 // TWS
	ThirdPartyJointSite ResourceSubType = 4 // 3PL交接点
	NormalJointSite     ResourceSubType = 5 // 其他中转点
	SocJointSite        ResourceSubType = 8 // sorting center

	// DeliverSite sub type
	SelfCollectSite   ResourceSubType = 6 // 自提点
	NormalDeliverSite ResourceSubType = 7 // 其他收件地
)

type ResourceType uint32

const (
	ResourceTypeSite ResourceType = 1
	ResourceTypeLine ResourceType = 2
)

var (
	LastMileFlagList  = []ResourceSubType{C_LM, C_FLAndLM, C_FMAndFLAndLM, L_LM, L_FMAndLM}
	FirstMileFlagList = []ResourceSubType{C_FM, C_FMAndFLAndLM, L_FMAndLM, L_FM}
)

func IsLastMile(rt ResourceType, rst ResourceSubType) bool {
	if rt != ResourceTypeLine {
		return false
	}

	return ContainsResourceSubType(LastMileFlagList, rst)
}

func IsTWS(rt ResourceType, rst ResourceSubType) bool {
	if rt != ResourceTypeSite {
		return false
	}
	return rst == TWS
}

func IsSoc(rt ResourceType, rst ResourceSubType) bool {
	return rst == SocJointSite
}

func IsFM(rt ResourceType, rst ResourceSubType) bool {
	if rt != ResourceTypeLine {
		return false
	}
	return ContainsResourceSubType(FirstMileFlagList, rst)
}

func ContainsResourceSubType(arr []ResourceSubType, val ResourceSubType) bool {
	for _, elem := range arr {
		if elem == val {
			return true
		}
	}
	return false

}

type ResourceRole struct {
	ResourceType    ResourceType
	ResourceSubType ResourceSubType
}

func (rr ResourceRole) String() string {
	return fmt.Sprintf("resource_type=%d, resource_sub_type=%d", rr.ResourceType, rr.ResourceSubType)
}

var (
	ResourceRole_FM      = &ResourceRole{ResourceType: ResourceTypeLine, ResourceSubType: C_FM}
	ResourceRole_LocalLM = &ResourceRole{ResourceType: ResourceTypeLine, ResourceSubType: L_LM}
	ResourceRole_TWS     = &ResourceRole{ResourceType: ResourceTypeSite, ResourceSubType: TWS}
	ResourceRole_Soc     = &ResourceRole{ResourceType: ResourceTypeSite, ResourceSubType: SocJointSite}
)

func (r *ResourceRole) IsLastMile() bool {
	return IsLastMile(r.ResourceType, r.ResourceSubType)
}

func (r *ResourceRole) IsTWS() bool {
	return IsTWS(r.ResourceType, r.ResourceSubType)
}

func (r *ResourceRole) IsSoc() bool {
	return IsSoc(r.ResourceType, r.ResourceSubType)
}

func (r *ResourceRole) IsFM() bool {
	return IsFM(r.ResourceType, r.ResourceSubType)
}

func (r *ResourceRole) ToWeightDataSource() WeightDataSource {
	if r.IsLastMile() {
		return WeightDataSourceLM
	}
	if r.IsTWS() {
		return WeightDataSourceTWS
	}

	return WeightDataSource(0)
}
