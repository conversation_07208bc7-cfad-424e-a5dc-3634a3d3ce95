package spx_workforce_lib

import "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/http"

type GetStatisticOTClockListReq struct {
	BizStaffId string `json:"biz_staff_id"`
	StartTime  int64  `json:"start_time"`
	EndTime    int64  `json:"end_time"`
}

type GetStatisticOTClockListResp struct {
	http.StandardHttpResp
	Data StatisticOTClockList `json:"data"`
}

type StatisticOTClockList struct {
	OT         []*OTItem         `json:"ot"`
	Attendance []*AttendanceItem `json:"attendance"`
}

type OTItem struct {
	OTDateStart         int64  `json:"ot_date_start"` // 秒
	OTDateEnd           int64  `json:"ot_date_end"`
	OTHoursApplied      string `json:"ot_hours_applied"`       // 小时
	ActualOTHoursWorked string `json:"actual_ot_hours_worked"` // 小时
}

type AttendanceItem struct {
	EventStartTime int64 `json:"event_start_time"`
	EventEndTime   int64 `json:"event_end_time"`
	ClockInTime    int64 `json:"clock_in_time"`
	ClockOutTime   int64 `json:"clock_out_time"`
}

const (
	PaidTypeWithoutSalary int8 = 1 //1 不带薪
	PaidTypeWithSalary    int8 = 2 //2：带薪
)

type GetLeaveDayListReq struct {
	DriverIdList []string `json:"driver_id_list"`
	StartTime    int64    `json:"start_time"`
	EndTime      int64    `json:"end_time"`
	PaidType     int8     `json:"paid_type"`
}

type GetLeaveDayListResp struct {
	http.StandardHttpResp
	Data LeaveDayData `json:"data"`
}
type LeaveDayData struct {
	List []LeaveDayItem `json:"list"`
}
type LeaveDayItem struct {
	DriverId  string `json:"driver_id"`
	StartTime int64  `json:"start_time"`
	EndTime   int64  `json:"end_time"`
}
