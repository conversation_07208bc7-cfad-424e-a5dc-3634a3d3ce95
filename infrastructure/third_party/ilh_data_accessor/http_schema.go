package ilh_data_accessor

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/service/excel_handler/excel_parser"
	forcaste_do "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/sls/ilh_sf/forecast_task/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/http"
)

type GetILHTaskByTaskIDReq struct {
	TaskID uint64 `json:"task_id"`
}
type GetILHTaskByTaskIDResp struct {
	http.StandardHttpResp
	Data *do.IlhAsyncTaskTab `json:"data"`
}

type UpdateILHTaskWithParamsByTaskIDReq struct {
	AsyncTask do.IlhAsyncTaskTab `json:"async_task"`
}

type UpdateILHTaskWithParamsByTaskIDResp struct {
	http.StandardHttpResp
}

type GetForecastTaskByIdReq struct {
	TabId int64 `json:"tab_id"`
}
type GetForecastTaskByIdResp struct {
	http.StandardHttpResp
	Data *forcaste_do.IlhForecastTaskTab `json:"data"`
}

type ILHReconTaskApiReq struct {
	BillNo         string            `json:"bill_no" binding:"required"`
	TaskType       enum.ILHReconTask `json:"task_type" binding:"required"`
	FileUrl        string            `json:"file_url" binding:"required"`
	OrderCount     int32             `json:"order_count"`
	BillCreateTime int32             `json:"bill_create_time,omitempty"`
	SyncTask       bool              `json:"sync_task,omitempty"`
	Region         string            `json:"region"`
	Token          string            `json:"token"`
}

type ILHReconTaskApiResp struct {
	BillNo       string            `json:"bill_no"`
	TaskType     enum.ILHReconTask `json:"task_type"`
	ChargeTaskId int64             `json:"charge_task_id"`
}

type ILHMawbCostBatchCalcReq struct {
	RequestList         []*excel_parser.ILHCommonParseResult `json:"request_list"`
	TaskType            enum.ILHReconTask                    `json:"task_type"`
	ReconBillCreateTime int32                                `json:"recon_bill_create_time,omitempty"`
	Url                 string                               `json:"url"`
}

type ILHMawbCostBatchCalcResp struct {
	MawbNo                     string                                `json:"mawb_no,omitempty"`
	TpType                     enum.TpType                           `json:"tp_type,omitempty"`
	BaseFeeMap                 map[enum.ShippingFeeComponent]float64 `json:"base_fee_map,omitempty"`
	MinChargeWeightCheckResult enum.ILHMinChargeWeightCheckResult    `json:"min_charge_weight_check_result,omitempty"`
	DurationCheckResult        string                                `json:"duration_check_result,omitempty"`
	DurationCheckErrMsg        string                                `json:"duration_check_err_msg,omitempty"`
	FinalBsfConfigMap          map[string][]int64                    `json:"final_bsf_config_map,omitempty"`
	FinalExtraFeeConfigMap     map[string][]int64                    `json:"final_extra_fee_config_map,omitempty"`
	IlhMawbCostErrResp         map[string]IlhMawbCostErrResp         `json:"ilh_mawb_cost_err_resp,omitempty"`
}

type IlhMawbCostErrResp struct {
	SSCErr                 string                 `json:"ssc_err,omitempty"`
	ErrMsg                 string                 `json:"err_msg,omitempty"`
	ErrType                enum.ILHBillingErrType `json:"err_type,omitempty"`
	MultiQuotationConfigId []int64                `json:"multi_quotation_config_id,omitempty"`
	FeeType                enum.FeeType           `json:"fee_type,omitempty"`
	ExtraFeeType           enum.ExtraFeeComponent `json:"extra_fee_type,omitempty"`
}
