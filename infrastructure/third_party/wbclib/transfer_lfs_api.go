package wbclib

import (
	"errors"
	"fmt"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/third_party/lfslib/lfslib_schema"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/utils/std_format"
)

func TransferWBCSyncOrderResponseDataToLfsSyncOrderData(in *SyncOrderResponseData) (*lfslib_schema.LfsSyncOrderData, error) {
	onErr := func(e error) (*lfslib_schema.LfsSyncOrderData, error) {
		return nil, fmt.Errorf("TransferWBCSyncOrderResponseDataToLfsSyncOrderData: %w", e)
	}
	lmWaybill := getLmWaybill(in.WaybillFieldList)
	if lmWaybill == nil {
		return onErr(errors.New("no last mile waybill is found"))
	}
	codAmount, err := std_format.ParseFloat64(in.SloField.CodAmount)
	if err != nil {
		return onErr(fmt.Errorf("parse cod_amount: %w", err))
	}
	cogs, err := std_format.ParseFloat64(in.SloField.Cogs)
	if err != nil {
		return onErr(fmt.Errorf("parse cogs: %w", err))
	}
	totalPrice, err := std_format.ParseFloat64(in.SloField.TotalPrice)
	if err != nil {
		return onErr(fmt.Errorf("parse total_price: %w", err))
	}
	asf, err := std_format.ParseFloat64(in.SloField.SellerAsf)
	if err != nil {
		return onErr(fmt.Errorf("parse actual_shipping_fee: %w", err))
	}
	skuInfo, err := transferSkus(in.SloField.SkuInfo)
	if err != nil {
		return onErr(fmt.Errorf("parse sku_info: %w", err))
	}
	res := &lfslib_schema.LfsSyncOrderData{
		MerchantRegion: in.MerchantRegion,
		SlsTrackingNo:  in.SloField.SloTn,
		OrderSn:        in.SloField.OrderSn,
		LogID:          in.SloField.SloId,
		ServiceCode:    in.SloField.ServiceCode,
		PaymentMethod:  in.SloField.PaymentMethod,
		CodAmount:      codAmount,
		Cogs:           cogs,
		TotalPrice:     totalPrice,
		// WeightInfo is set below
		WeightInfo: lfslib_schema.WeightInfo{},
		// SizeInfo is set below
		SizeInfo: lfslib_schema.SizeInfo{},
		WmsFlag:  int(in.SloField.WmsFlag),
		DgFlag:   float64(in.SloField.DgFlag),
		LocationInfo: lfslib_schema.LocationInfo{
			Distance:        int(in.SloField.LocationInfo.Distance),
			PickupLocation:  transferLocation(in.SloField.LocationInfo.PickupLocation),
			DeliverLocation: transferLocation(in.SloField.LocationInfo.DeliverLocation),
		},
		Direction:    int(in.SloField.Direction),
		SlsTnCtime:   int(in.SloField.SlsTnCtime),
		ShipmentType: int(in.SloField.ShipmentType),
		FeeDetail: lfslib_schema.FeeDetail{
			SellerAsf: asf,
			// FreightFee:       in.FeeDetail.FreightFee,
		},
		SkuInfo:      skuInfo,
		DsDistance:   int(in.SloField.DistanceDs),
		DecoupleFlag: int8(in.SloField.DecoupleFlag),
		WhsCode:      in.SloField.WhsCode,
		WhsId:        in.SloField.WhsId,
		SellerId:     in.SloField.SellerId,
	}
	if in.ExceptionMessage != nil {
		res.ExceptionOrderMessage = &lfslib_schema.ExceptionOrderMessage{}
		res.ExceptionOrderMessage.ExceptionType = in.ExceptionMessage.ExceptionType
		res.ExceptionOrderMessage.Esf = in.ExceptionMessage.Esf
		res.ExceptionOrderMessage.ExceptionRole = in.ExceptionMessage.ExceptionRole
		res.ExceptionOrderMessage.ExceptionOccurTime = in.ExceptionMessage.ExceptionOccurTime
		res.ExceptionOrderMessage.ExceptionResourceId = in.ExceptionMessage.ExceptionResourceId
		res.ExceptionOrderMessage.ChargeableWeightParcel = in.ExceptionMessage.ChargeableWeightParcel
		res.ExceptionOrderMessage.ChargeableWeightSku = in.ExceptionMessage.ChargeableWeightSku
		res.ExceptionOrderMessage.GrandTotal = in.ExceptionMessage.GrandTotal
		res.ExceptionOrderMessage.SellerVoucher = in.ExceptionMessage.SellerVoucher
		res.ExceptionOrderMessage.FailedReason = in.ExceptionMessage.FailedReason
	}
	err = applyLastMileWaybill(res, lmWaybill)
	if err != nil {
		return onErr(err)
	}
	twsWaybill := getTwsWaybill(in.WaybillFieldList)
	err = applyTwsWaybill(res, twsWaybill)
	if err != nil {
		return onErr(err)
	}
	return res, nil
}

func applyTwsWaybill(data *lfslib_schema.LfsSyncOrderData, twsWaybill *NotifyWaybillData) error {
	if twsWaybill == nil {
		return nil
	}
	onErr := func(e error) error {
		return fmt.Errorf("applyTwsWaybill: %w", e)
	}

	twsSize, err := transferSize(twsWaybill.PushedDimensionInfo)
	if err != nil {
		return onErr(err)
	}
	data.WeightInfo.TwsWeight = int(twsWaybill.PushedWeightInfo.ActualWeight)
	data.SizeInfo.TwsSize = *twsSize

	return nil
}

func applyLastMileWaybill(data *lfslib_schema.LfsSyncOrderData, lmWaybill *NotifyWaybillData) error {
	if lmWaybill == nil {
		return nil
	}
	onErr := func(e error) error {
		return e
	}
	lmSize, err := transferSize(lmWaybill.PushedDimensionInfo)
	if err != nil {
		return onErr(fmt.Errorf("parse pushed_dimension: %w", err))
	}
	lmPushedFee, err := std_format.ParseFloat64(lmWaybill.PushedShippingFee)
	if err != nil {
		return onErr(fmt.Errorf("parse pushed_shipping_fee: %w", err))
	}
	data.LineId = lmWaybill.ResourceId
	data.LmTrackingNo = lmWaybill.ThirdPartyTn
	data.WeightInfo.LmWeight = lfslib_schema.WeightMessage{
		ActualWeight:     lmWaybill.PushedWeightInfo.ActualWeight,
		VolumetricWeight: lmWaybill.PushedWeightInfo.VolumetricWeight,
		ChargeableWeight: lmWaybill.PushedWeightInfo.ChargeableWeight,
	}
	data.SizeInfo.LmSize = *lmSize
	data.FeeDetail.ThreePlPushedFee = lmPushedFee
	data.LocationInfo.Distance = int(lmWaybill.LocationInfo.Distance)

	return nil
}

func transferSkus(skus []*SkuItemV2) ([]lfslib_schema.Sku, error) {
	onErr := func(e error) ([]lfslib_schema.Sku, error) {
		return nil, fmt.Errorf("transfer skus: %w", e)
	}
	res := make([]lfslib_schema.Sku, 0, len(skus))
	for _, sku := range skus {
		weight, err := std_format.ParseFloat64(sku.Weight)
		if err != nil {
			return onErr(err)
		}

		length, err := std_format.ParseFloat64(sku.Length)
		if err != nil {
			return nil, fmt.Errorf("parse length: %w", err)
		}

		width, err := std_format.ParseFloat64(sku.Width)
		if err != nil {
			return nil, fmt.Errorf("parse width: %w", err)
		}

		height, err := std_format.ParseFloat64(sku.Height)
		if err != nil {
			return nil, fmt.Errorf("parse height: %w", err)
		}

		modelId := sku.ModelId
		itemId := sku.ItemId

		res = append(res, lfslib_schema.Sku{
			Weight:     weight,
			Quantity:   int(sku.Quantity),
			Length:     length,
			Width:      width,
			Height:     height,
			ItemSizeId: int(sku.ItemSizeId),
			ModelId:    modelId,
			ItemId:     itemId,
		})
	}

	return res, nil
}

func transferLocation(loc LocationInfo) lfslib_schema.Location {
	return lfslib_schema.Location{
		LocationIds: loc.LocationIds,
		Postcode:    loc.Postcode,
		Longitude:   loc.Longitude,
		Latitude:    loc.Latitude,
	}
}

func transferSize(dimension Dimension) (*lfslib_schema.Size, error) {
	length, err := std_format.ParseFloat64(dimension.Length)
	if err != nil {
		return nil, fmt.Errorf("parse length: %w", err)
	}

	width, err := std_format.ParseFloat64(dimension.Width)
	if err != nil {
		return nil, fmt.Errorf("parse width: %w", err)
	}

	height, err := std_format.ParseFloat64(dimension.Height)
	if err != nil {
		return nil, fmt.Errorf("parse height: %w", err)
	}

	return &lfslib_schema.Size{
		Length: length,
		Height: height,
		Width:  width,
	}, nil
}

func getLmWaybill(waybillList []*NotifyWaybillData) *NotifyWaybillData {
	for _, waybill := range waybillList {
		if waybill.IsLastMile() {
			return waybill
		}
	}
	return nil
}

func getTwsWaybill(waybillList []*NotifyWaybillData) *NotifyWaybillData {
	for _, waybill := range waybillList {
		if waybill.IsTWS() {
			return waybill
		}
	}
	return nil
}
