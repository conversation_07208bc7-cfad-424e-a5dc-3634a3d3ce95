// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/auditclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/chargeclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/dataclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lcosclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lfsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/llsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lnpclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/locationclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/spex_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/wbcclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/allocate_order_data_repo"
	batch_allocate2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast"
	repo3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast_unit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/pickup_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/audit_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/repo"
	allocation2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation/order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/batch_minute_order_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/export_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/forecasting_sub_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	repo2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone/zone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast/forecast_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/outercheck"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/product"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/forecast/forecastservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_log"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/sync_lfs_order"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/schedule_visual/schedule_stat"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/smart_routing_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard"
	repository2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_dashboard/repository"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrservice"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/grpcfacade"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/allocate_volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/audit_log_task"
	batch_allocate3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_allocate_forecast"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/export_masking_panel_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/forecast_chain"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/mask_product_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/masking"
	routing2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/routing"
	schedule_visual3 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/services"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/volumeroutingtask"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/batch_allocate/service/split_batch_chain"
	service2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/forecast/service"
	parcel_type_definition2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/pickup_efficiency_counter"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	whitelist2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocpath"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/available_lh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/business_audit/approval_manager"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/cc_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/ilh_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/lh_capacity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/masking_result_panel"
	schedule_visual2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/schedule_visual"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/select_lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/smartrouting_debug"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volume_counter_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/volumerouting"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/layercache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil/counter"
)

// Injectors from injector.go:

func InitGrpc() (*grpc_api.GrpcAPI, error) {
	lfsApiImpl := lfsclient.NewLfsApiImpl()
	llsApiImpl := llsclient.NewLlsApiImpl()
	laneServiceImpl := lane.NewLaneService(lfsApiImpl, llsApiImpl)
	chargeApiImpl := chargeclient.NewChargeApiImpl()
	lineCheapestShippingFeeFactor := schedule_factor.NewLineCheapestShippingFeeFactor(chargeApiImpl, laneServiceImpl)
	preCalFeeServiceImpl := routing.NewRoutingPreCalFeeServiceImpl(lineCheapestShippingFeeFactor)
	ilhWeightCounterImpl := volume_counter.NewILHWeightCounterImpl()
	routingServiceImpl := routing.NewRoutingServiceImpl(laneServiceImpl, preCalFeeServiceImpl, ilhWeightCounterImpl)
	locationZoneDaoImpl := zone.NewLocationZoneDaoImpl()
	lpsApiImpl := lpsclient.NewLpsApiImpl()
	levelCache, err := layercache.NewLayerCache()
	if err != nil {
		return nil, err
	}
	locationClientImpl := locationclient.NewLocationClientImpl(levelCache)
	addrRepoImpl := address.NewAddrRepoImpl(locationClientImpl)
	zoneRepoImpl := locationzone.NewZoneRepoImpl(locationZoneDaoImpl, lpsApiImpl, addrRepoImpl, levelCache)
	softRuleRepoImpl := ruledata.NewSoftRuleRepoImpl(lpsApiImpl)
	routingRuleRepoImpl := routing.NewRoutingRuleRepo(zoneRepoImpl, lpsApiImpl, laneServiceImpl, llsApiImpl, softRuleRepoImpl)
	routingConfigRepoImpl := ruledata.NewRoutingConfigRepoImpl(lpsApiImpl)
	routingConfigServiceImpl := routing_config.NewRoutingConfigServiceImpl(routingConfigRepoImpl, lpsApiImpl, softRuleRepoImpl, levelCache)
	dgFactor := schedule_factor.NewDgFactor()
	redisCounter := counter.NewRedisCounterImpl()
	volumeCounterImpl := volume_counter.NewVolumeCounterImpl(redisCounter, laneServiceImpl)
	minVolumeFactor := schedule_factor.NewMinVolumeFactor(volumeCounterImpl)
	maxCapacityFactor := schedule_factor.NewMaxCapacityFactor(volumeCounterImpl)
	minWeightFactor := schedule_factor.NewMinWeightFactor(volumeCounterImpl)
	maxWeightFactor := schedule_factor.NewMaxWeightFactor(volumeCounterImpl)
	linePriorityFactor := schedule_factor.NewLinePriorityFactor()
	defaultWeightageFactor := schedule_factor.NewDefaultWeightageFactor()
	defaultPriorityFactor := schedule_factor.NewDefaultPriorityFactor()
	zoneRuleRepoImpl := vrrepo.NewZoneRuleRepoImpl()
	zoneGroupRepoImpl := vrrepo.NewZoneGroupRepoImpl(zoneRuleRepoImpl, routingConfigRepoImpl)
	vrrepoZoneRepoImpl := vrrepo.NewZoneRepoImpl()
	taskRepoImpl := vrrepo.NewTaskRepoImpl()
	serviceImpl := vrservice.NewServiceImpl(zoneGroupRepoImpl, vrrepoZoneRepoImpl, zoneRuleRepoImpl, taskRepoImpl, addrRepoImpl, redisCounter)
	lnpApiImpl := lnpclient.NewLnpApiImpl()
	zoneRuleMgrImpl := volumerouting.NewZoneRuleMgrImpl(serviceImpl, zoneRuleRepoImpl, zoneGroupRepoImpl, vrrepoZoneRepoImpl, taskRepoImpl, lpsApiImpl, routingConfigRepoImpl, lnpApiImpl)
	minVolumeV2 := schedule_factor.NewMinVolumeV2(zoneRuleMgrImpl)
	maxVolumeV2 := schedule_factor.NewMaxVolumeV2(zoneRuleMgrImpl)
	ilhParcelMinVolumeFactor := schedule_factor.NewILHParcelMinVolumeFactor(volumeCounterImpl)
	ilhParcelMaxCapacityFactor := schedule_factor.NewILHParcelMaxCapacityFactor(volumeCounterImpl)
	combinationPriorityFactor := schedule_factor.NewCombinationPriorityFactor()
	factorSet := schedule_factor.NewFactorSet(dgFactor, minVolumeFactor, maxCapacityFactor, minWeightFactor, maxWeightFactor, linePriorityFactor, lineCheapestShippingFeeFactor, defaultWeightageFactor, defaultPriorityFactor, minVolumeV2, maxVolumeV2, ilhParcelMinVolumeFactor, ilhParcelMaxCapacityFactor, combinationPriorityFactor)
	routingLogRepoImpl := routing_log.NewRoutingLogRepoImpl()
	routingLogServiceImpl := routing_log.NewRoutingLogServiceImpl(volumeCounterImpl, laneServiceImpl, lpsApiImpl, routingLogRepoImpl)
	ccRoutingRuleRepoImpl := cc_routing_rule.NewCCRoutingRuleRepoImpl()
	ccApiImpl := ccclient.NewCCApiImpl()
	ccRoutingServiceImpl := cc_routing.NewCCRoutingServiceImpl(ccRoutingRuleRepoImpl, ccApiImpl)
	availableLHRepoImpl := repo.NewAvailableLHRepoImpl()
	availableLHServiceImpl := available_lh.NewAvailableLHServiceImpl(availableLHRepoImpl)
	lhCapacityRepoImpl := repo2.NewLHCapacityRepoImpl()
	lhCapacityServiceImpl := lh_capacity.NewLHCapacityServiceImpl(lhCapacityRepoImpl, laneServiceImpl)
	ilhRoutingServiceImpl := routing.NewILHRoutingServiceImpl(ilhWeightCounterImpl)
	smartRoutingServiceImpl := select_lane.NewSmartRoutingServiceImpl(routingServiceImpl, routingRuleRepoImpl, lfsApiImpl, routingConfigServiceImpl, addrRepoImpl, factorSet, routingLogServiceImpl, laneServiceImpl, ccRoutingServiceImpl, availableLHServiceImpl, lhCapacityServiceImpl, ilhRoutingServiceImpl)
	volumeCounterService := volume_counter_service.NewVolumeCounterService(zoneRuleRepoImpl, zoneGroupRepoImpl, volumeCounterImpl, vrrepoZoneRepoImpl, routingConfigServiceImpl, zoneRepoImpl, laneServiceImpl)
	maskVolumeCounterImpl := volumecounter.NewMaskVolumeCounterImpl()
	shopWhitelistRepoImpl := whitelist.NewShopWhitelistRepoImpl()
	shopWhitelistServiceImpl := whitelist2.NewShopWhitelistServiceImpl(shopWhitelistRepoImpl)
	softRuleService := allocation.NewSoftRuleService(maskVolumeCounterImpl, chargeApiImpl, shopWhitelistServiceImpl)
	iMaskRuleVolumeRepo := rulevolume.NewMaskRuleVolumeRepoImpl(lpsApiImpl)
	checkVolumeFinderImpl := rulevolume2.NewCheckVolumeFinderImpl(iMaskRuleVolumeRepo)
	auditApiImpl := auditclient.NewAuditApiImpl()
	businessAuditRepoImpl := business_audit.NewBusinessAuditRepoImpl()
	approvalExecutorImpl := approval_manager.NewApprovalExecutorImpl(auditApiImpl, businessAuditRepoImpl)
	maskRuleVolumeServiceImpl := rulevolume2.NewMaskRuleVolumeServiceImpl(addrRepoImpl, lpsApiImpl, iMaskRuleVolumeRepo, levelCache, checkVolumeFinderImpl, approvalExecutorImpl, businessAuditRepoImpl)
	allocationRuleImpl := rule.NewAllocationRuleImpl(lpsApiImpl)
	allocationConfigImpl := config.NewAllocationConfigImpl(lpsApiImpl)
	maskConfigRepoImpl := config.NewMaskConfigRepo(allocationRuleImpl, allocationConfigImpl)
	maskRuleConfRepo := rule.NewMaskRuleConfRepo()
	priorityRepoImpl := productpriority.NewPriorityRepoImpl(lpsApiImpl)
	pickupPriorityRepoImpl := pickup_priority.NewPickupPriorityRepoImpl()
	maskRuleRepoImpl := rule.NewRuleRepo(iMaskRuleVolumeRepo, maskRuleVolumeServiceImpl, maskRuleConfRepo, lpsApiImpl, allocationRuleImpl, priorityRepoImpl, approvalExecutorImpl, businessAuditRepoImpl, pickupPriorityRepoImpl)
	priorityBusinessImpl := productpriority.NewPriorityBusinessImpl(priorityRepoImpl, lpsApiImpl)
	allOuterCheckServiceImpl := outercheck.NewAllOuterCheckServiceImpl(priorityBusinessImpl)
	client, err := redisutil.Client()
	if err != nil {
		return nil, err
	}
	maskingScheduleVisualStat := schedule_stat.NewMaskingScheduleVisualStat(client)
	maskingScheduleVisualStatV2 := schedule_stat.NewMaskingScheduleVisualStatV2(client)
	maskingForecastScheduleVisualStat := schedule_stat.NewMaskingForecastScheduleVisualStat(client)
	scheduleVisualSet := schedule_stat.NewScheduleVisualSet(maskingScheduleVisualStat, maskingScheduleVisualStatV2, maskingForecastScheduleVisualStat)
	scheduleCountStat := schedule_visual.NewScheduleCountStat(scheduleVisualSet)
	batchAllocateOrderRepoImpl := order.NewBatchAllocateOrderRepo()
	greyServiceImpl := service.NewGreyServiceImpl()
	parcelTypeDefinitionRepoImpl := parcel_type_definition.NewParcelTypeDefinitionRepoImpl()
	parcelTypeDefinitionServiceImpl := parcel_type_definition2.NewParcelTypeDefinitionServiceImpl(parcelTypeDefinitionRepoImpl, lpsApiImpl)
	pickupEffCounterImpl := pickup_efficiency_counter.NewPickupEffCounterImpl()
	spexServiceImpl := spex_service.NewSpexServiceImpl()
	lcosApiImpl := lcosclient.NewLcosApiImpl()
	allocationServiceImpl := allocation.NewAllocationService(softRuleService, maskRuleVolumeServiceImpl, maskConfigRepoImpl, maskRuleRepoImpl, allOuterCheckServiceImpl, maskVolumeCounterImpl, volumeCounterImpl, scheduleCountStat, iMaskRuleVolumeRepo, batchAllocateOrderRepoImpl, greyServiceImpl, parcelTypeDefinitionServiceImpl, pickupEffCounterImpl, spexServiceImpl, lcosApiImpl, lpsApiImpl, llsApiImpl, addrRepoImpl)
	volumeRoutingServer := grpc_api.NewVolumeRoutingServer(zoneRuleMgrImpl, volumeCounterImpl, laneServiceImpl, volumeCounterService, allocationServiceImpl, lfsApiImpl, ilhWeightCounterImpl, lhCapacityServiceImpl)
	smartRoutingDebug := smartrouting_debug.NewSmartRoutingDebug()
	smartRoutingDebugServer := grpc_api.NewSmartRoutingDebugServer(smartRoutingDebug)
	grpcAPI := &grpc_api.GrpcAPI{
		SmartRoutingServer:      smartRoutingServiceImpl,
		VolumeRoutingService:    volumeRoutingServer,
		MaskAllocateServer:      allocationServiceImpl,
		SmartRoutingDebugServer: smartRoutingDebugServer,
	}
	return grpcAPI, nil
}

func initSaturnService() (*services.SaturnService, error) {
	zoneRuleRepoImpl := vrrepo.NewZoneRuleRepoImpl()
	lpsApiImpl := lpsclient.NewLpsApiImpl()
	routingConfigRepoImpl := ruledata.NewRoutingConfigRepoImpl(lpsApiImpl)
	zoneGroupRepoImpl := vrrepo.NewZoneGroupRepoImpl(zoneRuleRepoImpl, routingConfigRepoImpl)
	zoneRepoImpl := vrrepo.NewZoneRepoImpl()
	taskRepoImpl := vrrepo.NewTaskRepoImpl()
	levelCache, err := layercache.NewLayerCache()
	if err != nil {
		return nil, err
	}
	locationClientImpl := locationclient.NewLocationClientImpl(levelCache)
	addrRepoImpl := address.NewAddrRepoImpl(locationClientImpl)
	redisCounter := counter.NewRedisCounterImpl()
	serviceImpl := vrservice.NewServiceImpl(zoneGroupRepoImpl, zoneRepoImpl, zoneRuleRepoImpl, taskRepoImpl, addrRepoImpl, redisCounter)
	zoneManagerImpl := volumerouting.NewZoneManagerImpl(serviceImpl, zoneRepoImpl, taskRepoImpl, zoneRuleRepoImpl)
	zoneImportTask := volumeroutingtask.NewZoneImportTask(zoneManagerImpl)
	zoneExportTask := volumeroutingtask.NewZoneExportTask(zoneManagerImpl)
	lnpApiImpl := lnpclient.NewLnpApiImpl()
	zoneRuleMgrImpl := volumerouting.NewZoneRuleMgrImpl(serviceImpl, zoneRuleRepoImpl, zoneGroupRepoImpl, zoneRepoImpl, taskRepoImpl, lpsApiImpl, routingConfigRepoImpl, lnpApiImpl)
	ruleLimitImportTask := volumeroutingtask.NewRuleLimitImportTask(zoneRuleMgrImpl)
	ruleEffectiveTask := volumeroutingtask.NewRuleEffectiveTask(zoneRuleMgrImpl)
	dataApi := dataclient.NewDataApi()
	exportTaskRepoImpl := export_task.NewExportTaskRepoImpl()
	prodRepoImpl := product.NewProdRepoImpl()
	maskingConvertorMap := dataclient.NewMaskingConvertorMap()
	keyDataImpl := masking_result_panel.NewKeyDataImpl(dataApi, exportTaskRepoImpl, prodRepoImpl, maskingConvertorMap, lpsApiImpl)
	maskingDataExportTask := export_masking_panel_data.NewMaskingDataExportTask(keyDataImpl)
	maskVolumeCounterImpl := volumecounter.NewMaskVolumeCounterImpl()
	allocateCheckBatchVolumeCounter := allocate_volume_counter.NewAllocateCheckBatchVolumeCounter(maskVolumeCounterImpl)
	lfsApiImpl := lfsclient.NewLfsApiImpl()
	llsApiImpl := llsclient.NewLlsApiImpl()
	laneServiceImpl := lane.NewLaneService(lfsApiImpl, llsApiImpl)
	volumeCounterImpl := volume_counter.NewVolumeCounterImpl(redisCounter, laneServiceImpl)
	routingLogRepoImpl := routing_log.NewRoutingLogRepoImpl()
	routingLogServiceImpl := routing_log.NewRoutingLogServiceImpl(volumeCounterImpl, laneServiceImpl, lpsApiImpl, routingLogRepoImpl)
	reportOrderCount := routing2.NewReportOrderCount(routingLogServiceImpl)
	forecastrepoServiceImpl := forecastrepo.NewServiceImpl(zoneRuleRepoImpl)
	orderSyncRepoImpl := sync_lfs_order.NewOrderSyncRepoImpl()
	locationZoneDaoImpl := zone.NewLocationZoneDaoImpl()
	locationzoneZoneRepoImpl := locationzone.NewZoneRepoImpl(locationZoneDaoImpl, lpsApiImpl, addrRepoImpl, levelCache)
	softRuleRepoImpl := ruledata.NewSoftRuleRepoImpl(lpsApiImpl)
	routingRuleRepoImpl := routing.NewRoutingRuleRepo(locationzoneZoneRepoImpl, lpsApiImpl, laneServiceImpl, llsApiImpl, softRuleRepoImpl)
	chargeApiImpl := chargeclient.NewChargeApiImpl()
	lineCheapestShippingFeeFactor := schedule_factor.NewLineCheapestShippingFeeFactor(chargeApiImpl, laneServiceImpl)
	preCalFeeServiceImpl := routing.NewRoutingPreCalFeeServiceImpl(lineCheapestShippingFeeFactor)
	ilhWeightCounterImpl := volume_counter.NewILHWeightCounterImpl()
	routingServiceImpl := routing.NewRoutingServiceImpl(laneServiceImpl, preCalFeeServiceImpl, ilhWeightCounterImpl)
	routingConfigServiceImpl := routing_config.NewRoutingConfigServiceImpl(routingConfigRepoImpl, lpsApiImpl, softRuleRepoImpl, levelCache)
	ccRoutingRuleRepoImpl := cc_routing_rule.NewCCRoutingRuleRepoImpl()
	ccApiImpl := ccclient.NewCCApiImpl()
	ccRoutingServiceImpl := cc_routing.NewCCRoutingServiceImpl(ccRoutingRuleRepoImpl, ccApiImpl)
	wbcApiImpl := wbcclient.NewWbcApi()
	forecastTaskServiceImpl := forecastservice.NewForecastTaskServiceImpl(routingConfigServiceImpl, forecastrepoServiceImpl, lpsApiImpl, ccRoutingServiceImpl, lineCheapestShippingFeeFactor, routingLogServiceImpl, dataApi, wbcApiImpl, chargeApiImpl)
	ilhForecastTaskRepoImpl := forecastrepo.NewILHForecastTaskRepoImpl()
	availableLHRepoImpl := repo.NewAvailableLHRepoImpl()
	lhCapacityRepoImpl := repo2.NewLHCapacityRepoImpl()
	ilhForecastTaskServiceImpl := ilh_forecast_task.NewILHForecastTaskServiceImpl(ilhForecastTaskRepoImpl, availableLHRepoImpl, lhCapacityRepoImpl, laneServiceImpl, lpsApiImpl)
	availableLHServiceImpl := available_lh.NewAvailableLHServiceImpl(availableLHRepoImpl)
	lhCapacityServiceImpl := lh_capacity.NewLHCapacityServiceImpl(lhCapacityRepoImpl, laneServiceImpl)
	smartRoutingForecastServiceImpl := smart_routing_forecast.NewSmartRoutingForecastServiceImpl(forecastrepoServiceImpl, orderSyncRepoImpl, routingRuleRepoImpl, routingServiceImpl, laneServiceImpl, locationzoneZoneRepoImpl, lfsApiImpl, lpsApiImpl, forecastTaskServiceImpl, softRuleRepoImpl, zoneRuleRepoImpl, zoneGroupRepoImpl, zoneRepoImpl, ilhForecastTaskServiceImpl, availableLHServiceImpl, lhCapacityServiceImpl)
	smartRoutingForecastTask := routing2.NewSmartRoutingForecastTask(smartRoutingForecastServiceImpl, forecastrepoServiceImpl)
	ilhForecastTask := routing2.NewILHForecastTask(ilhForecastTaskServiceImpl, routingServiceImpl, smartRoutingForecastServiceImpl)
	lfsHardCriteriaCreatedBySystem := routing2.NewLfsHardCriteriaCreatedBySystem(forecastTaskServiceImpl)
	lfsHardCriteriaV2 := routing2.NewLfsHardCriteriaV2(forecastTaskServiceImpl)
	lfsHardCriteriaCheck := routing2.NewLfsHardCriteriaCheck(forecastTaskServiceImpl)
	iMaskRuleVolumeRepo := rulevolume.NewMaskRuleVolumeRepoImpl(lpsApiImpl)
	checkVolumeFinderImpl := rulevolume2.NewCheckVolumeFinderImpl(iMaskRuleVolumeRepo)
	auditApiImpl := auditclient.NewAuditApiImpl()
	businessAuditRepoImpl := business_audit.NewBusinessAuditRepoImpl()
	approvalExecutorImpl := approval_manager.NewApprovalExecutorImpl(auditApiImpl, businessAuditRepoImpl)
	maskRuleVolumeServiceImpl := rulevolume2.NewMaskRuleVolumeServiceImpl(addrRepoImpl, lpsApiImpl, iMaskRuleVolumeRepo, levelCache, checkVolumeFinderImpl, approvalExecutorImpl, businessAuditRepoImpl)
	maskRuleConfRepo := rule.NewMaskRuleConfRepo()
	allocationRuleImpl := rule.NewAllocationRuleImpl(lpsApiImpl)
	priorityRepoImpl := productpriority.NewPriorityRepoImpl(lpsApiImpl)
	pickupPriorityRepoImpl := pickup_priority.NewPickupPriorityRepoImpl()
	maskRuleRepoImpl := rule.NewRuleRepo(iMaskRuleVolumeRepo, maskRuleVolumeServiceImpl, maskRuleConfRepo, lpsApiImpl, allocationRuleImpl, priorityRepoImpl, approvalExecutorImpl, businessAuditRepoImpl, pickupPriorityRepoImpl)
	scheduleRule := routing2.NewScheduleRule(routingRuleRepoImpl, maskRuleRepoImpl, lhCapacityServiceImpl, availableLHServiceImpl)
	updateZoneVolume := masking.NewUpdateZoneVolume(iMaskRuleVolumeRepo)
	updateRouteVolume := masking.NewUpdateRouteVolume(iMaskRuleVolumeRepo)
	allocateOrderDataRepoImpl := allocate_order_data_repo.NewAllocateOrderDataRepoImpl(addrRepoImpl)
	allocateDateRankRepoImpl := repo3.NewAllocateDateRankRepo()
	allocateHistoricalRankRepoImpl := repo3.NewAllocateHistoricalRankRepo()
	allocateForecastRankRepoImpl := repo3.NewAllocateForecastRankImpl()
	allocateForecastTaskConfigRepoImpl := repo3.NewAllocateForecastTaskConfigRepoImpl()
	allocateShippingFeeImpl := repo3.NewAllocateShippingFeeServiceImpl(chargeApiImpl, lpsApiImpl)
	priorityBusinessImpl := productpriority.NewPriorityBusinessImpl(priorityRepoImpl, lpsApiImpl)
	forecastingSubTaskRepoImpl := forecasting_sub_task.NewForecastingSubTaskRepoImpl()
	forecastingSubTaskServiceImpl := forecasting_sub_task.NewForecastingSubTaskServiceImpl(forecastingSubTaskRepoImpl)
	allocateForecastTaskConfigServiceImpl := service2.NewAllocateForecastTaskConfigServiceImpl(allocateForecastTaskConfigRepoImpl, lpsApiImpl, allocateShippingFeeImpl, iMaskRuleVolumeRepo, allocationRuleImpl, maskRuleVolumeServiceImpl, allocateForecastRankRepoImpl, priorityBusinessImpl, forecastingSubTaskServiceImpl, forecastingSubTaskRepoImpl)
	allocateRankServiceImpl := service2.NewAllocateRankServiceImpl(lpsApiImpl, allocateDateRankRepoImpl, allocateHistoricalRankRepoImpl, allocateForecastRankRepoImpl, allocateForecastTaskConfigServiceImpl)
	allocateStoreConsumer := masking.NewAllocateStoreConsumer(allocateOrderDataRepoImpl, allocateRankServiceImpl)
	loadForecastVolumeConfig := masking.NewLoadForecastVolumeConfig(iMaskRuleVolumeRepo, allocateForecastTaskConfigRepoImpl, maskRuleVolumeServiceImpl)
	allocationConfigImpl := config.NewAllocationConfigImpl(lpsApiImpl)
	allOuterCheckServiceImpl := outercheck.NewAllOuterCheckServiceImpl(priorityBusinessImpl)
	forecastVolumeRepo := forecast_volume.NewForecastVolumeRepo()
	forecastLocationVolumeService := forecast_volume.NewForecastLocationVolumeServiceImpl(forecastVolumeRepo)
	shopWhitelistRepoImpl := whitelist.NewShopWhitelistRepoImpl()
	shopWhitelistServiceImpl := whitelist2.NewShopWhitelistServiceImpl(shopWhitelistRepoImpl)
	softRuleService := allocation.NewSoftRuleService(maskVolumeCounterImpl, chargeApiImpl, shopWhitelistServiceImpl)
	maskConfigRepoImpl := config.NewMaskConfigRepo(allocationRuleImpl, allocationConfigImpl)
	client, err := redisutil.Client()
	if err != nil {
		return nil, err
	}
	maskingScheduleVisualStat := schedule_stat.NewMaskingScheduleVisualStat(client)
	maskingScheduleVisualStatV2 := schedule_stat.NewMaskingScheduleVisualStatV2(client)
	maskingForecastScheduleVisualStat := schedule_stat.NewMaskingForecastScheduleVisualStat(client)
	scheduleVisualSet := schedule_stat.NewScheduleVisualSet(maskingScheduleVisualStat, maskingScheduleVisualStatV2, maskingForecastScheduleVisualStat)
	scheduleCountStat := schedule_visual.NewScheduleCountStat(scheduleVisualSet)
	batchAllocateOrderRepoImpl := order.NewBatchAllocateOrderRepo()
	greyServiceImpl := service.NewGreyServiceImpl()
	parcelTypeDefinitionRepoImpl := parcel_type_definition.NewParcelTypeDefinitionRepoImpl()
	parcelTypeDefinitionServiceImpl := parcel_type_definition2.NewParcelTypeDefinitionServiceImpl(parcelTypeDefinitionRepoImpl, lpsApiImpl)
	pickupEffCounterImpl := pickup_efficiency_counter.NewPickupEffCounterImpl()
	spexServiceImpl := spex_service.NewSpexServiceImpl()
	lcosApiImpl := lcosclient.NewLcosApiImpl()
	allocationServiceImpl := allocation.NewAllocationService(softRuleService, maskRuleVolumeServiceImpl, maskConfigRepoImpl, maskRuleRepoImpl, allOuterCheckServiceImpl, maskVolumeCounterImpl, volumeCounterImpl, scheduleCountStat, iMaskRuleVolumeRepo, batchAllocateOrderRepoImpl, greyServiceImpl, parcelTypeDefinitionServiceImpl, pickupEffCounterImpl, spexServiceImpl, lcosApiImpl, lpsApiImpl, llsApiImpl, addrRepoImpl)
	allocateForecastServiceImpl := masking_forecast.NewAllocateForecastTaskServiceImpl(allocationConfigImpl, allocateOrderDataRepoImpl, allocateRankServiceImpl, allOuterCheckServiceImpl, maskRuleVolumeServiceImpl, forecastLocationVolumeService, allocationServiceImpl, lpsApiImpl, addrRepoImpl, client, scheduleCountStat, chargeApiImpl)
	jobChainServiceImpl := forecast_chain.NewJobChainServiceImpl(forecastingSubTaskServiceImpl, allocateForecastTaskConfigServiceImpl, allocateRankServiceImpl, lpsApiImpl, allocationConfigImpl, client, addrRepoImpl, allocateForecastServiceImpl, allocateOrderDataRepoImpl, scheduleCountStat)
	maskingForecast := masking.NewMaskingForecast(allocateForecastTaskConfigServiceImpl, allocateForecastTaskConfigRepoImpl, allocateForecastServiceImpl, forecastingSubTaskRepoImpl, jobChainServiceImpl)
	maskProductPriorityJob := mask_product_priority.NewMaskCronJob()
	auditLogRepoImpl := audit_log.NewAuditLogRepoImpl()
	auditLogServiceImpl := audit_log.NewAuditLogServiceImpl(auditLogRepoImpl)
	auditLogTaskServer := audit_log_task.NewAuditLogTaskServer(auditLogServiceImpl)
	scheduleVisualRepo := repository.NewScheduleVisualRepo()
	scheduleVisualTaskService := schedule_visual2.NewScheduleVisualTaskService(scheduleVisualRepo, client)
	syncScheduleCountTask := schedule_visual3.NewSyncScheduleCountTask(scheduleVisualTaskService)
	clearScheduleCountTask := schedule_visual3.NewClearScheduleCountTask(scheduleVisualTaskService)
	deleteMaskingSubTaskImpl := masking.NewDeleteMaskingSubTaskImpl(forecastingSubTaskServiceImpl)
	checkMaskingProcessTaskImpl := masking.NewCheckMaskingProcessTaskImpl(forecastingSubTaskServiceImpl, allocateForecastTaskConfigRepoImpl)
	getForecastTotalCountImpl := masking.NewGetForecastTotalCountImpl(allocateForecastTaskConfigRepoImpl, allocateOrderDataRepoImpl, forecastingSubTaskServiceImpl)
	updateRouteAndZoneVolume := masking.NewUpdateRouteAndZoneVolume(iMaskRuleVolumeRepo)
	allocateStoreHbaseConsumer := masking.NewAllocateStoreHbaseConsumer(allocateOrderDataRepoImpl)
	scheduleCountStatTask := masking.NewScheduleCountStatTask(scheduleCountStat)
	localForecastServiceImpl := smart_routing_forecast.NewLocalForecastServiceImpl(routingRuleRepoImpl, routingServiceImpl, locationzoneZoneRepoImpl, laneServiceImpl, zoneGroupRepoImpl, zoneRepoImpl)
	localSpxForecasting := routing2.NewLocalSpxForecasting(localForecastServiceImpl, forecastrepoServiceImpl)
	maskProductOrderNumRepoImpl := repository2.NewMaskProductOrderNumRepoImpl()
	maskingVolumeServiceImpl := volume_dashboard.NewMaskingVolumeServiceImpl(volumeCounterImpl, maskProductOrderNumRepoImpl, lpsApiImpl, exportTaskRepoImpl, maskRuleVolumeServiceImpl)
	reportMaskingVolumeTask := masking.NewReportMaskingVolumeTask(maskingVolumeServiceImpl)
	mergeMaskingVolumeTask := masking.NewMergeMaskingVolumeTask(maskingVolumeServiceImpl)
	clearMaskingVolumeTask := masking.NewClearMaskingVolumeTask(maskingVolumeServiceImpl)
	routingVolumeRepoImpl := repository2.NewRoutingVolumeRepoImpl()
	volumeChangeServiceImpl := volume_dashboard.NewVolumeChangeServiceImpl(routingConfigServiceImpl, softRuleRepoImpl)
	routingVolumeServiceImpl := volume_dashboard.NewRoutingVolumeServiceImpl(laneServiceImpl, lpsApiImpl, volumeCounterImpl, routingVolumeRepoImpl, exportTaskRepoImpl, volumeChangeServiceImpl)
	reportRoutingVolumeTask := routing2.NewReportRoutingVolumeTask(routingVolumeServiceImpl)
	mergeRoutingVolumeTask := routing2.NewMergeRoutingVolumeTask(routingVolumeServiceImpl)
	clearRoutingVolumeTask := routing2.NewClearRoutingVolumeTask(routingVolumeServiceImpl)
	batchAllocateForecastRepoImpl := repo3.NewBatchAllocateForecastRepoImpl()
	orderCollectorImpl := model.NewOrderCollectorImpl(addrRepoImpl)
	splittingRuleImpl := model.NewSplittingRuleImpl(orderCollectorImpl)
	batchAllocateForecastVolumeImpl := rulevolume.NewBatchAllocateForecastVolumeImpl()
	batchUnitTargetResultRepoImpl := repo3.NewBatchUnitTargetResultRepoImap()
	batchUnitFeeResultRepoImpl := repo3.NewBatchUnitFeeResultRepoImpl()
	batchAllocateForecastUnitResultRepoImpl := repo3.NewBatchAllocateForecastUnitResultRepoImpl()
	batchForecastUnitServiceImpl := forecast_unit.NewBatchForecastUnitServiceImpl()
	batchMinuteOrderConfRepoImpl := batch_minute_order_conf.NewBatchMinuteOrderConfRepoImpl()
	batchMinuteOrderConfServiceImpl := batch_allocate.NewBatchMinuteOrderConfServiceImpl(batchMinuteOrderConfRepoImpl)
	startBatchForecastUnitImpl := masking.NewStartBatchForecastUnitImpl(batchAllocateForecastRepoImpl, splittingRuleImpl, batchAllocateForecastVolumeImpl, lpsApiImpl, chargeApiImpl, allocateForecastTaskConfigRepoImpl, batchUnitTargetResultRepoImpl, batchUnitFeeResultRepoImpl, batchAllocateForecastUnitResultRepoImpl, forecastLocationVolumeService, batchForecastUnitServiceImpl, batchMinuteOrderConfServiceImpl, shopWhitelistServiceImpl)
	createBASubTask := batch_allocate_forecast.NewCreateBASubTask(allocateForecastTaskConfigServiceImpl)
	batchAllocateForecastUnitRepoImpl := repo3.NewBatchAllocateForecastUnitRepoImpl()
	batchAllocateSubTaskRepoImpl := repo3.NewBatchAllocateSubTaskRepoImpl()
	batchAllocateForecastServiceImpl := forecast.NewBatchAllocateForecastServiceImpl(allocateForecastTaskConfigRepoImpl, batchAllocateForecastUnitRepoImpl, batchAllocateSubTaskRepoImpl, batchUnitFeeResultRepoImpl, batchAllocateForecastUnitResultRepoImpl)
	updateBatchAllocateForecastTaskImpl := masking.NewUpdateBatchAllocateForecastTaskImpl(batchAllocateForecastServiceImpl)
	allocateHistoryOutlineImpl := repo3.NewAllocateHistoryOutlineImpl()
	allocateHistoryOutLine := masking.NewAllocateHistory(allocateHistoryOutlineImpl)
	batchAllocateSubTaskOutlineRepoImpl := repo3.NewBatchAllocateSubTaskOutlineRepoImpl()
	batchAllocateForecastImpl := service2.NewBatchAllocateForecastImpl(allocateForecastTaskConfigRepoImpl, addrRepoImpl, lpsApiImpl, batchAllocateForecastVolumeImpl, batchAllocateForecastRepoImpl, allocateDateRankRepoImpl, allocateHistoryOutlineImpl, batchUnitFeeResultRepoImpl, batchAllocateForecastUnitResultRepoImpl, batchAllocateSubTaskOutlineRepoImpl, batchAllocateOrderRepoImpl, pickupEffCounterImpl)
	parseBatchVolume := masking.NewParseBatchVolume(allocateForecastTaskConfigRepoImpl, batchAllocateForecastImpl, batchAllocateForecastVolumeImpl)
	allocateScheduleVisualTask := masking.NewAllocateScheduleVisualTask(scheduleCountStat)
	baForecastToolProgressImpl := batch_allocate_forecast.NewBAForecastToolProgressImpl(allocateForecastTaskConfigRepoImpl, batchAllocateForecastRepoImpl, batchUnitFeeResultRepoImpl)
	reportMaskingOrderCount := masking.NewReportMaskingOrderCount(lpsApiImpl)
	checkoutFulfillmentProductCounter := allocate_volume_counter.NewCheckoutFulfillmentProductCounter(allocationServiceImpl)
	deductVolumeCounter := allocate_volume_counter.NewDeductVolumeCounter(allocationServiceImpl)
	splitBatchRepoImpl := batch_allocate2.NewSplitBatchRepoImpl()
	executorImpl := split_batch_chain.NewExecutorImpl(splitBatchRepoImpl, batchAllocateOrderRepoImpl, maskRuleRepoImpl)
	splitBatchServerImpl := service.NewSplitBatchServerImpl(executorImpl)
	splitBatchAllocateOrdersTask := masking.NewSplitBatchAllocateOrdersTask(splitBatchServerImpl, lpsApiImpl)
	batchAllocateServiceImpl := allocation2.NewBatchAllocateService(batchAllocateOrderRepoImpl, maskRuleVolumeServiceImpl, maskRuleRepoImpl, maskVolumeCounterImpl, splitBatchRepoImpl, batchMinuteOrderConfServiceImpl, lpsApiImpl, levelCache, pickupEffCounterImpl, shopWhitelistServiceImpl)
	batchAllocateTask := batch_allocate3.NewBatchAllocateTask(batchAllocateServiceImpl)
	abnormalBatchAllocateTask := batch_allocate3.NewAbnormalBatchAllocateTask(batchAllocateServiceImpl)
	batchAbnormalInspectionTask := batch_allocate3.NewBatchAbnormalInspectionTask(batchAllocateServiceImpl)
	pushOrderResultTask := batch_allocate3.NewPushOrderResultTask(batchAllocateServiceImpl)
	updateOrderResultTask := batch_allocate3.NewUpdateOrderResultTask()
	batchAllocateHoldOrderConsumer := batch_allocate3.NewBatchAllocateHoldOrderConsumer(batchAllocateOrderRepoImpl)
	clearOrderAndResultTask := batch_allocate3.NewClearOrderAndResultTask(batchAllocateOrderRepoImpl)
	updateAllocationPathTask := batch_allocate3.NewUpdateAllocationPathTask()
	allocationPathSrvImpl := allocpath.NewAllocationPathSrvImpl(dataApi, lpsApiImpl, prodRepoImpl, maskRuleVolumeServiceImpl)
	makeUpAsyncAllocationLog := batch_allocate3.NewMakeUpAsyncAllocationLog(allocationPathSrvImpl)
	batchAllocateMonitor := batch_allocate3.NewBatchAllocateMonitor(splitBatchRepoImpl, batchAllocateOrderRepoImpl)
	saturnService := &services.SaturnService{
		ZoneImportTask:                      zoneImportTask,
		ZoneExportTask:                      zoneExportTask,
		RuleLimitImportTask:                 ruleLimitImportTask,
		RuleEffectiveTask:                   ruleEffectiveTask,
		MaskingDataExportTask:               maskingDataExportTask,
		AllocateCheckBatchVolumeCounter:     allocateCheckBatchVolumeCounter,
		ReportOrderCount:                    reportOrderCount,
		SmartRoutingForecastTask:            smartRoutingForecastTask,
		ILHForecastTask:                     ilhForecastTask,
		LfsHardCriteriaCreatedBySystem:      lfsHardCriteriaCreatedBySystem,
		LfsHardCriteriaV2:                   lfsHardCriteriaV2,
		LfsHardCriteriaCheck:                lfsHardCriteriaCheck,
		ScheduleRule:                        scheduleRule,
		UpdateZoneVolume:                    updateZoneVolume,
		UpdateRouteVolume:                   updateRouteVolume,
		AllocateStoreConsumer:               allocateStoreConsumer,
		LoadForecastVolumeConfig:            loadForecastVolumeConfig,
		MaskingForecast:                     maskingForecast,
		MaskProductPriorityCronJob:          maskProductPriorityJob,
		AuditLogTaskServer:                  auditLogTaskServer,
		SyncScheduleCountTask:               syncScheduleCountTask,
		ClearScheduleCountTask:              clearScheduleCountTask,
		DeleteMaskingSubTaskImpl:            deleteMaskingSubTaskImpl,
		CheckMaskingProcessTaskImpl:         checkMaskingProcessTaskImpl,
		GetForecastTotalCountImpl:           getForecastTotalCountImpl,
		UpdateRouteAndZoneVolume:            updateRouteAndZoneVolume,
		AllocateStoreHbaseConsumer:          allocateStoreHbaseConsumer,
		ScheduleCountStatTask:               scheduleCountStatTask,
		LocalSpxForecasting:                 localSpxForecasting,
		ReportMaskingVolumeTask:             reportMaskingVolumeTask,
		MergeMaskingVolumeTask:              mergeMaskingVolumeTask,
		ClearMaskingVolumeTask:              clearMaskingVolumeTask,
		ReportRoutingVolumeTask:             reportRoutingVolumeTask,
		MergeRoutingVolumeTask:              mergeRoutingVolumeTask,
		ClearRoutingVolumeTask:              clearRoutingVolumeTask,
		StartBatchForecastUnitImpl:          startBatchForecastUnitImpl,
		CreateBASubTask:                     createBASubTask,
		UpdateBatchAllocateForecastTaskImpl: updateBatchAllocateForecastTaskImpl,
		AllocateHistoryOutLine:              allocateHistoryOutLine,
		AnalyzeBatchVolume:                  parseBatchVolume,
		AllocateScheduleVisualTask:          allocateScheduleVisualTask,
		BAForecastToolProgressImpl:          baForecastToolProgressImpl,
		ReportMaskingOrderCount:             reportMaskingOrderCount,
		CheckoutFulfillmentProductCounter:   checkoutFulfillmentProductCounter,
		DeductVolumeCounter:                 deductVolumeCounter,
		SplitBatchAllocateOrdersTask:        splitBatchAllocateOrdersTask,
		BatchAllocateTask:                   batchAllocateTask,
		AbnormalBatchAllocateTask:           abnormalBatchAllocateTask,
		BatchAbnormalInspectionTask:         batchAbnormalInspectionTask,
		PushOrderResultTask:                 pushOrderResultTask,
		UpdateOrderResultTask:               updateOrderResultTask,
		BatchAllocateHoldOrderConsumer:      batchAllocateHoldOrderConsumer,
		ClearOrderAndResult:                 clearOrderAndResultTask,
		UpdateAllocationPathTask:            updateAllocationPathTask,
		MakeUpAsyncAllocationLog:            makeUpAsyncAllocationLog,
		BatchAllocateMonitor:                batchAllocateMonitor,
	}
	return saturnService, nil
}

// injector.go:

func InitSaturnService() *services.SaturnService {
	saturnService, _ := initSaturnService()
	services.SetService(saturnService)

	return services.GetService()
}
