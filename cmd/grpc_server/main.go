package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/chassis/config"
	"git.garena.com/shopee/bg-logistics/go/chassis/core/invocation"
	"git.garena.com/shopee/bg-logistics/go/chassis/handler"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache/lcregistry"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/configutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/dbutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/traceutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/middleware"
	chassis_middleware "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/middleware/grpc_logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/middleware/traffic_playback"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/kafkahelper"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"

	"git.garena.com/shopee/bg-logistics/go/chassis"
)

func main() {

	_ = os.Setenv("SSC_ENV", strings.ToLower(envvar.GetEnv()))
	if strings.Contains(strings.ToLower(envvar.GetModuleName()), "livetest") {
		err := os.Setenv("SSC_ENV", "livetest")
		if err != nil {
			log.Fatalf("write chassis envvar error: %v", err)
		}

		err = os.Setenv("APOLLO_CLUSTER", envvar.GetCIDLower()+"_livetest")
		if err != nil {
			fmt.Printf("write chassis envvar error: %v", err)
		}
	} else {
		err := os.Setenv("APOLLO_CLUSTER", envvar.GetCIDLower())
		if err != nil {
			fmt.Printf("write chassis envvar error: %v", err)
		}
	}

	// [register]
	//1. 注册服务处理逻辑g

	// [register]
	//grpc_api.BindGrpcServer()

	// [init]

	//2. 初始化服务
	handler.RegisterRateLimitHandler()
	handler.RegisterPrometheusMetricHandler()
	handler.RegisterRecorderHandler(handler.RecorderHandlerOptions{
		StaticAllowRules: registerAllowInterface(),
	})
	//链路压测mock掉kafka写操作
	handler.RegisterInjectHandler(handler.WithInjection(func(ctx context.Context, serviceName string, interfaceName string, headers map[string]string, req interface{}, rsp interface{}) (status int) {
		//Kafka发送失败返回offset -1，区分一下mock返回-2
		var mockOffset int64 = -2
		if rpl, ok := rsp.(*saturn.PublishReply); ok { //第一步，将传入的rsp断言为saturn reply
			rpl.Partition = 0
			rpl.Offset = mockOffset
		}
		return invocation.StatusCanceled
	}))

	handler.RegisterReplayerHandler() // 回放配置
	// 请求响应日志打印使用自定义的序列化工具，Chassis默认的JsonPB的性能很差
	handler.RegisterLogHandler(
		handler.WithLogProviderRequestBodyParser(objutil.LogJsonBytes),
		handler.WithLogProviderResponseBodyParser(objutil.LogJsonBytes),
	)

	err := chassis.Init(
		//直接指定grpc server option，详细的可选server option请参阅grpc的官方文档
		chassis.WithChassisConfigPrefix("grpc_server"),
		chassis.WithGRPCUnaryServerInterceptor(
			chassis_middleware.Recovery,
			chassis_middleware.LogRequest,
			middleware.GrpcTrafficCollect,
			middleware.GrpcMockCtx,
		),
		chassis.WithDefaultProviderHandlerChain(
			handler.RecorderProviderName,           // 录制配置
			handler.RepalyerProviderName,           // 回放配置
			handler.NameOfPrometheusMetricProvider, //这个option主要用于存续项目，将原来的grpc中间件迁移到chassis框架中
			handler.NameOfRateLimitHandlerOfProvider,
			handler.LogProviderHandlerName, // 打印日志
		),
		chassis.WithDefaultConsumerHandlerChain(
			handler.InjectConsumerName,
			handler.NameOfPrometheusMetricConsumer,
		),
	)

	if err != nil {
		log.Fatalf("chassis init failed:%v", err)
		return
	}
	if err := configutil.Init(); err != nil {
		log.Fatalf("initialize global configuration fail, error:%+v", err)
		return
	}
	if err := dbutil.InitGrpcDb(); err != nil {
		log.Fatalf("initialize database fail, error:%+v", err)
		return
	}
	if err := localcache.Init(lcregistry.LocalCacheConfig...); err != nil {
		log.Fatalf("initialize local cache fail, error:%+v", err)
		return
	}
	if err := redisutil.InitDefaultClient(); err != nil {
		log.Fatalf("initialize redis client fail, error:%+v", err)
		return
	}
	if err := traceutil.InitOrderTraceSdk(); err != nil {
		log.Fatalf("initialize trace sdk fail, error:%v", err)
		return
	}
	if err := traceutil.InitExceptionReporter(); err != nil {
		log.Fatalf("initialize ExceptionReporter fail, error:%v", err)
		return
	}
	if err := kafkahelper.InitSaturnProducer(); err != nil {
		log.Fatalf("init saturn producer failed %v", err)
	}
	if err := traffic_playback.InitTrafficCollect(); err != nil {
		log.Fatalf("init traffic collect failed %v", err)
	}

	ser, _ := InitGrpc()
	ser.BindGrpcServer()
	if err := prometheusutil.InitGrpcMetrics(); err != nil {
		log.Fatalf("init prometheus failed %+v", err)
	}
	name := ""
	addr := ""
	if err := config.UnmarshalConfig("service_description.name", &name); err != nil {
		log.Printf("get service_description.name config error: %v\n", err)
	}
	if err := config.UnmarshalConfig("cse.protocols.grpc.listenAddress", &addr); err != nil {
		log.Printf("get cse.protocols.grpc.listenAddress config error: %v\n", err)
	}
	log.Printf("[%s] start listen addr: %s\n", name, addr)

	//3. 启动服务，阻塞等待服务推出
	if err := chassis.Run(); err != nil {
		fmt.Println("chassis run failed:", err)
		return
	}
}

func registerAllowInterface() []*handler.RecorderRule {
	return []*handler.RecorderRule{
		{
			RequestType: "grpc",
			Interface:   "/smart_routing_protobuf.SmartRouting/SelectLane",
		},
		{
			RequestType: "grpc",
			Interface:   "/smart_routing_protobuf.SmartRouting/ILHRouting",
		},
		{
			RequestType: "grpc",
			Interface:   "/smart_routing_protobuf.SmartRouting/PreFulfillmentCheck",
		},
		{
			RequestType: "grpc",
			Interface:   "/smart_routing_protobuf.Masking/Allocate",
		},
		{
			RequestType: "grpc",
			Interface:   "/smart_routing_protobuf.Masking/EstimateMaskingChannel",
		},
		{
			RequestType: "grpc",
			Interface:   "/smart_routing_protobuf.Masking/AdjustCapacity",
		},
		{
			RequestType: "grpc",
			Interface:   "/smart_routing_protobuf.Masking/AdjustLocVolume",
		},
		{
			RequestType: "grpc",
			Interface:   "/smart_routing_protobuf.VolumeRoutingServer/VolumeCounter",
		},
		{
			RequestType: "grpc",
			Interface:   "/smart_routing_protobuf.VolumeRoutingServer/FilterByVolume",
		},
		{
			RequestType: "grpc",
			Interface:   "/smart_routing_protobuf.VolumeRoutingServer/StatisticVolumeByOrder",
		},
	}
}
