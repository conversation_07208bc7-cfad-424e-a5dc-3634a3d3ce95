//go:generate wire
//go:build wireinject
// +build wireinject

package main

import (
	"github.com/google/wire"

	grpc_api "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/grpcfacade"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/services"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
)

func InitGrpc() (*grpc_api.GrpcAPI, error) {
	wire.Build(
		grpc_api.ProviderSet,
		redisutil.Client,
	)
	return new(grpc_api.GrpcAPI), nil
}

func InitSaturnService() *services.SaturnService {
	saturnService, _ := initSaturnService()
	services.SetService(saturnService)

	return services.GetService()
}

func initSaturnService() (*services.SaturnService, error) {
	wire.Build(
		saturn_facade.ProviderSet,
		redisutil.Client,
	)
	return new(services.SaturnService), nil
}
