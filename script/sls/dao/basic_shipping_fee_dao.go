package dao

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/script/sls/model"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/script/sls/script_enum"
)

type BasicShippingFeeDao struct {
}

func (b BasicShippingFeeDao) GetOldModel() interface{} {
	return bsfModel
}

func (b BasicShippingFeeDao) GenerateQueryParam(rateID int64, minID int64, maxID int64, feeType int8) QueryParam {
	return generateQueryParamWithFeeType(rateID, minID, maxID, feeType)
}

func (b BasicShippingFeeDao) GenerateCountParam(rateID int64, feeType int8) QueryParam {
	return generateCountParamWithFeeType(rateID, feeType)
}

func (b BasicShippingFeeDao) GetNewModel() interface{} {
	return bsfModel
}

func (b BasicShippingFeeDao) GetOldDataArr() interface{} {
	oldDataArr := make([]model.BasicShippingFeeTab, 0)
	return &oldDataArr
}

func (b BasicShippingFeeDao) GenerateNewData(data interface{}, rateID int64, now uint32, _ ...script_enum.TransferFeeType) (int64, interface{}) {
	oldData := *data.(*[]model.BasicShippingFeeTab)
	newData := make([]model.BasicShippingFeeTab, 0)
	for i := range oldData {
		newData = append(newData, oldData[i].ToNewData(rateID, now).(model.BasicShippingFeeTab))
	}
	return int64(len(newData)), newData
}

func (b BasicShippingFeeDao) GetOldDataLength(data interface{}) int64 {
	oldData := *data.(*[]model.BasicShippingFeeTab)
	return int64(len(oldData))
}

var bsfModel = model.BasicShippingFeeTab{}
