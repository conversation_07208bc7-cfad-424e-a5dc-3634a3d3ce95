package sls

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_core/shipping_fee_item_resolver/rounding"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/utils/valueutil"
	"strconv"

	dto3 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/dto"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/gocommon/monitor"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_core/protocol"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_core/shipping_fee_item_resolver"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_core/shipping_fee_item_resolver/definition"
	entity2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/entity"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/entity"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/entity/billing_order"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/entity/biz_order"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/service"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_role/dto"
	service3 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/service"
	service2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/sls/recon_rule/service"
	dto2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/sls/recon_rule/vo"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/constant"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/monitoring"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/store"
	utils "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/utils/common"
)

// CompensationParam @core
type CompensationParam struct {
	Timestamp                  uint32
	PaymentMethod              enum.PaymentMethod
	ExceptionType              enum.WaybillExceptionTypeOption
	sellerAsf                  float64
	esf                        float64
	chargeableWeightParcel     float64
	chargeableWeightSku        float64
	cogs                       float64
	grandTotal                 float64
	sellerVoucher              float64
	reconFlag                  bool
	reconVersion               int32
	exceptionSubType           enum.ExceptionSubType // SCFS-4250
	uploadedCompensationAmount float64
	OnlyAdjust                 bool
	AgreedExceptionType        enum.WaybillExceptionTypeOption
	AgreedTimestamp            uint32
	reReconFlag                bool
	CalculateType              enum.BillingCalculateType
	addCodFlag                 bool
	CodTax                     float64
	IgnoreClaimCap             bool
	ExceptionSource            enum.ExceptionSource
}

// CompensationReq @core
type CompensationReq struct {
	CompensationParam
	BizOrder      *biz_order.ThreePLAsfBizOrderEntity
	scenario      enum.ThreePLASFCalculateScenario
	currentStatus string
}

// CompensationResp @core
type CompensationResp struct {
	totalFee                         float64
	compensationAmount               float64
	priorRecon                       float64
	calcErr                          *retcode.SSCError
	threePlAsfBillingOrder           *billing_order.ThreePlAsfBillingOrder
	rtsBillingOrder                  *billing_order.RtsBillingOrder
	oldBillingOrder, newBillingOrder *billing_order.CompensationBillingOrder
	noNeedStore                      bool
	RuleToUse                        enum.CalculateFeeType
}

func (r *CompensationResp) getCompensationAmount() float64 {
	if r == nil {
		return 0
	}
	return r.compensationAmount
}

func (r *CompensationResp) getPriorRecon() float64 {
	if r == nil {
		return 0
	}
	return r.priorRecon
}

func (r *CompensationResp) getRuleToUse() enum.CalculateFeeType {
	if r == nil {
		return 0
	}
	return r.RuleToUse
}

func (r *CompensationResp) getTotalFee() float64 {
	if r == nil {
		return 0
	}
	return r.totalFee
}

func (r *CompensationResp) getCalcErr() *retcode.SSCError {
	if r == nil {
		return nil
	}
	return r.calcErr
}

func (r *CompensationResp) getCompensationOrder() *billing_order.CompensationBillingOrder {
	if r == nil {
		return nil
	}
	return r.newBillingOrder
}

func (r *CompensationResp) GetRtsOrder() *billing_order.RtsBillingOrder {
	if r == nil {
		return nil
	}
	return r.rtsBillingOrder
}

func (r *CompensationResp) GetTplOrder() *billing_order.ThreePlAsfBillingOrder {
	if r == nil {
		return nil
	}
	return r.threePlAsfBillingOrder
}

func (r *CompensationResp) GetPair() *entity.ReconcilablePair {
	if r == nil {
		return nil
	}
	return &entity.ReconcilablePair{
		Old: r.oldBillingOrder,
		New: r.newBillingOrder,
	}
}

type AdjustTplASFRequest struct {
	BizOrder      *biz_order.ThreePLAsfBizOrderEntity
	PayableRule   *dto2.CompensationPayableRule
	ExceptionType enum.WaybillExceptionTypeOption
	ReReconFlag   bool
	CalculateType enum.BillingCalculateType
	BillingOrder  billing_order.IBillingEntity
	AddCodFlag    bool
}

type AdjustTplRTSRequest struct {
	BizOrder      *biz_order.ThreePLAsfBizOrderEntity
	PayableRule   *dto2.CompensationPayableRule
	ExceptionType enum.WaybillExceptionTypeOption
	ReReconFlag   bool
	CalculateType enum.BillingCalculateType
}

type ICompensationController interface {
	CalcCompensation(ctx dbhelper.BmsContext, req CompensationReq) (*CompensationResp, *retcode.SSCError)
}

// @core
type CompensationController struct {
	compensation         shipping_fee_item_resolver.ICompensationCalculationService
	orderService         service.IOrderService
	billingPushService   service.IBillingPushService
	payableService       service2.IPayableRuleService
	threePlReportService service3.IThreePlFeeReportService
	reconOrderService    service.IReconOrderService
}

func (l CompensationController) CalcCompensation(ctx dbhelper.BmsContext, req CompensationReq) (*CompensationResp, *retcode.SSCError) {
	logger.CtxLogInfof(ctx, "calcCompensation req:%+v", req)
	compensationConfig, sscError := store.GetInstance(ctx.GetStoreType()).GetFeeType(ctx, enum.Country(ctx.GetCountry()), enum.CompensationFee)
	if sscError != nil {
		return nil, sscError
	}
	if !compensationConfig.IsNeedCalculate() {
		sscError = retcode.SopError.CloneWithDetailFormat("feeType=%+v NotNeedCalculate", enum.CompensationFee.StringView())
		return nil, sscError
	}

	oldBillingOrder, sscError := l.orderService.GetBillingOrderWithTime(ctx, req.BizOrder.GetScfsOrderID(), int64(enum.CompensationFee), req.BizOrder.GetBizOrderEntity().OrderCtime, ctx.GetCountry())
	if sscError != nil {
		return nil, sscError
	}

	if dbhelper.IsCompensationReconScene(ctx) && oldBillingOrder == nil {
		newBillingOrder, sscError := l.orderService.InitCompensationBillingOrder(ctx, compensationConfig, req.BizOrder, req.reReconFlag, req.reconVersion)
		if sscError != nil {
			return nil, sscError
		}
		if newBillingOrder != nil {
			oldBillingOrder = newBillingOrder
		}
	}

	var oldCompensationOrder *billing_order.CompensationBillingOrder
	if oldBillingOrder != nil {
		oldCompensationOrder, _ = oldBillingOrder.(*billing_order.CompensationBillingOrder)
		// 如果是已经 recon done 的 compensation，则用上一次的 exception info 计算 compensation.
		if oldCompensationOrder.FixedFeeAmount() {
			req.ExceptionType = oldCompensationOrder.Req.ExceptionType
			req.Timestamp = oldCompensationOrder.Req.Timestamp
			req.PaymentMethod = oldCompensationOrder.Req.PaymentMethod
		}
	}

	// pick rate table first
	// previously, if compensation rate table is not found, 3PL ASF and RTS fee CAN STILL be adjusted
	// since new payableRule will be bound with compensation rate table, if rate table is not found, we can't calculate anything
	// but, we still need to compat old payableRule logic
	// use agreed data to pick payable rate table in advance
	rateTable := l.compensation.PickOptimalRateTable(ctx, l.pickRateTableRequest(ctx, req, true))

	var payableRule *dto2.CompensationPayableRule
	var getAdjustmentRuleErr *retcode.SSCError
	reReconFlag := req.reReconFlag
	// try to get rule from rate table in advance
	exceptionType := req.ExceptionType
	if req.AgreedExceptionType != enum.UndefinedException {
		exceptionType = req.AgreedExceptionType
	}
	// payable rule for adjustment
	// try to get rule from rate table first
	if rateTable != nil {
		payableRule = rateTable.GetExtraData().ToLost().GetExactRuleByRuleType(exceptionType.ToRuleType())
	}
	// if rate table is not found or payable rule is not configured in rate table, get rule from old table
	if payableRule == nil {
		payableRule, getAdjustmentRuleErr = l.payableService.GetPayableRule(ctx, req.BizOrder.GetServiceID(), exceptionType.ToRuleType())
	}
	if getAdjustmentRuleErr != nil {
		return nil, getAdjustmentRuleErr
	}

	// 获取3pl asf billing order设置cod flag
	oldTplBillingOrder, sscError := l.orderService.GetBillingOrderWithTime(ctx, req.BizOrder.GetScfsOrderID(), int64(enum.ThreePLASF), req.BizOrder.GetBizOrderEntity().OrderCtime, ctx.GetCountry())
	if sscError != nil {
		return nil, sscError
	}

	addCodFlag := false
	if oldTplBillingOrder != nil && oldTplBillingOrder.GetBillingEntity().RateChannelId != 0 {
		rateId := oldTplBillingOrder.GetBillingEntity().RateChannelId
		tplAsfRateTable := store.GetInstance(ctx.GetStoreType()).GetRateTableByRateId(ctx, enum.ThreePLASF, oldTplBillingOrder.GetServiceID(), ctx.GetCountry(), uint32(rateId))
		if tplAsfRateTable == nil {
			logger.CtxLogErrorf(ctx, "[CalcCompensation] 3pl asf rate channel not found, rateId %d, feeType %s, country %s", rateId, enum.ThreePLASF.StringView(), ctx.GetCountry())
			return nil, retcode.RateChannelNotFound.CloneWithDetailFormat("3pl asf rate channel not found, rateId %d, feeType %s, country %s", rateId, enum.ThreePLASF.StringView(), ctx.GetCountry())
		}
		addCodFlag = tplAsfRateTable.GetExtraData().ToBase().GetExtraFeeComponentConfig().CodServiceFeeRule == enum.Add2LogisticsFee
		req.CompensationParam.CodTax = oldTplBillingOrder.(*billing_order.ThreePlAsfBillingOrder).CalculateInfo.ShippingInfo.CodFee.TaxFee
	}
	req.CompensationParam.addCodFlag = addCodFlag

	// adjust 3PL ASF and RTS fee
	adjustTplASFReq := AdjustTplASFRequest{
		BizOrder:      req.BizOrder,
		PayableRule:   payableRule,
		ExceptionType: exceptionType,
		ReReconFlag:   reReconFlag,
		CalculateType: req.CalculateType,
		AddCodFlag:    addCodFlag,
		BillingOrder:  oldTplBillingOrder,
	}
	threePlAsfBillingOrder, sscError := l.adjustThreePlAsf(ctx, adjustTplASFReq)
	if sscError != nil {
		return nil, sscError
	}

	adjustRtsReq := AdjustTplRTSRequest{
		BizOrder:      req.BizOrder,
		PayableRule:   payableRule,
		ExceptionType: exceptionType,
		ReReconFlag:   reReconFlag,
		CalculateType: req.CalculateType,
	}

	rtsBillingOrder, sscError := l.adjustRts(ctx, adjustRtsReq)
	if sscError != nil {
		return nil, sscError
	}

	// only adjust 3PL ASF and RTS fee
	if req.OnlyAdjust {
		logger.CtxLogInfof(ctx, fmt.Sprintf("trigger by update agreed info, only adjust 3PL ASF and RTS fee, bizOrderNo: %s", req.BizOrder.GetBizOrderNo()))
		compensationResp := &CompensationResp{
			threePlAsfBillingOrder: threePlAsfBillingOrder,
			rtsBillingOrder:        rtsBillingOrder,
		}

		if dbhelper.IsCompensationReconScene(ctx) {
			compensationResp.oldBillingOrder = oldCompensationOrder
			compensationResp.newBillingOrder = oldCompensationOrder
		}
		return compensationResp, nil
	}

	// pick compensation rate table
	var rc *rounding.ComponentRoundingCfg
	if exceptionType != req.ExceptionType {
		rateTable = l.compensation.PickOptimalRateTable(ctx, l.pickRateTableRequest(ctx, req, false))
		// return in advance
		if rateTable == nil {
			monitoring.BizReportCalculateResult(ctx, req.BizOrder.GetServiceID(), 0, enum.CompensationFee.StringView(), retcode.RateChannelNotFound)
			ctx.Set(enum.BILLING_RATE_PROCESS.TransferVisualKey(enum.ERROR_SUFFIX), retcode.RateChannelNotFound)
			return nil, retcode.RateChannelNotFound
		}
		// payable rule for compensation
		payableRule = rateTable.GetExtraData().ToLost().GetExactRuleByRuleType(req.ExceptionType.ToRuleType())
		if payableRule == nil {
			payableRule, getAdjustmentRuleErr = l.payableService.GetPayableRule(ctx, req.BizOrder.GetServiceID(), req.ExceptionType.ToRuleType())
		}
		if getAdjustmentRuleErr != nil {
			return nil, getAdjustmentRuleErr
		}
	}
	if rateTable != nil {
		rc = rounding.GetRoundingComponentCfg(ctx, enum.Country(rateTable.GetCountry()), rateTable.GetFeeTypeId(), enum.TotalFeeRoundingComponent, rateTable.GetServiceType())
	}

	compensation, sscError := l.Compensation(ctx, req, threePlAsfBillingOrder, rtsBillingOrder, payableRule, compensationConfig, rateTable, oldCompensationOrder, rc)
	if sscError != nil {
		return nil, sscError
	}
	compensation.threePlAsfBillingOrder = threePlAsfBillingOrder
	compensation.rtsBillingOrder = rtsBillingOrder
	return compensation, nil
}

func (l CompensationController) Compensation(
	ctx dbhelper.BmsContext,
	req CompensationReq,
	threePlAsfBillingOrder *billing_order.ThreePlAsfBillingOrder,
	rtsBillingOrder *billing_order.RtsBillingOrder,
	payableRule *dto2.CompensationPayableRule,
	compensation *dto.FeeTypeConfigDto,
	rateTable entity2.RateTable,
	oldCompensationOrder *billing_order.CompensationBillingOrder,
	rc *rounding.ComponentRoundingCfg,
) (*CompensationResp, *retcode.SSCError) {
	startTime := recorder.Now(ctx)

	//var sscError *retcode.SSCError
	//if oldBillingOrder == nil {
	//	oldBillingOrder, sscError = l.orderService.GetBillingOrder(ctx, req.BizOrder.GetScfsOrderID(), int64(enum.CompensationFee), ctx.GetCountry())
	//	if sscError != nil {
	//		return nil, sscError
	//	}
	//}

	//if dbhelper.IsCompensationReconScene(ctx) && oldBillingOrder == nil {
	//	newBillingOrder, sscError := l.orderService.InitCompensationBillingOrder(ctx, compensation, req.BizOrder, req.reReconFlag, req.reconVersion, enum.CalculateStatusError)
	//	if sscError != nil {
	//		return nil, sscError
	//	}
	//	if newBillingOrder != nil {
	//		oldBillingOrder = newBillingOrder
	//	}
	//}

	//var oldCompensationOrder *billing_order.CompensationBillingOrder
	//if oldBillingOrder != nil {
	//	oldCompensationOrder = oldBillingOrder.(*billing_order.CompensationBillingOrder)
	//	if oldCompensationOrder.FixedFeeAmount() {
	//		return &CompensationResp{
	//			totalFee:           oldCompensationOrder.Resp.GetTotalShippingFee(),
	//			compensationAmount: oldCompensationOrder.Resp.GetCompensationAmount(),
	//			priorRecon:         oldCompensationOrder.Resp.GetPriorRecon(),
	//			oldBillingOrder:    oldCompensationOrder,
	//			newBillingOrder:    oldCompensationOrder,
	//		}, nil
	//	}
	//}

	var sscError *retcode.SSCError
	if oldCompensationOrder == nil {
		oldBillingOrder, sscError := l.orderService.GetBillingOrderWithTime(ctx, req.BizOrder.GetScfsOrderID(), int64(enum.CompensationFee),
			req.BizOrder.GetBizOrderEntity().OrderCtime, ctx.GetCountry())
		if sscError != nil {
			return nil, sscError
		}
		if oldBillingOrder != nil {
			oldCompensationOrder = oldBillingOrder.(*billing_order.CompensationBillingOrder)
		}
	}
	if oldCompensationOrder != nil {
		if oldCompensationOrder.FixedFeeAmount() {
			return &CompensationResp{
				totalFee:           oldCompensationOrder.Resp.GetTotalShippingFee(),
				compensationAmount: oldCompensationOrder.Resp.GetCompensationAmount(),
				priorRecon:         oldCompensationOrder.Resp.GetPriorRecon(),
				oldBillingOrder:    oldCompensationOrder,
				newBillingOrder:    oldCompensationOrder,
			}, nil
		}
	}

	var calcErr *retcode.SSCError
	var compensationResult *protocol.CompensationCalculateResponse
	var calcReq protocol.CompensationFeeCalculateRequest
	var calcParams definition.CalculateParams
	var priorRecon float64

	if payableRule != nil {
		if threePlAsfBillingOrder != nil && threePlAsfBillingOrder.FixedFeeAmount() {
			//remark字段调整之后也需要保留
			remark := threePlAsfBillingOrder.CalculateInfo.ShippingInfo.ExtraCharges.Remark
			newAdjust := GetThreePlAsfPayableDetail(ctx, *payableRule, threePlAsfBillingOrder.CalculateInfo.BeforeAdjustFeeComponent, req.addCodFlag, remark, rc)
			reconInfo, reconErr := l.reconOrderService.GetBillingReconInfo(ctx, req.BizOrder.GetServiceID(), enum.ThreePLASF, req.BizOrder.GetBizOrderEntity().AssociateOrderNo)
			priorRecon = getThreePlAsfPriorRecon(reconInfo, reconErr, threePlAsfBillingOrder, newAdjust.TotalShippingFee)
		}
		if rtsBillingOrder != nil && rtsBillingOrder.FixedFeeAmount() {
			reconInfo, reconErr := l.reconOrderService.GetBillingReconInfo(ctx, req.BizOrder.GetServiceID(), enum.ThreePLASF, req.BizOrder.GetBizOrderEntity().AssociateOrderNo)
			if payableRule.Has(enum.AdjustmentBit3plRtsFee) {
				needPayRts := rtsBillingOrder.CalculateInfo.TotalShippingFeeBeforeAdjust
				// 没有保存调整前rts的flag，且调整前rts保存值==0 => 使用DB里保存的total fee计算（加needPayRts == 0是为了兼容保存了rts调整前费用，但是没有保存flag的情况）
				if !rtsBillingOrder.CalculateInfo.HasBeforeAdjust && needPayRts == 0 {
					needPayRts = rtsBillingOrder.GetTotalFee()
					logger.CtxLogInfof(ctx, "historical order, compatible with current total fee in db, needPayRts=%v", needPayRts)
				}
				// shopee收取的费用 += 已经付给supplier的金额 - 需要支付supplier的金额(勾选AdjustmentBit3plRtsFee，claim单仍旧需要支付费用)
				priorRecon += getThreeRtsPriorRecon(reconInfo, reconErr, rtsBillingOrder, rtsBillingOrder.GetTotalFee()-needPayRts)
			} else {
				priorRecon += getThreeRtsPriorRecon(reconInfo, reconErr, rtsBillingOrder, rtsBillingOrder.GetTotalFee())
			}
		}
		calcReq = l.compensationRequest(ctx, req, priorRecon, threePlAsfBillingOrder, rateTable, rc)
		compensationResult, calcParams, calcErr = l.compensation.CalculateCompensation(ctx, calcReq)
	} else {
		calcErr = retcode.CompensationAdjustmentRuleNotFound.CloneWithDetailFormat("service id %s calcCompensation adjustment config not found", req.BizOrder.BizOrder.ServiceID)
		//billing history有部分字段是从calcReq 中取，所以即使没有payableRule导致计费失败，也需要对calcReq赋值
		calcReq = l.compensationRequest(ctx, req, priorRecon, threePlAsfBillingOrder, rateTable, rc)
	}
	// 上报费用结果
	l.threePlReportService.ReportThreePlCompensationFee(ctx, startTime, calcReq, compensationResult, calcErr)
	var billingOrder *billing_order.CompensationBillingOrder
	var ruleToUse enum.CalculateFeeType
	var rateId uint32
	if calcParams != nil {
		if params, ok := calcParams.(*definition.CompensationFeeCalculateParams); ok {
			ruleToUse = params.RuleToUse
			rateId = params.RateTable.GetRateId()
		}
	}

	if compensation.IsNeedStore(ctx) {
		billingOrder, sscError = l.handleCompensationNewBillingOrder(ctx, req, oldCompensationOrder, calcReq, compensationResult, ruleToUse, calcErr, rateId)
		if sscError != nil {
			return nil, sscError
		}
	}

	resp := &CompensationResp{
		totalFee:               compensationResult.GetTotalShippingFee(),
		compensationAmount:     compensationResult.GetCompensationAmount(),
		priorRecon:             compensationResult.GetPriorRecon(),
		calcErr:                calcErr,
		threePlAsfBillingOrder: threePlAsfBillingOrder,
		rtsBillingOrder:        rtsBillingOrder,
		newBillingOrder:        billingOrder,
		oldBillingOrder:        oldCompensationOrder,
		RuleToUse:              ruleToUse,
	}

	if billingOrder == nil || (billingOrder.GetCalculateStatus() != enum.CalculateStatusSuccess && oldCompensationOrder.GetCalculateStatus() == enum.CalculateStatusSuccess) { //nolint
		resp.newBillingOrder = oldCompensationOrder
	}
	return resp, nil
}

func getThreePlAsfPriorRecon(reconInfo *dto3.SingleBillingReconInfo, reconErr *retcode.SSCError, threePlAsfBilling *billing_order.ThreePlAsfBillingOrder, newAdjustTotalFee float64) float64 {
	// 第一优先级 取recon获取的agreeAmount作为priorRecon
	if reconErr == nil && reconInfo.AgreeFeeInfo.AmountFlag {
		agreeAmount, err := strconv.ParseFloat(reconInfo.AgreeFeeInfo.Amount, 64)
		if err == nil {
			return agreeAmount
		}
	}
	// 第二优先级 取计费计算的金额
	if threePlAsfBilling != nil && threePlAsfBilling.GetBillingEntity() != nil && threePlAsfBilling.GetBillingEntity().CalculateStatus == enum.CalculateStatusSuccess {
		return threePlAsfBilling.GetTotalFee() - newAdjustTotalFee
	}
	// 第三优先级 取对账的3pl upload Amount
	if reconErr == nil {
		uploadAmount, err := strconv.ParseFloat(reconInfo.TplUploadFee.Amount, 64)
		if err == nil {
			return uploadAmount
		}
	}
	// 上面三个都取不到 兜底返回0
	return 0
}

func getThreeRtsPriorRecon(reconInfo *dto3.SingleBillingReconInfo, reconErr *retcode.SSCError, rtsBilling *billing_order.RtsBillingOrder, shopeeCalculateRts float64) float64 {
	// 第一优先级 取recon获取的agreeAmount作为priorRecon
	if reconErr == nil && reconInfo.RtsAgreeFee.AmountFlag {
		agreeAmount, err := strconv.ParseFloat(reconInfo.RtsAgreeFee.Amount, 64)
		if err == nil {
			return agreeAmount
		}
	}
	// 第二优先级 取计费计算的金额
	if rtsBilling != nil && rtsBilling.GetBillingEntity() != nil && rtsBilling.GetBillingEntity().CalculateStatus == enum.CalculateStatusSuccess {
		return shopeeCalculateRts
	}
	// 第三优先级 取对账的3pl upload Amount
	if reconErr == nil {
		uploadAmount, err := strconv.ParseFloat(reconInfo.RtsTplUploadFee.Amount, 64)
		if err == nil {
			return uploadAmount
		}
	}
	// 上面三个都取不到 兜底返回0
	return 0
}

func needPullReconInfo(threePlAsfBilling *billing_order.ThreePlAsfBillingOrder, rtsBilling *billing_order.RtsBillingOrder) bool {
	if (threePlAsfBilling != nil && threePlAsfBilling.FixedFeeAmount()) || (rtsBilling != nil && rtsBilling.FixedFeeAmount()) {
		return true
	}
	return false
}

func (l CompensationController) compensationRequest(ctx dbhelper.BmsContext, req CompensationReq, recon float64, threePlAsfBillingOrder *billing_order.ThreePlAsfBillingOrder, rateTable entity2.RateTable, rc *rounding.ComponentRoundingCfg) protocol.CompensationFeeCalculateRequest {
	request := protocol.CompensationFeeCalculateRequest{
		Rate:                       rateTable,
		ServiceID:                  req.BizOrder.BizOrder.ServiceID,
		Region:                     ctx.GetCountry(),
		Timestamp:                  req.Timestamp,
		PaymentMethod:              req.PaymentMethod,
		ExceptionType:              req.ExceptionType,
		SellerAsf:                  req.sellerAsf,
		Esf:                        req.esf,
		ChargeableWeightParcel:     req.chargeableWeightParcel,
		ChargeableWeightSku:        req.chargeableWeightSku,
		Cogs:                       req.cogs,
		GrandTotal:                 req.grandTotal,
		SellerVoucher:              req.sellerVoucher,
		PriorRecon:                 recon,
		FeeType:                    enum.CompensationFee,
		ExceptionOrderSubType:      req.exceptionSubType,
		UploadedCompensationAmount: req.uploadedCompensationAmount,
		IgnoreClaimCap:             req.IgnoreClaimCap,
		ExceptionSource:            req.ExceptionSource,
	}
	// fallback rc
	if rc == nil {
		rc = rounding.GetRoundingComponentCfg(ctx, enum.Country(request.Region), request.FeeType, enum.TotalFeeRoundingComponent, enum.ServiceType(req.BizOrder.BizOrder.ServiceType))
		// 打印一下单号
		logger.CtxLogErrorf(ctx, "fallback case slsTN=%s", request.BizOrderNo)
		rounding.ExceptionRoundReport(ctx, request.FeeType, enum.ServiceType(req.BizOrder.BizOrder.ServiceType), "RequestFallback")
	}

	if threePlAsfBillingOrder != nil {
		request.ThreePLAsf = threePlAsfBillingOrder.Resp.GetDetail().TotalFee() // Notice: 这里的Resp是在前置流程中构造，并非存储到DB的Resp
		// SCFS-25020 addCodFlag为false的情况下，VatFee不包含cod tax，这里要加上
		if !req.addCodFlag {
			request.ThreePLAsf += req.CodTax
		}
		request.ThreePLAsf = rc.FeeRoundingByStandardConfig(ctx, request.ThreePLAsf, valueutil.NewFloat64(threePlAsfBillingOrder.Resp.GetDetail().TotalFeeWithDecimal(req.addCodFlag, req.CodTax).InexactFloat64()), rounding.SetRoundFuncNotUse(true))
	}
	return request
}

func (l CompensationController) pickRateTableRequest(ctx dbhelper.BmsContext, req CompensationReq, useAgreed bool) protocol.CompensationPickRateTableRequest {
	exceptionType := req.ExceptionType
	if useAgreed && req.AgreedExceptionType != enum.UndefinedException {
		exceptionType = req.AgreedExceptionType
	}
	timestamp := req.Timestamp
	if useAgreed && req.AgreedTimestamp != 0 {
		timestamp = req.AgreedTimestamp
	}

	return protocol.CompensationPickRateTableRequest{
		ServiceID:     req.BizOrder.GetServiceID(),
		Region:        ctx.GetCountry(),
		Timestamp:     timestamp,
		FeeType:       enum.CompensationFee,
		PaymentMethod: req.PaymentMethod,
		ExceptionType: exceptionType,
	}
}

func (l CompensationController) handleCompensationNewBillingOrder(ctx dbhelper.BmsContext, req CompensationReq, oldBillingOrder *billing_order.CompensationBillingOrder, calcReq protocol.CompensationFeeCalculateRequest, compensation *protocol.CompensationCalculateResponse, ruleToUse enum.CalculateFeeType, calcError *retcode.SSCError, rateId uint32) (*billing_order.CompensationBillingOrder, *retcode.SSCError) {
	compensationConfig, err := store.GetInstance(ctx.GetStoreType()).GetFeeType(ctx, enum.Country(ctx.GetCountry()), enum.CompensationFee)
	if err != nil {
		return nil, err
	}
	newBillingOrder := &billing_order.CompensationBillingOrder{
		Country:          ctx.GetCountry(),
		ScfsOrderId:      req.BizOrder.GetScfsOrderID(),
		FeeType:          int64(compensationConfig.FeeTypeId),
		TotalFee:         compensation.GetTotalShippingFee(),
		ChargeableWeight: compensation.GetChargeableWeight(),
		BillingRole:      int64(compensationConfig.Role),
		RelateRole:       int64(compensationConfig.RelatedBillingRole),
		ServiceID:        req.BizOrder.BizOrder.ServiceID,
		BizOrderNo:       req.BizOrder.BizOrder.BizOrderNo,
		FeeCategory:      int64(compensationConfig.FeeCategory),
		ServiceDimension: int64(compensationConfig.ServiceDimension),
		ServiceType:      req.BizOrder.BizOrder.ServiceType,
		ServiceCategory:  req.BizOrder.BizOrder.ServiceCategory,
		RateChannelId:    compensation.GetRateChannelID(),
		CalculateTime:    int64(utils.GetTimestamp(ctx)),
		OrderStatus:      req.BizOrder.BizOrder.OrderStatus,
		PaymentMethod:    string(req.PaymentMethod),
		CalculateStatus:  enum.CalculateStatusError,
		Req:              calcReq,
		Resp:             compensation.WithCalcErr(calcError),
		Cogs:             req.cogs,
		ExtraData:        enum.EmptyStruct,
		AssociateOrderNo: req.BizOrder.BizOrder.AssociateOrderNo,
		BillingStatus:    enum.BillingStatusInit,
	}
	if oldBillingOrder != nil {
		newBillingOrder.ExtraData = oldBillingOrder.ExtraData
	}
	if err := newBillingOrder.SetExtraDataWithSupplierType(req.BizOrder.ExtraData.SupplierType); err != nil {
		return nil, err
	}

	if newBillingOrder.RateChannelId == 0 {
		newBillingOrder.RateChannelId = int64(rateId)
	}
	newBillingOrder.SetReconStartBillingStatus(ctx)

	if sscError := newBillingOrder.SetRuleToUse(ruleToUse); sscError != nil {
		return nil, sscError
	}

	if calcError == nil {
		newBillingOrder.CalculateStatus = enum.CalculateStatusSuccess
	}

	newBillingOrder.CalculateType = int64(req.CalculateType)

	_, _, sscError := l.orderService.UpsertBillingOrder(ctx, oldBillingOrder, newBillingOrder, calcError, true, req.reReconFlag)

	// report
	// 1) compensation billing orders' upsert result with exception type
	// 2) compensation billing orders' exception type change times
	operation := constant.OperationUpdate
	if oldBillingOrder == nil || oldBillingOrder.IsNil() {
		operation = constant.OperationCreate
	} else if oldBillingOrder.Req.ExceptionType != req.ExceptionType {
		moduleName := fmt.Sprintf("%s.%s", constant.CatModuleFunction, newBillingOrder.GetFeeType().StringView())
		interfaceName := fmt.Sprintf("%s->%s", oldBillingOrder.Req.ExceptionType.StringView(), req.ExceptionType.StringView())
		_ = monitor.AwesomeReportEvent(ctx, moduleName, interfaceName, sscError.Retcode().String(), sscError.String())
	}
	monitoring.BizReportBillingResultWithSubType(ctx, newBillingOrder.GetFeeType().StringView(), req.ExceptionType.StringView(), operation, sscError)

	if sscError != nil {
		return nil, sscError
	}
	l.processBillingOrderPush(ctx, oldBillingOrder, newBillingOrder, req.BizOrder)

	return newBillingOrder, nil
}

func (l CompensationController) processBillingOrderPush(ctx dbhelper.BmsContext, oldOrder billing_order.IBillingEntity, newOrder billing_order.IBillingEntity, newBizOrder *biz_order.ThreePLAsfBizOrderEntity) *retcode.SSCError {
	isUpdate := false
	if oldOrder != nil {
		isUpdate = true
	}
	bizOrderTab, err := newBizOrder.ToBizOrderTab()
	if err != nil {
		return retcode.UpdateBizOrderError.CloneWithDetailFormat("transfer tab got err=%+v bizOrderEnt=%+v", err, newBizOrder)
	}
	bizOrderTime := bizOrderTab.OrderCtime
	l.billingPushService.PushInsertBillingOrder(ctx, newOrder, bizOrderTime, isUpdate, newOrder.GetScfsBillingID())
	return nil
}

func (l CompensationController) adjustRts(ctx dbhelper.BmsContext, req AdjustTplRTSRequest) (*billing_order.RtsBillingOrder, *retcode.SSCError) {
	billingOrder, sscError := l.orderService.GetBillingOrderWithTime(ctx, req.BizOrder.GetScfsOrderID(), int64(enum.RtsFee), req.BizOrder.GetBizOrderEntity().OrderCtime, ctx.GetCountry())
	if sscError != nil {
		return nil, sscError
	}
	if billingOrder == nil {
		return nil, nil
	}
	rtsBillingOrder := billingOrder.(*billing_order.RtsBillingOrder)
	if rtsBillingOrder.FixedFeeAmount() ||
		rtsBillingOrder.CalculateStatus == enum.CalculateStatusError ||
		req.PayableRule == nil {
		return rtsBillingOrder, nil
	}

	rc := rounding.GetRoundingComponentCfg(ctx, enum.Country(rtsBillingOrder.GetBillingEntity().Country), rtsBillingOrder.GetFeeType(), enum.TotalFeeRoundingComponent, enum.ServiceType(rtsBillingOrder.GetBillingEntity().ServiceType))
	if !req.PayableRule.Has(enum.AdjustmentBit3plRtsFee) {
		rtsBillingOrder.Resp.RtsFee = 0
		detail := protocol.RtsCalculateDetail{
			RateID:    rtsBillingOrder.Resp.RtsFeeDetail.RateID,
			DPVersion: rc.GetVersion(),
		}
		rtsBillingOrder.Resp.RtsFeeDetail = detail
		rtsBillingOrder.TotalFee = rtsBillingOrder.Resp.GetTotalFee()
		rtsBillingOrder.TotalFee = rc.FeeRoundingByStandardConfig(ctx, rtsBillingOrder.TotalFee, valueutil.NewFloat64(rtsBillingOrder.Resp.GetTotalFee()))
	}
	//for safety
	if rtsBillingOrder.Resp != nil {
		// 兼容老的LostOrderTag字段
		if req.ExceptionType == enum.LostException {
			rtsBillingOrder.Resp.LostOrderTag = true
		}
		rtsBillingOrder.Resp.ExceptionType = req.ExceptionType
	}
	rtsBillingOrder.CalculateType = req.CalculateType.ToInt64()
	_, billingOrder, sscError = l.orderService.UpsertBillingOrder(ctx, rtsBillingOrder, rtsBillingOrder, nil, true, req.ReReconFlag)
	if sscError != nil {
		return nil, sscError
	}
	rtsBillingOrder = billingOrder.(*billing_order.RtsBillingOrder)
	l.processBillingOrderPush(ctx, rtsBillingOrder, rtsBillingOrder, req.BizOrder)
	return rtsBillingOrder, nil
}

func (l CompensationController) adjustThreePlAsf(ctx dbhelper.BmsContext, req AdjustTplASFRequest) (*billing_order.ThreePlAsfBillingOrder, *retcode.SSCError) {
	if req.BillingOrder == nil {
		return nil, nil
	}
	threePLAsfBillingOrder, ok := req.BillingOrder.(*billing_order.ThreePlAsfBillingOrder)
	if !ok {
		return nil, retcode.GetBillingOrderError.CloneWithDetail("GetBillingOrder ThreePLASF failed")
	}
	oldShippingFeeInfo := threePLAsfBillingOrder.CalculateInfo.ShippingInfo
	// 如果保存了未调整的fee component，使用calculate info里未调整过的fee component
	if threePLAsfBillingOrder.ExtraData.HasBeforeAdjust {
		oldShippingFeeInfo = threePLAsfBillingOrder.CalculateInfo.BeforeAdjustFeeComponent
	}
	threePLAsfBillingOrder.CalculateInfo.BeforeAdjustFeeComponent = oldShippingFeeInfo

	if threePLAsfBillingOrder.BillingEntity.CalculateStatus != enum.CalculateStatusSuccess {
		oldShippingFeeInfo = protocol.ThreePlAsfFeeComponent{}
	}
	oldShippingFeeInfo = oldShippingFeeInfo.GetWithoutRoundFeeComponent()
	// Notice: 这里要设置Resp.Detail用于后续计算CompensationFee的参数获取(调整前)
	threePLAsfBillingOrder.Resp.ThreePLAsfDetail = protocol.ThreePLAsfDetail{
		Bsf:              oldShippingFeeInfo.BasicShippingFee.PreTaxFee,
		RemoteFee:        oldShippingFeeInfo.RemoteFee.PreTaxFee,
		FuelFee:          oldShippingFeeInfo.FuelFee.PreTaxFee,
		InsuranceFee:     oldShippingFeeInfo.InsuranceFee.PreTaxFee,
		CodFee:           oldShippingFeeInfo.CodFee.PreTaxFee, // 根据是否满足cod service fee条件计算
		VatFee:           oldShippingFeeInfo.VatFee.TotalFee,  // 注意：根据rate table配置的CodServiceFeeRule决定是否包含cod tax
		PickupServiceFee: oldShippingFeeInfo.PickupServiceFee.PreTaxFee,
		FeeAdjustment:    oldShippingFeeInfo.AdjustmentFee.PreTaxFee,
		RiskFee:          oldShippingFeeInfo.RiskFee.PreTaxFee,
		AdditionalFee:    oldShippingFeeInfo.AdditionalFee.PreTaxFee,
		FestiveFee:       oldShippingFeeInfo.FestiveFee.PreTaxFee,
		ExtraCharges:     oldShippingFeeInfo.ExtraCharges.PreTaxFee,
	}

	if threePLAsfBillingOrder.FixedFeeAmount() ||
		threePLAsfBillingOrder.BillingEntity.CalculateStatus != enum.CalculateStatusSuccess ||
		req.PayableRule == nil {
		return threePLAsfBillingOrder, nil
	}

	//remark字段调整之后也需要保留
	rc := rounding.GetRoundingComponentCfg(ctx, enum.Country(threePLAsfBillingOrder.GetBillingEntity().Country), threePLAsfBillingOrder.GetFeeType(), enum.TotalFeeRoundingComponent, enum.ServiceType(threePLAsfBillingOrder.GetBillingEntity().ServiceType))
	remark := threePLAsfBillingOrder.CalculateInfo.ShippingInfo.ExtraCharges.Remark
	newShippingFeeInfo := GetThreePlAsfPayableDetail(ctx, *(req.PayableRule), oldShippingFeeInfo, req.AddCodFlag, remark, rc)

	threePLAsfBillingOrder.BillingEntity.TotalFee = newShippingFeeInfo.TotalShippingFee
	threePLAsfBillingOrder.CalculateInfo.ShippingInfo = newShippingFeeInfo
	//for safety
	if threePLAsfBillingOrder.Resp != nil {
		// 兼容老的LostOrderTag字段
		if req.ExceptionType == enum.LostException {
			threePLAsfBillingOrder.Resp.LostOrderTag = true
			threePLAsfBillingOrder.ExtraData.LostOrderTag = true
		}
		threePLAsfBillingOrder.Resp.ExceptionType = req.ExceptionType
		threePLAsfBillingOrder.ExtraData.ExceptionType = req.ExceptionType
	}
	threePLAsfBillingOrder.ExtraData.IsAdjust = true
	threePLAsfBillingOrder.ExtraData.DPVersion = rc.GetVersion()
	threePLAsfBillingOrder.BillingEntity.CalculateType = req.CalculateType.ToInt64()
	threePLAsfBillingOrder.SetBizOrder(req.BizOrder)
	_, billingOrder, sscError := l.orderService.UpsertBillingOrder(ctx, threePLAsfBillingOrder, threePLAsfBillingOrder, nil, true, req.ReReconFlag)
	if sscError != nil {
		return nil, sscError
	}
	threePLAsfBillingOrder = billingOrder.(*billing_order.ThreePlAsfBillingOrder)
	l.processBillingOrderPush(ctx, threePLAsfBillingOrder, threePLAsfBillingOrder, req.BizOrder)

	return threePLAsfBillingOrder, nil
}

// GetThreePlAsfPayableDetail 获得真正需要给的明细费用
func GetThreePlAsfPayableDetail(ctx dbhelper.BmsContext, rule dto2.CompensationPayableRule, info protocol.ThreePlAsfFeeComponent, addCodFlag bool, remark string, rc *rounding.ComponentRoundingCfg) protocol.ThreePlAsfFeeComponent {
	component := protocol.ThreePlAsfFeeComponent{}
	// SCFS-3527 即使不配置BitBsf，bsf保留taxRate 保证BR AdditionalFees在配置的时候能拿到BSF的taxRate xingchen.hu
	component.BasicShippingFee.TaxRate = info.BasicShippingFee.TaxRate
	for i := 0; i < 32; i++ {
		bit := enum.AdjustmentBit(i)
		if rule.Has(bit) {
			switch bit {
			case enum.AdjustmentBitBsf:
				component.BasicShippingFee = info.BasicShippingFee
			case enum.AdjustmentBitInsuranceFee:
				component.InsuranceFee = info.InsuranceFee
			case enum.AdjustmentBitCodServiceFee:
				// 根据CodServiceFeeRule决定是否添加cod
				if addCodFlag {
					component.CodFee = info.CodFee
				}
			case enum.AdjustmentBitRemoteFee:
				component.RemoteFee = info.RemoteFee
			case enum.AdjustmentBitFuelFee:
				component.FuelFee = info.FuelFee
			case enum.AdjustmentBitPickupServiceFee:
				component.PickupServiceFee = info.PickupServiceFee
			case enum.AdjustmentBitAdditionalFee:
				//component.AdditionalFee = info.AdditionalFee
				// BR迁移中对该子项进行拆分，需要合并处理
				component.AdditionalFee = protocol.FeeItem{
					IsConfig: info.AdditionalFee.IsConfig || info.RiskFee.IsConfig,
					FeeDetail: protocol.FeeDetail{
						PreTaxFee: info.AdditionalFee.PreTaxFee + info.RiskFee.PreTaxFee,
						TaxFee:    info.AdditionalFee.TaxFee + info.RiskFee.TaxFee,
						TotalFee:  info.AdditionalFee.TotalFee + info.RiskFee.TotalFee,
						TaxRate:   info.BasicShippingFee.TaxRate, // 该extraFee只有BR使用，返回bsf的税率
					},
				}
			case enum.AdjustmentBitFestiveFee:
				component.FestiveFee = info.FestiveFee
			case enum.AdjustmentBitExtraCharges:
				component.ExtraCharges = info.ExtraCharges
			}
		}
	}

	component.ExtraCharges.Remark = remark

	component.VatFee = info.VatFee
	component.VatFee.TotalFee = 0
	// ===========
	component.TotalShippingFee = component.BasicShippingFee.TotalFee + component.InsuranceFee.TotalFee + component.CodFee.TotalFee + component.RemoteFee.TotalFee + component.FuelFee.TotalFee + component.AdditionalFee.TotalFee + component.PickupServiceFee.TotalFee + component.AdjustmentFee.TotalFee + component.FestiveFee.TotalFee + component.ExtraCharges.TotalFee
	component.VatFee.TotalFee = component.BasicShippingFee.TaxFee + component.InsuranceFee.TaxFee + component.CodFee.TaxFee + component.RemoteFee.TaxFee + component.FuelFee.TaxFee + component.AdditionalFee.TaxFee + component.PickupServiceFee.TaxFee + component.AdjustmentFee.TaxFee + component.FestiveFee.TaxFee + component.ExtraCharges.TaxFee
	component.TotalShippingFeeBeforeAdjustment.PreTaxFee = component.BasicShippingFee.PreTaxFee + component.InsuranceFee.PreTaxFee + component.CodFee.PreTaxFee + component.RemoteFee.PreTaxFee + component.FuelFee.PreTaxFee + component.AdditionalFee.PreTaxFee + component.PickupServiceFee.PreTaxFee + component.AdjustmentFee.PreTaxFee + component.FestiveFee.PreTaxFee + component.ExtraCharges.PreTaxFee
	component.TotalShippingFeeBeforeAdjustment.TotalFee = component.TotalShippingFee
	component.TotalShippingFeeBeforeAdjustment.TaxFee = component.VatFee.TotalFee
	// ===========
	component.TotalShippingFee = rc.SumDecimalFeeRoundingByStandardConfig(ctx, component.TotalShippingFee, addCodFlag, component.GetTotalFee)
	component.VatFee.TotalFee = rc.SumDecimalFeeRoundingByStandardConfig(ctx, component.VatFee.TotalFee, addCodFlag, component.GetTaxFee)
	component.TotalShippingFeeBeforeAdjustment.PreTaxFee = rc.SumDecimalFeeRoundingByStandardConfig(ctx, component.TotalShippingFeeBeforeAdjustment.PreTaxFee, addCodFlag, component.GetPreTaxTotalFeeBeforeAdjustment)
	component.TotalShippingFeeBeforeAdjustment.TotalFee = rc.SumDecimalFeeRoundingByStandardConfig(ctx, component.TotalShippingFeeBeforeAdjustment.TotalFee, addCodFlag, component.GetTotalFeeBeforeAdjustment)
	component.TotalShippingFeeBeforeAdjustment.TaxFee = component.VatFee.TotalFee

	return component
}
