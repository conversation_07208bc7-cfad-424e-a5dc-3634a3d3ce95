// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package billing_status_api

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/service"
)

// Injectors from wire.go:

func NewBillingStatusUpdateController() *billingStatusHttpController {
	reconOrderService := service.NewReconOrderService()
	threePLBizOrderService := service.NewThreePlBizOrderService()
	orderService := service.InitOrderService()
	referenceNumMappingService := service.NewReferenceNumMappingService()
	billing_status_apiBillingStatusHttpController := &billingStatusHttpController{
		reconOrderService:          reconOrderService,
		bizOrderService:            threePLBizOrderService,
		orderService:               orderService,
		referenceNumMappingService: referenceNumMappingService,
	}
	return billing_status_apiBillingStatusHttpController
}
