package decouple_rpc_job

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	decouple_controller2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/application/coreapi/api/sls/decouple_controller"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/application/task_api/rpc_job/decouple_rpc_job/decouple_rpc_job_schema"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/application/task_api/rpc_job/rpc_job_template"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

type DecoupleCompareRpcJob struct {
	ctl decouple_controller2.IDecoupleComparator
}

var _ rpc_job_template.ICommonRpcJobHandler = &DecoupleCompareRpcJob{}

func (j *DecoupleCompareRpcJob) HandleJob(ctx context.Context, req interface{}) *retcode.SSCError {
	j.ctl.CompareDecoupleOrder(ctx, &decouple_controller2.CompareDecoupleOrderRequest{
		TimePivot: recorder.Now(ctx).Unix(),
	})

	return nil
}

func (j *DecoupleCompareRpcJob) JobName() string {
	return decouple_rpc_job_schema.DecoupleCompareRpcJobName
}

func (j *DecoupleCompareRpcJob) JobRequestSchema() interface{} {
	return &decouple_rpc_job_schema.DecoupleCompareRpcJobRequest{}
}

func (j *DecoupleCompareRpcJob) DecodeFromParams(ctx context.Context, args saturn.JobArgs, to interface{}) error {
	return nil
}

func NewDecoupleCompareRpcJob() rpc_job_template.ICommonRpcJobHandler {
	decoupleController := decouple_controller2.NewDecoupleController()
	decoupleCompareRpcJob := &DecoupleCompareRpcJob{
		ctl: decoupleController,
	}
	return decoupleCompareRpcJob
}
