// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package deduction_fee

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/service/deduction_fee"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/service/rate_table"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_common_rate"
)

// Injectors from wire.go:

func NewDeductionFeeController() *deductionFeeController {
	rateTableService := rate_table.NewRateTableService()
	deductionFeeService := deduction_fee.NewDeductionFeeService()
	spxCommonRateService := spx_common_rate.NewSpxCommonRateService()
	deduction_feeDeductionFeeController := &deductionFeeController{
		rateTableService:     rateTableService,
		deductionFeeService:  deductionFeeService,
		spxCommonRateService: spxCommonRateService,
	}
	return deduction_feeDeductionFeeController
}
