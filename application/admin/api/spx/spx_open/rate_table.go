/**
    @author:qingping
    @since:2023/5/22
    @desc: //TODO
**/

package spx_open_controller

import (
	"fmt"
	"strconv"
	"strings"

	utils "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/utils/common"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_request"
	dto3 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/dto"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_response/open_fee_response"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_core/protocol"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/dto"
	dto2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/dto"
	entity2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/entity"
	vo2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_rule/vo"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/dto/fee_type_calculate_dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/constant"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

type rateTableAdmin interface {
	CreateRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError)
	UpdateRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError)
	ListRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError)
	GetRateTableByRateId(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError)
	DeleteRateTableByRateId(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError)
	CopyRateTable(context *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError)
	GetExtraFeeDetailByRateId(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError)
	TestRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError)
	DisableRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError)
	GetRateTableHistoryById(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError)
	DeactivateRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError)
}

func (s *SpxOpenAdminController) DisableRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError) {
	disableReq := req.(*dto.DisableRateIdReq)
	if disableReq.RateId != 0 {
		return nil, s.spxOpenAdminService.DisableRateTable(ctx, disableReq.RateId)
	}
	if strings.EqualFold(disableReq.Region, ctx.GetCountry()) {
		return nil, retcode.DisableRateCardError.CloneWithDetail("your region is not correctly")
	}
	return nil, s.spxOpenAdminService.DisableRateTableByMerchantTypeId(ctx, disableReq.MerchantTypeId)
}

func (s *SpxOpenAdminController) GetRateTableHistoryById(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError) {
	historyReq := req.(*dto.RateTableHistoryReq)
	resp, err := s.spxOpenAdminService.GetRateTableHistoryById(ctx, historyReq)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *SpxOpenAdminController) GetExtraFeeDetailByRateId(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError) {
	getReq := req.(*dto.GetExtraFeeConfigDetailByRateIdReq)
	rateID := uint64(getReq.RateId)
	res := dto.GetExtraFeeConfigDetailByRateIdResp{}

	codFee, err := s.extraFeeAdminService.GetExtraFeeFee(ctx, dto.QueryExtraFeeRequestV2{RateID: rateID})
	if err != nil {
		return nil, err
	}
	if codFee == nil {
		res.FuelFeeDetail = struct{}{}
	} else {
		res.CodServiceFeeDetail = codFee
	}
	fuelFee, err := s.fuelFeeService.GetFuelFeeDetail(ctx, rateID)
	if err != nil {
		return nil, err
	}
	if fuelFee == nil {
		res.FuelFeeDetail = struct{}{}
	} else {
		res.FuelFeeDetail = dto.FuelFeeDetail{Percentage: fuelFee.Percentage}
	}

	insuranceFee, err := s.extraFeeAdminService.GetExtraFeeFee(ctx, dto.QueryRangeInsuranceRequest{RateID: rateID})
	if err != nil {
		return nil, err
	}
	if insuranceFee == nil {
		res.InsuranceFeeDetail = struct{}{}
	} else {
		res.InsuranceFeeDetail = insuranceFee
	}
	rtsFee, err := s.extraFeeAdminService.GetExtraFeeFee(ctx, dto2.QuerySpxOpenRtsRequest{RateID: rateID})
	if err != nil {
		return nil, err
	}
	if rtsFee == nil {
		res.RtsFeeDetail = struct{}{}
	} else {
		res.RtsFeeDetail = rtsFee
	}

	pickupFee, pickupFeeBound, err := s.pickupServiceFeeService.SearchPickupServiceFee(ctx, rateID)
	if err != nil {
		return nil, err
	}
	var bound *enum.Bound
	if len(pickupFee) > 0 {
		bound = &pickupFeeBound
	}
	res.PickupServiceFeeDetail = dto.PickupServiceFeeDetailCareBound{
		List:      pickupFee,
		BoundRule: bound,
	}

	remoteFee, err := s.remoteAreaFeeService.GetRemoteAreaFeeDetail(ctx, rateID)
	if err != nil {
		return nil, err
	}
	if len(remoteFee) == 0 {
		res.RemoteAreaFeeDetail = struct{}{}
	} else {
		res.RemoteAreaFeeDetail = remoteFee[0]
	}

	adjustmentFee, err := s.adjustmentFeeService.GetAdjustmentFeeByRateId(ctx, rateID)
	if err != nil {
		return nil, err
	}
	if adjustmentFee == nil {
		res.AdjustmentFeeDetail = struct{}{}
	} else {
		res.AdjustmentFeeDetail = adjustmentFee
	}
	spxOpenAdditionalFeeDetail, err := s.spxOpenAdditionalFeeService.GetSpxOpenAdditionalFeeDetail(ctx, rateID)
	if err != nil {
		res.SpxOpenAdditionalFeeDetail = struct{}{}
	} else {
		res.SpxOpenAdditionalFeeDetail = spxOpenAdditionalFeeDetail
	}

	totalFeeRounding, err := s.totalFeeRoundService.GetTotalFeeRoundingByRateId(ctx, rateID)
	if err != nil {
		return nil, err
	}
	if totalFeeRounding == nil {
		res.TotalFeeRoundingDetail = struct{}{}
	} else {
		res.TotalFeeRoundingDetail = totalFeeRounding
	}

	rejectCollectionServiceFee, err := s.rejectCollectionServiceFeeService.GetRejectCollectionServiceFee(ctx, rateID)
	if err != nil {
		return nil, err
	}
	if rejectCollectionServiceFee == nil {
		res.RejectCollectionServiceFee = struct{}{}
	} else {
		res.RejectCollectionServiceFee = rejectCollectionServiceFee
	}

	return res, nil
}

func (s *SpxOpenAdminController) CopyRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError) {
	copyRateTableReq := req.(*dto.CopyRateTableReq)
	copyRateTableReq.UserGroup = ""
	resp, err := s.spxOpenAdminService.CopyRateTable(ctx, copyRateTableReq)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *SpxOpenAdminController) DeleteRateTableByRateId(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError) {
	deleteRateTableReq := req.(*dto.DeleteRateTableReq)
	resp, err := s.spxOpenAdminService.DeleteRateTableById(ctx, deleteRateTableReq)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *SpxOpenAdminController) UpdateRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError) {
	updateRateTableReq := req.(*dto.UpdateSpxOpenRateTableReq)
	rateTable, err := s.spxOpenAdminService.GetRateTableEntityByID(ctx, uint64(updateRateTableReq.RateId))
	openTable, ok := rateTable.(*entity2.SpxOpenRateTable)
	if !ok || err != nil || openTable == nil {
		return nil, retcode.RateTableUpdateError.CloneWithDetail("get spx open rate table entity error")
	}
	// 已经生效的费率表不允许提交
	if openTable.GetRateTableStatus() == enum.Submit {
		return nil, retcode.RateTableUpdateError.CloneWithDetail("rate table is effective")
	}
	//提交时校验route状态
	if updateRateTableReq.Submit != nil && *updateRateTableReq.Submit {
		if err := s.validateRateWithRoute(ctx, uint64(openTable.RouteId)); err != nil {
			return struct {
				ErrorCode int32 `json:"error_code"`
			}{ErrorCode: int32(err.RetCode)}, nil
		}
		return s.spxOpenAdminService.UpdateSubmitRateTable(ctx, updateRateTableReq, openTable)
	}
	// 只允许草稿态编辑route id
	if updateRateTableReq.RouteId != nil {
		openTable.RouteId = *updateRateTableReq.RouteId
	}
	return s.spxOpenAdminService.UpdateNonSubmitRateTable(ctx, updateRateTableReq, openTable)
}

func (s *SpxOpenAdminController) validateRateWithRoute(ctx *dbhelper.HttpContext, routeId uint64) *retcode.SSCError {
	routeConfig, err := s.routeService.DetailRouteBasicInfo(ctx, dto.DetailRouteGroupReq{
		RouteGroupId: routeId,
	})
	if err != nil {
		return err
	}
	if routeConfig.GroupStatus == enum.RouteGroupDisabled {
		return retcode.DisabledRouteGroupError.CloneWithDetail("route_id is disabled")
	}
	return nil
}

func (s *SpxOpenAdminController) GetRateTableByRateId(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError) {
	getRateTableReq := req.(*dto.RateTableDetailReq)
	resp, err := s.spxOpenAdminService.GetRateTableById(ctx, getRateTableReq)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *SpxOpenAdminController) CreateRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError) {
	createRateTableReq := req.(*dto.CreateSpxOpenRateTableReq)
	billingRule, err := s.billingRuleService.Detail(ctx, 0, enum.SpxOpenASF, createRateTableReq.ServiceId)
	if err != nil {
		return nil, retcode.RateTableCreateError.CloneWithError(err.NewError())
	}
	defaultBillingRuleConfig := &vo2.SpxOpenShippingFeeRuleConfig{
		ShippingFeeRule:         billingRule.SpxOpenShippingFeeRule.ShippingFeeRule,
		CalculateByMerchantType: billingRule.SpxOpenShippingFeeRule.CalculateByMerchantType,
		SpxOpenWeightRuleConfig: vo2.SpxOpenWeightRuleConfig{
			WeightRule:                   billingRule.SpxOpenShippingFeeRule.WeightRule,
			VolumetricWeightIncluded:     billingRule.SpxOpenShippingFeeRule.VolumetricWeightInclude,
			VolumetricFactor:             int32(billingRule.SpxOpenShippingFeeRule.VolumetricFactor),
			ChargeableWeightFormula:      billingRule.SpxOpenShippingFeeRule.ChargeableWeightFormula,
			ActualWeightPriorityRule:     billingRule.SpxOpenShippingFeeRule.ActualWeightPriorityRule,
			VolumetricWeightPriorityRule: billingRule.SpxOpenShippingFeeRule.VolumetricPriorityRule,
		},
		SpxOpenDimensionConfig: vo2.SpxOpenDimensionConfig{
			PriorityRule: billingRule.SpxOpenShippingFeeRule.SizePriorityRule,
		},
	}
	createRateTableReq.ServiceInfo.SpxOpenShippingFeeRuleConfig = defaultBillingRuleConfig
	resp, err := s.spxOpenAdminService.CreateRateTable(ctx, &dto.CreateRateTableReq{
		BusinessModelConfig: createRateTableReq.BusinessModelConfig,
		ServiceInfo:         createRateTableReq.ServiceInfo,
		FeeComponentConfig:  createRateTableReq.FeeComponentConfig,
		FeeTypeId:           enum.SpxOpenASF,
	})
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *SpxOpenAdminController) ListRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError) {
	listRateTableReq := req.(*dto.ListSpxOpenRateTableReq)
	finalList := make([]dto.SpxOpenRateTableItem, 0)
	resp, err := s.spxOpenAdminService.ListRateTable(ctx, &dto.ListRateTableReq{
		FeeTypeId:           enum.SpxOpenASF,
		ServiceId:           listRateTableReq.ServiceId,
		RateId:              listRateTableReq.RateId,
		MerchantTypeIdList:  listRateTableReq.MerchantTypeIdList,
		RateStatus:          listRateTableReq.RateStatus,
		StartTimeLowerBound: listRateTableReq.StartTimeLowerBound,
		StartTimeUpperBound: listRateTableReq.StartTimeUpperBound,
		Offset:              listRateTableReq.Offset,
		Size:                listRateTableReq.Size,
	})
	if err != nil {
		return finalList, err
	}
	for i := range resp.List {
		finalList = append(finalList, dto.SpxOpenRateTableItem{
			ServiceId:            resp.List[i].ServiceId,
			RateId:               resp.List[i].RateId,
			RateStatus:           resp.List[i].RateStatus,
			FeeComponent:         resp.List[i].FeeComponent,
			BasicShippingFeeRule: resp.List[i].BasicShippingFeeRule,
			StartTime:            resp.List[i].StartTime,
			Operator:             resp.List[i].Operator,
			Ctime:                resp.List[i].Ctime,
			Mtime:                resp.List[i].Mtime,
			MerchantType:         *resp.List[i].MerchantType,
		})
	}
	return dto.ListSpxOpenRateTableRsp{
		Offset: listRateTableReq.Offset,
		Size:   listRateTableReq.Size,
		List:   finalList,
		Total:  resp.Total,
	}, nil
}

func (s *SpxOpenAdminController) TestRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError) {
	// 计算运费的时候，指定使用 DB Store
	ctx.Set(constant.StoreType, enum.DBStore)
	testReq := req.(*dto.RateTableTestReq)
	rateTable, err := s.spxOpenAdminService.GetRateTableEntityByID(ctx, testReq.RateID)
	if err != nil {
		return nil, err
	}
	if rateTable.GetBasicShippingFeeRule() == enum.OrderWeight {
		if testReq.WeightInfo.ChargeableWeight <= 0 {
			return nil, retcode.RequestTransferError.CloneWithDetail("actual_weight must be positive")
		}
	} else if rateTable.GetBasicShippingFeeRule() == enum.OrderDimension && ctx.GetCountry() == enum.TW.StringView() {
		if testReq.SizeId < 1 || testReq.SizeId > 4 {
			return nil, retcode.RequestTransferError.CloneWithDetail("size_id must be 1/2/3/4")
		}
	}
	productId, rowErr := strconv.ParseInt(rateTable.GetServiceId(), 10, 64)
	if rowErr != nil {
		return nil, retcode.RequestTransferError.CloneWithError(rowErr)
	}
	calculateReq, err := s.transferCalculateRequest(ctx, productId, testReq)
	if err != nil {
		logger.CtxLogErrorf(ctx, "transfer calculate request:%+v.", err)
		return nil, retcode.RequestTransferError.CloneWithError(fmt.Errorf("transfer calculate request err: %v", err))
	}
	calculateReq.SetRateTable(rateTable)
	calculate, calculateErr := s.calculateHandler.Calculate(ctx, calculateReq)
	return open_fee_response.GetSpxOpenShippingFeeResp(ctx, calculateReq, calculate, calculateErr), calculateErr
}

func (s *SpxOpenAdminController) transferCalculateRequest(ctx *dbhelper.HttpContext, productId int64, testReq *dto.RateTableTestReq) (calculate_request.ICalculateRequest, *retcode.SSCError) {
	return s.reqTransfer.TransferCalculateRequest(ctx, enum.SpxOpenASF, &fee_type_calculate_dto.SpxOpenCalculateShippingDTO{
		BaseCalculateShippingFeeDTO: dto3.BaseCalculateShippingFeeDTO{},
		RowReq: &protocol.CalOpenShippingFeeBaseSchema{
			ProductID: productId,
			OpenWight: protocol.OpenWight{
				OrderWeight: int32(testReq.WeightInfo.ChargeableWeight),
			},
			OpenSize: protocol.OpenSize{
				OrderHeight: int32(testReq.WeightInfo.Height),
				OrderWidth:  int32(testReq.WeightInfo.Width),
				OrderLength: int32(testReq.WeightInfo.Length),
				SizeId:      testReq.SizeId,
			},
			MerchantId:          testReq.OrderInfo.AccountId,
			CodAmount:           testReq.OrderInfo.CodAmount,
			PickupLocationIDs:   testReq.Origin.LocationId,
			DeliveryLocationIDs: testReq.Destination.LocationId,
			ExpressInsuredValue: testReq.OrderInfo.Cogs,
			PickupPostcode:      testReq.Origin.Postcode,
			DeliveryPostcode:    testReq.Destination.Postcode,
			PickupInfo:          &protocol.AddressInfoSchema{},
			DeliveryInfo:        &protocol.AddressInfoSchema{},
			OrderCreateTime:     utils.GetTimestamp(ctx),
			WeightSizeInfo: protocol.OpenWeightSizeInfo{
				OpenWeightInfo: protocol.OpenWeightInfo{
					SellerWeight: testReq.WeightInfo.ChargeableWeight,
				},
				OpenSizeInfo: protocol.OpenSizeInfo{
					SellerSize: protocol.OpenSizeItem{
						Length: testReq.WeightInfo.Length,
						Width:  testReq.WeightInfo.Width,
						Height: testReq.WeightInfo.Height,
						SizeId: testReq.SizeId,
					},
				},
			},
			VasInfo: protocol.VasInfo{
				VasTypes:                          []string{"1"},
				CollectFeeAmount:                  0,
				RecipientRejectsCollectServiceTag: enum.RecipientRejectsCollectServiceTrueTag,
			},
		},
		TestRateTableId: testReq.RateID,
		TestFlag:        true,
	})
}

// DeactivateRateTable 停用upcoming费率表
func (s *SpxOpenAdminController) DeactivateRateTable(ctx *dbhelper.HttpContext, req interface{}) (interface{}, *retcode.SSCError) {
	deactivateRateTableReq := req.(*dto.DeactivateRateTableReq)

	err := s.spxOpenAdminService.DeactivateRateTable(ctx, deactivateRateTableReq)
	if err != nil {
		return nil, err
	}

	return &dto.DeactivateRateTableResp{
		RateId: deactivateRateTableReq.RateId,
	}, nil
}
