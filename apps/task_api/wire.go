//go:generate wire
//go:build wireinject
// +build wireinject

package task_api

import (
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/address_revision_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_info"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/branch/branch_task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/aggregate_masked_channel_cdt"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_data"
	auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/automated_volume_generation_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/cdt_ab_test"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_forecast_task"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_history"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_rule_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/edd_update_task_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/lane_manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_manipulation_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/cdt_management/product_ctime"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/line_toggle"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/location_whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/mesh"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/e_fence/polygon"
	edt_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/google_result_cache_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/google_result_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/hd_distance_cache_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/hd_distance_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/lps_holiday"
	parcel_library2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/package_limit/parcel_library"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/pickup_group"
	timeslot2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/pickup_configuration/timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/postal_code_to_geo"
	task_configuration2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sa_task/task_configuration"
	task_record2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sa_task/task_record"
	scheduled2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/service_point_geo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/area"
	operation_route2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/area_route_serviceable/route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_conf"
	basic_location2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/basic_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/basic_serviceable/collect_deliver_group_ref"
	card_delivery_address2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/card_delivery_address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/common_serviceable/scenario_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_conf"
	operation_location2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_location"
	operation_postcode2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/serviceable_area/operation_serviceable/operation_postcode"
	siteBasicConfSiteServiceableAreaBasicConf "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_basic_conf"
	siteServiceableLocationSiteServiceableAreaLocation "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/site_serviceable_area/site_serviceable_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sls_holiday"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/sls_opa"
	order_account_mapping2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_compare"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/spx_serviceable_area/spx_serviceable_area_version"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/hd_station_cache"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station/station_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/station_buyer_repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/model/tpl_id_line_id_ref"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/branch"
	autoUpdateDataAutoUpdateRule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/auto_update_rule"
	automated_volume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/automated_volume"
	cdt_ab_test2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_ab_test"
	cdtCalculationAutoUpdateRule "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/cdt_calculation"
	edd_auto_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_auto_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/edd_delay_queue"
	manual_update_rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/cdt_management/manual_update_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/e_fence"
	edt_rule3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/edt_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/map_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/match"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/package_limit/parcel_library"
	pickupConfigPickupConfiguration "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/pickup_config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/pickup_configuration/timeslot"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/sa_task/task_configuration"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/sa_task/task_record"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/scheduled"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/area_route_serviceable"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_cep_range"
	basic_conf2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_conf"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_location"
	basic_postcode2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/basic_postcode"
	collect_deliver_group2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/basic_serviceable/collect_deliver_group"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/card_delivery_address"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_cep_range"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_postcode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/serviceable_area/operation_serviceable/operation_route"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/site_serviceable_area/site_serviceable_area_location"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/sls_opa_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/order_account_mapping"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area"
	spx_serviceable_area_compare2 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/spx_serviceable_area/spx_serviceable_area_compare"
	station3 "git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/service/station"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/lcs_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/s3_service"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/third_party/spex_service"
	"github.com/google/wire"
)

func InitTaskService() *TaskService {
	wire.Build(
		wire.Struct(new(TaskService), "*"),
		auto_update_rule.CdtAutoUpdateRuleDAOProviderSet,
		auto_update_rule2.CdtAutoUpdateRuleDAOProviderSet,
		auto_update_data.CdtAutoUpdateDataDAOProviderSet,
		lane_auto_update_data.CdtAutoUpdateDataDAOProviderSet,
		manual_update_rule.CdtAutoUpdateRuleDAOProviderSet,
		lane_manual_update_rule.CdtAutoUpdateRuleDAOProviderSet,
		product_ctime.ProductCtimeDAOProviderSet,
		edd_auto_update_rule.EddAutoUpdateRuleDaoProviderSet,
		cdt_ab_test.CdtAbTestRuleDaoProviderSet,
		autoUpdateDataAutoUpdateRule.CdtAutoUpdateDataDAOProviderSet,
		manual_update_rule2.CdtManualUpdateRuleDAOProviderSet,
		cdtCalculationAutoUpdateRule.CdtCalculationServiceProviderSet,
		manual_manipulation_rule.CdtManualManipulationRuleDAOProviderSet,
		lane_manual_manipulation_rule.CdtManualManipulationRuleDAOProviderSet,
		sls_holiday.SlsHolidayDAOProviderSet,
		lps_holiday.LPSHolidayDAOProviderSet,
		tpl_id_line_id_ref.TPLIDLineIDRefDAOProviderSet,
		edd_history.EDDHistoryDAOProviderSet,
		cdt_ab_test2.CdtAbTestRuleServiceProviderSet,
		aggregate_masked_channel_cdt.AggregateMaskedChannelCdtProviderSet,
		automated_volume_generation_data.AutomatedVolumeGenerationDataProviderSet,
		automated_volume.AutomatedVolumeGenerationRuleProviderSet,
		task_configuration.SATaskConfigurationServiceProviderSet,
		task_configuration2.LogisticSATaskConfigruationProviderSet,
		basic_postcode.LineBasicServiceablePostcodeProviderSet,
		task_record.SATaskRecordServiceProviderSet,
		task_record2.LogisticSATaskRecordDaoProviderSet,
		s3_service.S3ServiceProviderSet,
		basic_location.LineBasicServiceableLocationServiceProviderSet,
		basic_location2.LineBasicServiceableLocationProviderSet,
		scheduled.ScheduledServiceProviderSet,
		scheduled2.ScheduledDaoSet,
		basic_conf.LineBasicServiceableConfProviderSet,
		order_account_mapping.OrderAccountMappingProviderSet,
		order_account_mapping2.OrderAccountMappingDaoProviderSet,
		spx_serviceable_area.SpxServiceableAreaServiceProviderSet,
		spx_serviceable_area_version.SpxServiceableAreaVersionDaoProvierSet,
		spx_serviceable_area_data.SpxServiceableAreaDataDaoProviderSet,
		spx_serviceable_area_compare.SpxServiceableAreaCompareTaskDaoProviderSet,
		card_delivery_address.CardDeliveryAddressServiceProviderSet,
		card_delivery_address2.CardDeliveryAddressChangeVersionDaoProviderSet,
		basic_postcode2.LineBasicServiceablePostcodeServiceProviderSet,
		basic_cep_range.LineBasicServiceableCepRangeServiceProviderSet,
		cep_range.LineBasicServiceableCepRangeProviderSet,
		operation_location.LineOperationServiceableLocationServiceProviderSet,
		operation_location2.LogisticLineOperationServiceableLocationTabProviderSet,
		operation_postcode.LineOperationServiceablePostcodeServiceProviderSet,
		operation_postcode2.LogisticLineOperationServiceablePostcodeTabProviderSet,
		operation_cep_range.LineOperationServiceableCepRangeServiceProviderSet,
		area_route_serviceable.LineServiceableRouteServiceProviderSet,
		route.LogisticLineServiceableRouteTabProviderSet,
		area.LogisticLineServiceableAreaTabProviderSet,
		collect_deliver_group.LineCollectDeliverGroupConfProviderSet,
		operation_route.LineOperationRouteServiceProviderSet,
		operation_route2.LineOperationRouteTabProviderSet,
		lcs_service.LcsServiceProviderSet,
		branch.BranchServiceProviderSet,
		branch_info.BranchDaoProviderSet,
		branch_group.BranchGroupDaoProviderSet,
		spex_service.SpexServiceProviderSet,
		postal_code_to_geo.BranchGroupDaoProviderSet,
		branch_task_record.BranchTaskRecordDaoProviderSet,
		station_conf.LogisticStationDaoProviderSet,
		edd_update_rule_conf.EDDUpdateRuleConfDAOProviderSet,
		edd_update_task_conf.EDDUpdateTaskConfDAOProviderSet,
		edd_forecast_task.EDDForecastTaskProviderSet,
		edd_auto_update_rule2.EDDAutoUpdateRuleServiceProviderSet,
		edd_delay_queue.EddWaybillDelayQueueProviderSet,
		spx_serviceable_area_compare2.SpxServiceableAreaCompareServiceProviderSet,
		basic_conf2.LineBasicServiceableConfManagerProviderSet,
		scenario_conf.LineCommonServiceableScenarioConfProviderSet,
		operation_conf.LogisticLineOperationServiceableConfTabProviderSet,
		collect_deliver_group_ref.LineBasicServiceableGroupRefProviderSet,
		collect_deliver_group2.CollectDeliverGroupServiceProviderSet,
		automated_volume2.AutomatedVolumeGenerationRuleServiceProviderSet,
		station3.StationServiceProviderSet,
		match.HomeDeliveryServiceProviderSet,
		hd_distance_repo.HdDistanceDaoProviderSet,
		hd_distance_cache_repo.HdDistanceCacheDaoProviderSet,
		service_point_geo.ServicePointDaoProviderSet,
		map_service.MapServiceProviderSet,
		google_result_repo.GoogleResultDaoProviderSet,
		google_result_cache_repo.GoogleResultCacheDaoProviderSet,
		station_buyer_repo.StationBuyerProviderSet,
		address_revision_repo.AddressRevisionDaoProviderSet,
		hd_station_cache.LogisticStationCacheProviderSet,
		station3.StationVolumeServiceProviderSet,
		station3.StationRedisCounterProviderSet,
		e_fence.EFenceServiceSet,
		polygon.EFencePolygonDaoSet,
		mesh.EFenceMeshDaoSet,
		location_whitelist.EFenceLocationWhiteListDaoSet,
		line_toggle.EFenceLineToggleDaoDaoSet,
		site_serviceable_area_location.SiteServiceableAreaLocationServiceProviderSet,
		siteBasicConfSiteServiceableAreaBasicConf.LogisticSiteServiceableAreaBasocConfDaoProviderSet,
		siteServiceableLocationSiteServiceableAreaLocation.LogisticSiteServiceableAreaCepRangeDaoProviderSet,
		pickupConfigPickupConfiguration.LinePickupConfigServiceProviderSet,
		pickup_config.PickupConfigDAOProviderSet,
		pickup_group.PickupGroupDAOProviderSet,
		timeslot.PickupTimeslotServiceProviderSet,
		timeslot2.PickupTimeslotDAOProviderSet,
		edt_rule3.EdtRuleServiceProviderSet,
		edt_rule2.EdtRuleRepoProviderSet,
		edt_rule3.ValidatorManagerProviderSet,
		sls_opa_service.SlsOpaServiceProviderSet,
		sls_opa.SlsOpaDAOProviderSet,
		parcel_library.LogisticParcelLibraryServiceProviderSet,
		parcel_library2.LogisticParcelLibraryDaoProviderSet,
	)

	return nil
}
