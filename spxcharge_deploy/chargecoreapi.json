{"#": "for a detail explaination of this file.", "project_name": "spxcharge", "module_name": "core", "build": {"commands": [], "docker_image": {"base_image": "harbor.shopeemobile.com/shopee/sls-cicd-base:go1.15-0316", "squash": false, "run_commands": ["GOPROXY=''", "GO111MODULE=on go build -ldflags '-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn' -tags=jsoniter -o api_server spxcharge_cmd/coreapi/main.go"], "dependent_libraries_files": ["."], "image_language": "golang", "multi_stage_base_image": "harbor.shopeemobile.com/shopee/saturn-executor:v1.0.0", "enable_multi_stage_golang_image": true, "workdir": "/workspace", "generate_dockerignore": true}}, "run": {"acquire_prometheus_port": true, "enable_prometheus": true, "prometheus_path": "/metrics", "depend_services": [], "command": "supervisord -n -c spxcharge_deploy/supervisor/core_api.ini", "#": "The time unit for `grace_period` and `timeout` is second.", "check": {"protocol": "HTTP", "timeout": 2, "retry": 10, "max_fails": 10, "interval": 10, "endpoint": "/ping", "grace_period": 15}, "smoke": {"protocol": "HTTP", "timeout": 2, "retry": 10, "max_fails": 10, "interval": 10, "endpoint": "/ping", "grace_period": 600}, "liveness": {"protocol": "HTTP", "timeout": 2, "retry": 60, "max_fails": 60, "interval": 10, "endpoint": "/ping", "grace_period": 600}}, "deploy": {"idcs": {"live": {"sg": ["sg", "sg1"], "my": ["sg", "sg1"], "th": ["sg", "sg1"], "ph": ["sg", "sg1"], "vn": ["sg", "sg1"], "id": ["sg", "sg1"], "tw": ["sg", "sg1"], "br": ["sg", "sg1"]}}, "resources": {"live": {"cpu": 8, "mem": 32768}, "test": {"cpu": 8, "mem": 8192}, "uat": {"cpu": 8, "mem": 8192}, "staging": {"cpu": 8, "mem": 8192}}, "instances": {"live": {"sg": 2}}}}