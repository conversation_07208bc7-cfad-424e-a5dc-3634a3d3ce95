//go:generate wire
//go:build wireinject
// +build wireinject

package grpc_api

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/application/checkout/grpc_api"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/application/coreapi/grpc_api/health"
	"github.com/google/wire"
)

func InitCheckoutGRPCServer() *CheckoutGRPCServer {
	wire.Build(
		wire.Struct(new(CheckoutGRPCServer), "*"),
		health.HealthServiceProviderSet,
		grpc_api.EsfControllerProviderSet,
		grpc_api.BaseInfoControllerProviderSet,
		grpc_api.AllocationSFControllerProviderSet,
		grpc_api.ThirdPartyForecastSFControllerProviderSet,
		grpc_api.EsfRecalculateControllerSet,
	)
	return nil
}
