package oms

import (
	"context"

	"git.garena.com/shopee/marketplace-logistics/fulfilment_preview/modules/spex/account"
)

type OMSApi interface {
	GetOMSWarehousePriority(ctx context.Context, shopId int64, whIds []string,
		userDeliveryAddress *account.BuyerDefaultDeliveryAddress, mapShopIdToIsPFFShop map[int64]bool) (map[string]int32, error)
	GetOMSWarehousePriorityV2(ctx context.Context, shopId int64, whIds []string,
		userDeliveryAddress *account.BuyerDefaultDeliveryAddress) (map[string]int32, error)
}
