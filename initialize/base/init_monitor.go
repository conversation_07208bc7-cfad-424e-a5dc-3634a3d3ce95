package base

import (
	"context"
	"fmt"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/config"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/monitoring"
)

func InitMonitor(ctx context.Context, conf *config.Config) error {
	if err := monitoring.InitMonitor(conf.EnvConfig); err != nil {
		return fmt.Errorf("InitMonitor fail, err = %v config=%+v", err, conf)
	}
	return nil
}
