package initialize

import (
	"context"

	"git.garena.com/shopee/bg-logistics/go/chassis"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/config"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/initialize/base"
	checkout_api "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/interfaces/checkout/api"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/interfaces/checkout/grpc_api"
)

func CheckoutStart(ctx context.Context, conf *config.Config) error {
	initFunc := []func(ctx context.Context, conf *config.Config) error{
		base.InitLog,
		//注释掉这里，为了兼容 checkout 链路 br sg11 cat domain sg11 后缀, chassis框架默认自己会启动cat
		//base.InitMonitor,
		base.InitSysConfig,
		base.InitFinancePublicConfig,
		base.InitV2AllConfig,
		base.InitAllDb,
		base.InitChargeDb,
		base.InitRedis,
		base.InitSpex,
		base.InitCacheLoadPrometheusMetrics,
		base.InitRateDBStore,
		base.InitRateCacheStore,
		base.InitProductLimitCache,
		base.InitCheckOutMetric,
		base.InitCheckoutKafkaProducer,
		base.InitGlobalStandardProducer,
		base.InitTraffic,
		base.InitSendComparatorGoroutinePool,
		base.InitSecHttpClient,
	}
	//初始化
	for _, fc := range initFunc {
		err := fc(ctx, conf)
		if err != nil {
			return err
		}
	}

	InitPprofServer()
	RegisterCheckoutService(ctx, conf)
	return nil
}

func RegisterCheckoutService(ctx context.Context, c *config.Config) {

	// 注册http
	chassis.RegisterSchema("rest", checkout_api.InitCheckoutApiResource())

	// register grpc service
	ctx = dbhelper.NewCommonCtx(ctx)
	grpcController := grpc_api.InitCheckoutGRPCServer()
	grpcController.Register()
}
