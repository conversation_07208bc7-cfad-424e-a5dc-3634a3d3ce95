package plugin

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/entity/spx_driver_payable_entity"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/dto/fee_type_calculate_dto/spx_driver_payable_dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/service/spx_driver_payable_cost_service/driver_payable_core/schema"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_driver_payable_cost/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_driver_payable_cost/entity"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_driver_payable_cost/repository"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum/spx_driver_payable_enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/recorder"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/store"
	"github.com/shopspring/decimal"
)

// PeriodMultiplierPlugin 周期Multiplier计算匹配处理
type PeriodMultiplierPlugin struct {
	spxDriverPayableBillingPeriodRepository repository.ISpxDriverPayableBillingPeriodRepository
}

func (p *PeriodMultiplierPlugin) Process(ctx dbhelper.BmsContext, spxDriverSchema schema.ISpxDriverPayableSchema, shippingFeeDto dto.ICalculateShippingFeeDTO) *retcode.SSCError {
	// 周期Multiplier有SuccessRateMultiplier和AttendanceRateMultiplier
	spxDriverSchemaData, ok := spxDriverSchema.(*schema.SpxDriverPayableSchema)
	if !ok {
		return retcode.AssertError.CloneWithDetail("assert spxDriverSchema error")
	}

	spxDriverCalculateDTO, ok := shippingFeeDto.(*spx_driver_payable_dto.SpxDriverPayableCalculateDTO)
	if !ok {
		return retcode.AssertTypeError.AppendDetail("calculate dto is not SpxDriverPayableCalculateDTO")
	}

	startTime := recorder.Now(ctx)
	// 计算Period类型的Multiplier
	periodExtraData, sscErr := p.calculatePeriodMultiplier(ctx, spxDriverSchemaData, spxDriverCalculateDTO)
	if sscErr != nil {
		return sscErr
	}

	// 赋值到DTO
	// 当天的IncentivePoint
	spxDriverCalculateDTO.AttendanceRateMultiplier = *periodExtraData.AttendanceRateMultiplier
	spxDriverCalculateDTO.SuccessRateMultiplier = *periodExtraData.SuccessRateMultiplier

	logger.CtxLogInfof(ctx, "PeriodMultiplierPlugin handle finished, cost time: %v s", recorder.Now(ctx).Sub(startTime).Seconds())
	return nil
}

func (p *PeriodMultiplierPlugin) calculatePeriodMultiplier(ctx dbhelper.BmsContext, payableSchema *schema.SpxDriverPayableSchema, calculateDto *spx_driver_payable_dto.SpxDriverPayableCalculateDTO) (*do.ExtraData, *retcode.SSCError) {
	var (
		rateId = calculateDto.RateId
	)
	// SR Multiplier/SR Multiplier 一个计费周期只需要计算一次 已经计算过则不再重复计算
	billingPeriodEntity := payableSchema.SpxDriverPayableBillingPeriodEntity
	billingPeriodExtraData := billingPeriodEntity.ExtraData

	logger.CtxLogInfof(ctx, "calculate PeriodMultiplier Params, billingPeriodExtraData: %+v", billingPeriodExtraData)

	if billingPeriodExtraData.IsPeriodMultiplierCalculated() {
		return billingPeriodExtraData, nil
	}

	logger.CtxLogInfof(ctx, "calculate PeriodMultiplier Params, billingPeriodExtraData lack of PeriodMultiplier, start to calculate PeriodMultiplier")

	// 避免多次匹配 一口价
	routeUnrelatedConfig, sscErr := store.GetInstance(ctx.GetStoreType()).GetSpxDriverPayableRouteUnrelatedConfig(ctx, uint64(rateId))
	if sscErr != nil {
		return nil, sscErr
	}

	// 周期SuccessRateMultiplier/周期AttendanceRateMultiplier
	taskEntity := payableSchema.MatchTaskEntity
	billingPeriodExtraData, sscErr = p.matchIncentiveMultiplier4Period(ctx, payableSchema, calculateDto, taskEntity, billingPeriodExtraData, routeUnrelatedConfig)
	if sscErr != nil {
		logger.CtxLogErrorf(ctx, "calculate PeriodMultiplier Params, matchIncentiveMultiplier4Period error: %+v", sscErr)
		return nil, sscErr
	}

	// 将SR Multiplier和AR Multiplier存储起来
	sscErr = p.spxDriverPayableBillingPeriodRepository.UpdateExtraData(ctx, billingPeriodEntity.Id, billingPeriodExtraData)
	if sscErr != nil {
		return nil, sscErr
	}

	return billingPeriodExtraData, nil
}

func (p *PeriodMultiplierPlugin) matchIncentiveMultiplier4Period(ctx dbhelper.BmsContext, payableSchema *schema.SpxDriverPayableSchema, calculateDto *spx_driver_payable_dto.SpxDriverPayableCalculateDTO, taskEntity *entity.SpxDriverPayableTaskEntity, extraData *do.ExtraData, routeUnrelatedConfig *entity.SpxDriverPayableRouteUnrelatedCost) (*do.ExtraData, *retcode.SSCError) {
	var (
		configurationTypeSuccessRateMultiplier    = spx_driver_payable_enum.ConfigurationTypeSuccessRateMultiplier
		configurationTypeAttendanceRateMultiplier = spx_driver_payable_enum.ConfigurationTypeAttendanceRateMultiplier

		billingRuleRateCardDetailEntity = calculateDto.SpxBillingRuleEntity.RateCardRuleInfoEntity.RateCardRuleDetail
		billingPeriodEntity             = payableSchema.SpxDriverPayableBillingPeriodEntity
	)

	multiplierDefault := decimal.NewFromFloat(1)
	var attendanceRateMultiplier = &multiplierDefault
	var successRateMultiplier = &multiplierDefault
	billingPeriodExtraData := billingPeriodEntity.ExtraData

	var routePriceSuccessRateMultiplier *spx_driver_payable_entity.RouteRelatedFeeInfo
	var routePriceAttendanceRateMultiplier *spx_driver_payable_entity.RouteRelatedFeeInfo
	var sscErr *retcode.SSCError
	if billingRuleRateCardDetailEntity.ContainsMultiplier(spx_driver_payable_enum.SuccessRateMultiplier) {
		routePriceSuccessRateMultiplier, sscErr = UniversalMatchRoutePrice(ctx, payableSchema, calculateDto, taskEntity, nil, billingPeriodExtraData, routeUnrelatedConfig, configurationTypeSuccessRateMultiplier)
		if sscErr != nil {
			return nil, sscErr
		}

		successRateMultiplier = routePriceSuccessRateMultiplier.SuccessRateMultiplierPerPeriod
	}

	// 判断Multiplier是否有包含AR Multiplier
	if billingRuleRateCardDetailEntity.ContainsMultiplier(spx_driver_payable_enum.AttendanceRateMultiplier) {
		routePriceAttendanceRateMultiplier, sscErr = UniversalMatchRoutePrice(ctx, payableSchema, calculateDto, taskEntity, nil, billingPeriodExtraData, routeUnrelatedConfig, configurationTypeAttendanceRateMultiplier)
		if sscErr != nil {
			return nil, sscErr
		}

		if routePriceAttendanceRateMultiplier.AttendanceRateMultiplierPerPeriod == nil {
			return nil, retcode.SpxDriverCalculateErr.AppendDetail("route price AttendanceRateMultiplierPerPeriod is nil")
		}
		attendanceRateMultiplier = routePriceAttendanceRateMultiplier.AttendanceRateMultiplierPerPeriod
	}

	extraData.SuccessRateMultiplier = successRateMultiplier
	extraData.AttendanceRateMultiplier = attendanceRateMultiplier
	return extraData, nil
}

func (p *PeriodMultiplierPlugin) Report(ctx dbhelper.BmsContext, spxDriverSchema schema.ISpxDriverPayableSchema, shippingFeeDto dto.ICalculateShippingFeeDTO, sscError *retcode.SSCError) *retcode.SSCError {
	return nil
}
