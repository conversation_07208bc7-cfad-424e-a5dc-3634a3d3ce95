package flow

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum/spx_driver_payable_enum"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/dto/fee_type_calculate_dto/spx_driver_payable_dto"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_option"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/service/common"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/service/spx_driver_payable_cost_service/driver_payable_core/plugin"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/service/spx_driver_payable_cost_service/driver_payable_core/schema"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/utils/safego"
)

type ISpxDriverPayablePeriodFlow ISpxDriverPayableFlow

type SpxDriverPayablePeriodFlow struct {
}

func (s SpxDriverPayablePeriodFlow) PreTransferSchemaToCalculateDTO(ctx dbhelper.BmsContext, driverPayableSchema schema.ISpxDriverPayableSchema, calculateDto dto.ICalculateShippingFeeDTO) *retcode.SSCError {
	return retcode.AssertTypeError.CloneWithDetailFormat("period flow do not need pre transfer")
}
func (s SpxDriverPayablePeriodFlow) TransferSchemaToCalculateDTO(ctx dbhelper.BmsContext, driverPayableSchema schema.ISpxDriverPayableSchema, calculateDto dto.ICalculateShippingFeeDTO) *retcode.SSCError {
	driverPayableSchemaData, ok := driverPayableSchema.(*schema.SpxDriverPayableSchema)
	if !ok {
		return retcode.AssertTypeError.AppendDetail("driverPayableSchema is not driverPayableSchema")
	}
	spxLhCostCalculateByPeriodOrderDTO, ok := calculateDto.(*spx_driver_payable_dto.SpxDriverPayableCalculateDTO)
	if !ok {
		return retcode.AssertTypeError.AppendDetail("calculate dto is not SpxLhCostCalculateByPeriodOrderDTO")
	}

	// 初始化赋值
	sscErr := s.initCalculateDTO(ctx, driverPayableSchemaData, spxLhCostCalculateByPeriodOrderDTO)
	if sscErr != nil {
		return sscErr
	}

	// 各个插件赋值
	for _, spxDriverPlugin := range s.GetPluginList(ctx, driverPayableSchemaData) {
		err := spxDriverPlugin.Process(ctx, driverPayableSchemaData, calculateDto)
		safego.Do(ctx, func() {
			sscErr = spxDriverPlugin.Report(ctx, driverPayableSchemaData, calculateDto, err)
			if sscErr != nil {
				logger.CtxLogErrorf(ctx, "spx driver payable report Error, Error is %+v", sscErr)
			}
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func (s SpxDriverPayablePeriodFlow) initCalculateDTO(ctx dbhelper.BmsContext, spxDriverPayableSchema *schema.SpxDriverPayableSchema, spxLhCostCalculateByPeriodOrderDTO *spx_driver_payable_dto.SpxDriverPayableCalculateDTO) *retcode.SSCError {
	periodBillingTab := spxDriverPayableSchema.SpxDriverPayableBillingPeriodEntity
	feeType := periodBillingTab.OrderSource.TransferToFeeType()
	feeTypeConfig, sscErr := common.GetFeeTypeConfig(ctx, periodBillingTab.Region, feeType)
	if sscErr != nil {
		return retcode.GetFeeTypeRateConfigDetailError.CloneWithError(sscErr.NewError())
	}

	baseDto := dto.BaseCalculateShippingFeeDTO{
		ServiceId:     strconv.FormatInt(periodBillingTab.AgencyId, 10),
		Region:        periodBillingTab.Region,
		FeeType:       feeType,
		FeeTypeConfig: feeTypeConfig,
		CalculateOption: &calculate_option.CalculateOption{
			SpxPaymentType:                periodBillingTab.PaymentType,
			SpxDriverPayableCalculateType: spx_driver_payable_enum.SpxDriverPayableCalculatePeriod,
		},
	}

	// 周期计费需要使用所有的天计费数据
	spxLhCostCalculateByPeriodOrderDTO.DayResultEntityList = spxDriverPayableSchema.DayResultEntityList
	// Base赋值
	spxLhCostCalculateByPeriodOrderDTO.BaseCalculateShippingFeeDTO = baseDto
	spxLhCostCalculateByPeriodOrderDTO.PaymentType = periodBillingTab.PaymentType
	spxLhCostCalculateByPeriodOrderDTO.PeriodBillingEntity = periodBillingTab
	return nil
}

func (s SpxDriverPayablePeriodFlow) GetPluginList(ctx dbhelper.BmsContext, driverPayableSchema schema.ISpxDriverPayableSchema) []plugin.SpxDriverPayablePlugin {
	serviceFeePlugin := []plugin.SpxDriverPayablePlugin{
		plugin.NewMatchBillingRuleEntityPlugin(),
		plugin.NewCommonPlugin(),
		plugin.NewDriverMetricsPlugin(),
		plugin.NewHolidayTypePlugin(),
		plugin.NewRestDayPlugin(),
		plugin.NewAttendanceRatePlugin(),
		plugin.NewProductivityPlugin(),
		plugin.NewMonthlyMinimumSalaryMatchPlugin(), // 匹配月度最低工资  周期类型子费用依赖此
	}
	if driverPayableSchema.GetPaymentType(ctx) == spx_driver_payable_enum.SpxDriverPayablePaymentByServiceFee {
		return serviceFeePlugin
	}
	// 周期IncentiveFee处理不需要SuccessRate相关数据
	incentiveFeePlugin := []plugin.SpxDriverPayablePlugin{
		plugin.NewMatchBillingRuleEntityPlugin(),
		plugin.NewCommonPlugin(),
		plugin.NewDriverMetricsPlugin(),
		plugin.NewHolidayTypePlugin(),
		plugin.NewRestDayPlugin(),
		plugin.NewAttendanceRatePlugin(),
		plugin.NewProductivityPlugin(),                 // 计算Productivity FM&LM、LH, Salary、Incentive都需要
		plugin.NewIncentivePointPlugin(),               // 计算IncentivePoint  IncentiveFee需要
		plugin.NewSuccessRatePlugin(),                  // 计算SuccessRate IncentiveFee需要
		plugin.NewIncentiveRateThresholdPlugin(),       // 计算IncentiveRate IncentiveFee需要
		plugin.NewIncentiveSpecialRescuePluginPlugin(), // IncentivePoint计算场景下 判断SpecialRescue给IncentiveRate、SuccessRate重新赋值。 并重新计算IncentiveAmount
		plugin.NewPeriodMultiplierPlugin(),             // 计算PeriodMultiplier IncentiveFee需要
		plugin.NewDailyMultiplierPlugin(),              // 计算DailyMultiplier IncentiveFee需要
	}
	return incentiveFeePlugin
}
