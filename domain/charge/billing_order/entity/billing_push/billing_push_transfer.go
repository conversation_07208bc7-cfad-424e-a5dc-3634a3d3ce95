package billing_push

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

func (f *FinanceBillingPushEntity) ToFinanceBillingPushTab() (*do.FinanceBillingPushTab, *retcode.SSCError) {
	return &do.FinanceBillingPushTab{
		Id:            uint64(f.Id),
		ScfsBillingId: uint64(f.ScfsBillingId),
		PushType:      f.PushType,
		BillingInfo:   f.BillingInfo.JsonStr(),
		PushStatus:    f.PushStatus,
		ErrorMessage:  f.ErrorMessage,
		RetryTimes:    f.RetryTimes,
		Ctime:         f.Ctime,
		Mtime:         f.Mtime,
	}, nil
}
