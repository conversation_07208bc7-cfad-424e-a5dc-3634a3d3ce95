package billing_order

import (
	"fmt"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/utils/json_util"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/entity/billing_version"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/entity/biz_order"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/vo"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/vo/biz_order_extra_data"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/vo/weight_info"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/protocol/http_protocol/coreapi_protocol"
	protocol2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/protocol/http_protocol/coreapi_protocol"
	jsoniter "github.com/json-iterator/go"
)

var _ IBillingEntity = &KerryBillingOrder{}

type KerryBillingOrder struct {
	ID               int64
	Country          string
	ScfsOrderId      int64
	ScfsBillingId    int64
	FeeType          int64
	TotalFee         float64
	ChargeableWeight float64
	BillingRole      int64
	RelateRole       int64
	ServiceID        string
	BizOrderNo       string
	FeeCategory      int64
	ServiceDimension int64
	ServiceType      int64
	ServiceCategory  int64
	AssociateOrderNo string
	RateChannelId    int64
	OperatorVersion  int64
	CalculateTime    int64
	OrderStatus      string
	CalculateStatus  int8
	Ctime            int64
	Mtime            int64
	ExtraData        string
	CalculateInfo    *vo.KerryCalculateInfo
	WeightInfo       *weight_info.CommonWeightInfo
	BillingStatus    int8
}

func (r *KerryBillingOrder) GetLocationInfo() vo.ILocationInfo {
	return nil
}

func (r *KerryBillingOrder) GetBizOrderNo() string {
	if r == nil {
		return ""
	}
	return r.BizOrderNo
}

func (r *KerryBillingOrder) GetServiceID() string {
	if r == nil {
		return ""
	}
	return r.ServiceID
}

func (r *KerryBillingOrder) GetScfsOrderID() int64 {
	if r == nil {
		return 0
	}
	return r.ScfsOrderId
}

func (r *KerryBillingOrder) GetFeeType() enum.FeeType {
	return enum.KerryFee
}

func (r *KerryBillingOrder) UniqKey() string {
	if r != nil {
		return fmt.Sprintf("scfs_order_id=%d, scfs_billing_id=%d", r.ScfsOrderId, r.ScfsBillingId)
	}
	return ""
}

func (r *KerryBillingOrder) SetBillingStatus(status enum.BillingOrderStatus) {
	if r != nil {
		r.BillingStatus = int8(status)
	}
}

func (r *KerryBillingOrder) GetBillingStatus() enum.BillingOrderStatus {
	if r == nil {
		return 0
	}
	return enum.BillingOrderStatus(r.BillingStatus)
}

func (r *KerryBillingOrder) IsNil() bool {
	return r == nil
}

func (r *KerryBillingOrder) SetID(i int64) {
	if r != nil {
		r.ID = i
	}
}

func (r *KerryBillingOrder) GetID() int64 {
	if r != nil {
		return r.ID
	}
	return 0
}

func (r *KerryBillingOrder) GetCtime() int64 {
	if r != nil {
		return r.Ctime
	}
	return 0
}

func (r *KerryBillingOrder) SetCtime(ctime int64) {
	if r != nil {
		r.Ctime = ctime
	}
}

func (r *KerryBillingOrder) GetOperationVersion() int64 {
	if r != nil {
		return r.OperatorVersion
	}
	return 0
}

func (r *KerryBillingOrder) SetOperationVersion(version int64) {
	if r != nil {
		r.OperatorVersion = version
	}
}

func (r *KerryBillingOrder) GetCalculateStatus() int8 {
	if r == nil {
		return 0
	}
	return r.CalculateStatus
}

func (r KerryBillingOrder) ToFinanceBillingTab() (*do.FinanceBillingTab, *retcode.SSCError) {

	calculateInfo := "{}"
	weight := "{}"
	if r.CalculateInfo != nil {
		calculateInfo = r.CalculateInfo.ToJson()
	}

	if r.WeightInfo != nil {
		weight = r.WeightInfo.JsonStr()
	}

	return &do.FinanceBillingTab{
		ID:                  r.ID,
		Country:             r.Country,
		ScfsOrderId:         r.ScfsOrderId,
		ScfsBillingId:       r.ScfsBillingId,
		FeeType:             r.FeeType,
		BillingRole:         r.BillingRole,
		RelateRole:          r.RelateRole,
		BizOrderNo:          r.BizOrderNo,
		FeeCategory:         r.FeeCategory,
		ServiceDimension:    r.ServiceDimension,
		ServiceId:           r.ServiceID,
		ServiceType:         r.ServiceType,
		ServiceCategory:     r.ServiceCategory,
		AssociateOrderNo:    r.AssociateOrderNo,
		RateChannelId:       r.RateChannelId,
		OperatorVersion:     r.OperatorVersion,
		SignificantTimeInfo: "{}",
		CalculateTime:       r.CalculateTime,
		LocationInfo:        "{}",
		OrderStatus:         r.OrderStatus,
		CalculateInfo:       calculateInfo,
		WeightInfo:          weight,
		SizeInfo:            "{}",
		RequestData:         "{}",
		ResponseData:        "{}",
		ExtraData:           r.ExtraData,
		Ctime:               r.Ctime,
		Mtime:               r.Mtime,
		TotalFee:            r.TotalFee,
		ChargeableWeight:    r.ChargeableWeight,
		CalculateStatus:     int64(r.CalculateStatus),
		BillingStatus:       int64(r.BillingStatus),
	}, nil
}

func (r KerryBillingOrder) ToEsEntity() *BillingOrderIndex {
	return &BillingOrderIndex{
		Country:         r.Country,
		BusinessOrderNo: r.BizOrderNo,
		BusinessStatus:  r.OrderStatus,
		ServiceId:       r.ServiceID,
		FeeCategory:     enum.FeeCategory(r.FeeCategory),
		//历史遗留问题BillingStatus映射到es为calculate_status
		BillingOrderStatus: enum.BillingOrderStatus(r.BillingStatus),
		CalculateStatus:    enum.CalculateStatus(r.CalculateStatus),
		FeeType:            enum.FeeType(r.FeeType),
		ServiceCategory:    enum.ServiceCategory(r.ServiceCategory),
		ServiceType:        enum.ServiceType(r.ServiceType),
		BillingRole:        enum.BillingRole(r.BillingRole),
		ServiceDimension:   int8(r.ServiceDimension),
		Ctime:              uint32(r.Ctime),
		Mtime:              uint32(r.Mtime),
		BillingId:          uint64(r.ScfsBillingId),
		RateId:             uint64(r.RateChannelId),
		FeeAmount:          r.TotalFee,
		ChargeableWeight:   r.ChargeableWeight,
		ScfsOrderID:        r.ScfsOrderId,
	}
}

func (r KerryBillingOrder) GetBillingEntity() *BillingEntity {
	return &BillingEntity{
		ID:               r.ID,
		Country:          r.Country,
		ScfsOrderId:      r.ScfsOrderId,
		ScfsBillingId:    r.ScfsBillingId,
		FeeType:          r.FeeType,
		TotalFee:         r.TotalFee,
		ChargeableWeight: r.ChargeableWeight,
		BillingRole:      r.BillingRole,
		RelateRole:       r.RelateRole,
		ServiceID:        r.ServiceID,
		BizOrderNo:       r.BizOrderNo,
		FeeCategory:      r.FeeCategory,
		ServiceDimension: r.ServiceDimension,
		ServiceType:      r.ServiceType,
		ServiceCategory:  r.ServiceCategory,
		AssociateOrderNo: r.AssociateOrderNo,
		RateChannelId:    r.RateChannelId,
		OperatorVersion:  r.OperatorVersion,
		CalculateTime:    r.CalculateTime,
		OrderStatus:      r.OrderStatus,
		CalculateStatus:  r.CalculateStatus,
		Ctime:            r.Ctime,
		Mtime:            r.Mtime,
		BillingStatus:    r.BillingStatus,
		ExtraData:        r.ExtraData,
	}
}

func (r KerryBillingOrder) GetCalculateInfo() vo.ICalculateInfo {
	if r.CalculateInfo == nil {
		return &vo.KerryCalculateInfo{}
	}
	return r.CalculateInfo
}

func (r *KerryBillingOrder) SetSuccessStatus() {
	r.CalculateStatus = enum.CalculateStatusSuccess
}

func (r *KerryBillingOrder) GetScfsBillingID() int64 {
	if r == nil {
		return 0
	}
	return r.ScfsBillingId
}

func (r *KerryBillingOrder) SetBillingID(id int64) {
	r.ScfsBillingId = id
}

func (r KerryBillingOrder) ToHistoryTab(calcErr *retcode.SSCError) billing_version.IFinanceBillingVersionEnt {
	return &billing_version.KerryBillingVersion{
		FinanceBillingVersion: billing_version.FinanceBillingVersionEnt{
			Country:              r.Country,
			ScfsBillingId:        r.ScfsBillingId,
			FeeType:              r.FeeType,
			RateChannelId:        r.RateChannelId,
			CalculateTime:        r.CalculateTime,
			CalculateOrderStatus: r.OrderStatus,
			CalculateStatus:      int64(r.CalculateStatus),
			StatusMessage:        calcErr.Message(),
			CalculateInfo:        r.CalculateInfo.ToJson(),
		},
	}
}

func (r *KerryBillingOrder) GetTotalFee() float64 {
	return r.TotalFee
}

func (r *KerryBillingOrder) GetKerryFeeDetail() *vo.KerryCalculateInfo {
	if r == nil {
		return &vo.KerryCalculateInfo{}
	}

	return r.CalculateInfo
}

func (r *KerryBillingOrder) FixedFeeAmount() bool {
	if r == nil {
		return false
	}
	return r.BillingStatus == enum.BillingStatusReconDone || r.BillingStatus == enum.BillingStatusSettled
}

func (r *KerryBillingOrder) SetMtime(time int64) {
	if r == nil {
		return
	}
	r.Mtime = time
}

func (r *KerryBillingOrder) GetCalcErr() *retcode.SSCError {
	if r == nil {
		return nil
	}
	if r.CalculateInfo.CalcErr != nil {
		return r.CalculateInfo.CalcErr
	}
	return nil
}

func (r *KerryBillingOrder) DifferentWith(other ReconcilableBillingOrder) bool {
	return r.GetTotalFee() != other.GetTotalFee()
}

func (r *KerryBillingOrder) ReconSchema() protocol2.IReconSchema {
	if r == nil {
		return protocol2.ReconKerryPickUpFeeResp{}
	}
	resp := protocol2.ReconKerryPickUpFeeResp{
		CalculateStatus: r.CalculateStatus,
		ReconSchema: protocol2.ReconSchema{
			ScfsOrderID:   r.GetScfsOrderID(),
			ScfsBillingID: r.GetScfsBillingID(),
			LmTrackingNo:  r.GetBizOrderNo(),
		},
	}

	if r.CalculateInfo != nil {
		resp.GroupToTalFee = r.CalculateInfo.KerryPickupFeeTotal
		resp.RateChannelID = r.GetCalculateInfo().GetRateID()
		resp.GroupID = r.CalculateInfo.GroupId
		resp.PickUpAddress = r.CalculateInfo.PickupAddress
		resp.PickUpData = r.CalculateInfo.ActualPickupDate
		resp.ShopId = r.CalculateInfo.ShopId
		resp.SenderName = r.CalculateInfo.SenderName
		resp.LineId = r.CalculateInfo.LineID
		resp.GroupArea = r.CalculateInfo.PickupArea
		resp.MerchantID = r.CalculateInfo.MerchantID
		resp.GroupOrderCount = int(r.CalculateInfo.ParcelsVolume)
		resp.GroupCpo = r.CalculateInfo.KerryPickupFee
		resp.ServiceId = r.CalculateInfo.ServiceId

		if r.CalculateInfo.IsPickupRetry {
			resp.PickRetry = 1
		}
		if r.CalculateInfo.CalcErr != nil {
			resp.Message = r.CalculateInfo.CalcErr.Message()
			resp.RetCode = int(r.CalculateInfo.CalcErr.RetCode)

		}
	}

	return resp
}

func (r *KerryBillingOrder) GetReconVersion(_ dbhelper.BmsContext) int32 {
	if r == nil {
		return 0
	}
	extraData := &biz_order_extra_data.ReconExtraData{}
	err := jsoniter.Unmarshal([]byte(r.ExtraData), extraData)
	if err != nil {
		return 0
	}
	return extraData.ReconVersion
}

func (r *KerryBillingOrder) SetReconVersion(version int32) {
	if r == nil {
		return
	}

	var data biz_order_extra_data.ReconExtraData
	_ = json_util.FromJson(r.ExtraData, &data)
	data.ReconVersion = version

	r.ExtraData = data.JsonStr()
}

func (r *KerryBillingOrder) ReconSchemaWithBizOrder(iBizOrder biz_order.IBizOrderEntity) coreapi_protocol.IReconSchema {
	if r == nil {
		return nil
	}
	if iBizOrder == nil || iBizOrder.IsNil() {
		return r.ReconSchema()
	}
	reconSchema, ok := r.ReconSchema().(coreapi_protocol.ReconKerryPickUpFeeResp)
	if !ok {
		return r.ReconSchema()
	}
	bizOrder, ok := iBizOrder.(*biz_order.ThreePLAsfBizOrderEntity)
	if !ok {
		return r.ReconSchema()
	}

	reconSchema.SloID = bizOrder.ExtraData.LogID
	reconSchema.SlsTrackingNo = bizOrder.BizOrder.AssociateOrderNo
	reconSchema.TrackingCode = bizOrder.BizOrder.OrderStatus
	reconSchema.SlsTnCtime = bizOrder.BizOrder.OrderCtime
	return reconSchema
}

func (r *KerryBillingOrder) SetReconStartVersion(_ uint32) {
	return
}

func (r *KerryBillingOrder) GetReconStartVersion() uint32 {
	return 0
}

func (r *KerryBillingOrder) NeedRewriteDB() bool {
	return false
}

func (r *KerryBillingOrder) GetSupplierType() enum.SupplierType {
	if r == nil {
		return 0
	}
	extraData := &biz_order_extra_data.ReconExtraData{}
	if err := jsoniter.Unmarshal([]byte(r.ExtraData), extraData); err != nil {
		return 0
	}
	return extraData.SupplierType
}

func (r *KerryBillingOrder) SetSupplierType(supplierType enum.SupplierType) {
	var data biz_order_extra_data.ReconExtraData
	_ = json_util.FromJson(r.ExtraData, &data)
	data.SupplierType = supplierType
	r.ExtraData = data.JsonStr()
}
