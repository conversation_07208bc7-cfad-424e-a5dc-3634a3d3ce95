package excel_parser

import (
	"errors"
	"fmt"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/service/excel_handler"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/service/excel_handler/excel_config"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/sls/ilh_sf/vendor_line_mapping/do"
	persistence3 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/sls/ilh_sf/vendor_line_mapping/persistence"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	supplier_lib "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/third_party/supplier_transfer_lib"
)

const MaxRowCount = 100000

type vendorLineMappingParser struct {
	//数据重复性校验
	validateSet  map[string][]string
	uniqueKeySet map[string]struct{}
	//PRS Vendor‘s name(the text) should be the same for the same line_id
	lineIDVendorNameSet         map[string]string
	templateHeader              excel_config.ExcelTplRow
	supplierLineTransferService supplier_lib.ISupplierIDLib
	vendorLineMappingDao        persistence3.ILHVendorLineMappingDAO
	rowCount                    *int
}

func (r vendorLineMappingParser) postParseSheet(ctx dbhelper.BmsContext, data []excel_handler.IExcelDbObject, rowsData []excel_handler.IExcelRowData) error {
	return nil
}

func (r vendorLineMappingParser) afterParseAllSheet(ctx dbhelper.BmsContext, _ []excel_handler.IExcelDbObject) error {
	//校验重复mapping，查询数据库中所有的mapping
	vendorLineMappingList, _, sscError := r.vendorLineMappingDao.GetVendorLineMappingByPage(ctx, nil, 0, -1)
	if sscError != nil {
		return fmt.Errorf(sscError.Detail)
	}
	for _, t := range vendorLineMappingList {
		key := fmt.Sprintf("%s-%s-%s-%s", t.TransferMode, t.VendorName, t.OriginalCode, t.DestinationCode)
		uniqueKey := fmt.Sprintf("%s-%s", key, t.LineId)
		if _, ok := r.uniqueKeySet[uniqueKey]; ok {
			return fmt.Errorf("this vendor line mapping has been configured before: %s, tab_id:%v", uniqueKey, t.Id)
		}
		if _, ok := r.validateSet[key]; ok {
			r.validateSet[key] = append(r.validateSet[key], t.LineId)
		}
	}

	//批量接口 校验LineID是否真实存在
	lineIDList := make([]string, 0)
	for _, lineId := range r.validateSet {
		lineIDList = append(lineIDList, lineId...)
	}

	//调用statement接口，根据lineID批量查询supplier
	lineIDSupplierMap, sscError := r.supplierLineTransferService.BatchGetSupplierLineIDMap(ctx, lineIDList, enum.ILHCB.StringView())
	if sscError != nil {
		return fmt.Errorf(sscError.Detail)
	}
	//校验lineID是否真实存在
	invalidLineIDList := make([]string, 0)
	for _, lineID := range lineIDList {
		if _, ok := lineIDSupplierMap[lineID]; !ok {
			invalidLineIDList = append(invalidLineIDList, lineID)
		}
	}
	if len(invalidLineIDList) > 0 {
		return fmt.Errorf("these line id are invalid:{%s}", strings.Join(invalidLineIDList, ","))
	}

	errMsg := make([]string, 0)
	for key, uploadLineIdList := range r.validateSet {
		if len(uploadLineIdList) < 2 {
			continue
		}
		supplierIdList := make(map[int64][]string, 0)
		for _, lineId := range uploadLineIdList {
			if _, ok := lineIDSupplierMap[lineId]; !ok {
				//will not come here
				//should have been validated before
				continue
			}
			supplierIdList[lineIDSupplierMap[lineId].SupplierID] = append(supplierIdList[lineIDSupplierMap[lineId].SupplierID], lineId)
		}

		if len(supplierIdList) > 1 {
			errMsg = append(errMsg, fmt.Sprintf(" the key[%s], supplier - lineId map:[%v]", key, supplierIdList))
		}
	}
	if len(errMsg) > 0 {
		return fmt.Errorf("the same Vendor - Line Mapping are required to configure the same Supplier ID!, error list:%v", errMsg)
	}

	//校验数据库中 line id对应的 vendor name是一致的 行数显示
	for _, item := range vendorLineMappingList {
		if prsVendor, ok := r.lineIDVendorNameSet[item.LineId]; ok && prsVendor != item.VendorName {
			return fmt.Errorf("prs vendor‘s name(the text) should be the same for the same line_id %s as exist vendor's name is %s", item.LineId, item.VendorName)
		}
	}
	return nil
}

// 1、校验Transfer Mode + PRS Vendor + Origin Airport + Destination Airport组合重复 2、校验 相同的line id的vendor必须一样
func (r vendorLineMappingParser) validateRowData(ctx dbhelper.BmsContext, template excel_handler.IExcelRowData, rowIdx int) error {
	*r.rowCount += 1
	if *r.rowCount > MaxRowCount {
		return fmt.Errorf("sheet rows can not exceed %d", MaxRowCount)
	}
	vendorLineMappingTemplate := template.(*vendorLineMappingIntermediateData)
	uniqueKey := vendorLineMappingTemplate.GetUniqueKey()
	validateUniqueKey := fmt.Sprintf("%s-%s", uniqueKey, vendorLineMappingTemplate.LineID)
	if _, ok := r.uniqueKeySet[validateUniqueKey]; ok {
		return fmt.Errorf("this vendor line mapping duplicated: %s", validateUniqueKey)
	}
	r.uniqueKeySet[validateUniqueKey] = struct{}{}
	r.validateSet[uniqueKey] = append(r.validateSet[uniqueKey], vendorLineMappingTemplate.LineID)
	//校验相同的line id的vendor必须一样
	if prsVendor, ok := r.lineIDVendorNameSet[vendorLineMappingTemplate.LineID]; ok && prsVendor != vendorLineMappingTemplate.PRSVendor {
		return fmt.Errorf("prs vendor‘s name(the text) should be the same for the same line_id %s", vendorLineMappingTemplate.LineID)
	}

	r.lineIDVendorNameSet[vendorLineMappingTemplate.LineID] = vendorLineMappingTemplate.PRSVendor
	return nil
}

func (r vendorLineMappingParser) generateExcelTemplateObject() excel_handler.IExcelRowData {
	return &vendorLineMappingIntermediateData{}
}
func (t vendorLineMappingIntermediateData) GetCheckerSetters() excel_handler.ExcelCellCheckerSetterList {
	return vendorLineMappingTemplateSetters
}

func (t vendorLineMappingIntermediateData) GetUniqueKey() string {
	return fmt.Sprintf("%s-%s-%s-%s", t.TransferMode, t.PRSVendor, t.OriginCode, t.DestinationCode)
}
func (r vendorLineMappingParser) convertTemplateToDbObject(ctx dbhelper.BmsContext, template excel_handler.IExcelRowData) (excel_handler.IExcelDbObject, error) {
	dbObject := &do.ILHVendorLineMappingTab{}
	t := template.(*vendorLineMappingIntermediateData)
	dbObject.TransferMode = enum.TransferMode(t.TransferMode)
	dbObject.VendorName = t.PRSVendor
	dbObject.OriginalCode = t.OriginCode
	dbObject.DestinationCode = t.DestinationCode
	dbObject.LineId = t.LineID
	return dbObject, nil
}

func (r vendorLineMappingParser) getTemplateHeader() excel_config.ExcelTplRow {
	// return excel_handler.ZoneTplRow
	return r.templateHeader
}

type vendorLineMappingTpl struct {
	TransferMode,
	PRSVendor,
	OriginCode,
	DestinationCode,
	LineID string
}

type vendorLineMappingIntermediateData struct {
	vendorLineMappingTpl
}

var vendorLineMappingTemplateSetters = excel_handler.ExcelCellCheckerSetterList{
	setTransferMode,
	setPRSVendor,
	setOriginCode,
	setDestinationCode,
	setLineID,
}

func setTransferMode(t excel_handler.IExcelRowData, s string) error {
	s = strings.TrimSpace(s)
	transferMode := enum.TransferMode(s)
	if transferMode.StringView() == enum.EmptyStr {
		return errors.New("incorrect TransferMode(Air/Land/Sea)")
	}
	tmp := t.(*vendorLineMappingIntermediateData)
	tmp.TransferMode = s
	return nil
}

func setPRSVendor(t excel_handler.IExcelRowData, s string) error {
	s = strings.TrimSpace(s)
	if s == enum.EmptyStr || s == "/" {
		return errors.New("PRSVendor can not be empty")
	}
	tmp := t.(*vendorLineMappingIntermediateData)
	tmp.PRSVendor = s
	return nil
}

func setOriginCode(t excel_handler.IExcelRowData, s string) error {
	s = strings.TrimSpace(s)
	if s == enum.EmptyStr {
		return fmt.Errorf("original code' column is not allowed to input empty value. Please input '/' to replace")
	}
	tmp := t.(*vendorLineMappingIntermediateData)
	tmp.OriginCode = s
	return nil

}

func setDestinationCode(t excel_handler.IExcelRowData, s string) error {
	s = strings.TrimSpace(s)
	if s == enum.EmptyStr || s == "/" {
		return errors.New("DestinationCode can not be empty")
	}
	tmp := t.(*vendorLineMappingIntermediateData)
	tmp.DestinationCode = s
	return nil
}

func setLineID(t excel_handler.IExcelRowData, s string) error {
	s = strings.TrimSpace(s)
	if s == enum.EmptyStr || s == "/" {
		return errors.New("LineID can not be empty")
	}
	tmp := t.(*vendorLineMappingIntermediateData)
	tmp.LineID = s
	return nil
}
