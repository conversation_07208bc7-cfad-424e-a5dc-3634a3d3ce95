package excel_parser

import (
	"errors"
	"fmt"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/service/excel_handler"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/service/excel_handler/excel_config"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/third_party/location_server_lib"
)

type routeCodeMYParser struct {
	locationService location_server_lib.ILocationServerStub
	validateSet     map[string]struct{}
	templateHeader  excel_config.ExcelTplRow
}

func (r routeCodeMYParser) postParseSheet(ctx dbhelper.BmsContext, data []excel_handler.IExcelDbObject, rowsData []excel_handler.IExcelRowData) error {
	return nil
}

func (r routeCodeMYParser) afterParseAllSheet(_ dbhelper.BmsContext, _ []excel_handler.IExcelDbObject) error {
	return nil
}

func (r routeCodeMYParser) validateRowData(ctx dbhelper.BmsContext, template excel_handler.IExcelRowData, rowIdx int) error {
	routeCodeTemplate := template.(*routeCodeMYIntermediateData)
	if err := validateMYLocation(routeCodeTemplate.OriginState, routeCodeTemplate.OriginArea); err != nil {
		return err
	}
	if err := validateMYLocation(routeCodeTemplate.DestinationState, routeCodeTemplate.DestinationArea); err != nil {
		return err
	}

	country := ctx.GetCountry()
	originPaths := routeCodeTemplate.GetOriginLocationArray()
	originInfo, sscError := r.locationService.GetLocationByPaths(ctx, country, originPaths)
	if sscError != nil {
		return fmt.Errorf("origin location %s not found: %s", originPaths, sscError.NewError())
	}
	destinationPaths := routeCodeTemplate.GetDestinationArray()
	destinationInfo, sscError := r.locationService.GetLocationByPaths(ctx, country, destinationPaths)
	if sscError != nil {
		return fmt.Errorf("destination location %s not found: %s", destinationPaths, sscError.NewError())
	}

	routeCodeTemplate.OriginLocationId = uint64(originInfo.LocationID)
	routeCodeTemplate.DestinationId = uint64(destinationInfo.LocationID)

	// route code 唯一性校验
	uniqueKey := routeCodeTemplate.GetUniqueKey()
	if uniqueKey == enum.EmptyStr {
		return fmt.Errorf("the route config is invalid")
	}
	if _, ok := r.validateSet[uniqueKey]; ok {
		return fmt.Errorf("this route code has been configured: %s", uniqueKey)
	}
	r.validateSet[uniqueKey] = struct{}{}

	return nil
}

func validateMYLocation(state, area string) error {
	if area != enum.EmptyStr && state == enum.EmptyStr {
		return fmt.Errorf("invalid input with area not empty while state is empty, state: %+v, area: %+v", state, area)
	}
	return nil
}

func (r routeCodeMYParser) generateExcelTemplateObject() excel_handler.IExcelRowData {
	return &routeCodeMYIntermediateData{}
}

func (r routeCodeMYParser) convertTemplateToDbObject(ctx dbhelper.BmsContext, template excel_handler.IExcelRowData) (excel_handler.IExcelDbObject, error) {
	dbObject := &do.RouteCodeTab{}
	t := template.(*routeCodeMYIntermediateData)
	dbObject.RouteCode = t.Route
	dbObject.DstLocationId = t.DestinationId
	dbObject.OriginLocationId = t.OriginLocationId
	dbObject.RateID = t.RateID
	return dbObject, nil
}

func (r routeCodeMYParser) getTemplateHeader() excel_config.ExcelTplRow {
	return r.templateHeader
}

// https://jira.shopee.io/browse/SCFS-3323
// 基础模版，因为spx分市场了，所以需要将route code 按照市场 分为 base 和 其他的一些市场对应的tpl， SG支持仅postcode，my仅有state和area , MY 是Province/District/Ward， 有的市场是四级地址，
type routeCodeMYTpl struct {
	OriginState,
	OriginArea,
	DestinationState,
	DestinationArea,
	Route string
}

type routeCodeMYCalculateData struct {
	RateID,
	OriginLocationId,
	DestinationId uint64
}

type routeCodeMYIntermediateData struct {
	routeCodeMYTpl
	routeCodeMYCalculateData
}

func (t routeCodeMYIntermediateData) GetUniqueKey() string {
	return fmt.Sprintf("%d-%d", t.OriginLocationId, t.DestinationId)
}

var routeCodeMYTemplateSetters = excel_handler.ExcelCellCheckerSetterList{
	setMYOriginState,
	setMYOriginArea,
	setMYDestinationState,
	setMYDestinationArea,
	setMYRouteCode,
}

func (t routeCodeMYIntermediateData) GetCheckerSetters() excel_handler.ExcelCellCheckerSetterList {
	return routeCodeMYTemplateSetters
}

func (t routeCodeMYIntermediateData) GetOriginLocationArray() []string {
	result := make([]string, 0)
	if t.OriginState == "" {
		return result
	}
	result = append(result, t.OriginState)
	if t.OriginArea == "" {
		return result
	}
	result = append(result, t.OriginArea)
	return result
}

func (t routeCodeMYIntermediateData) GetDestinationArray() []string {
	result := make([]string, 0)
	if t.DestinationState == "" {
		return result
	}
	result = append(result, t.DestinationState)
	if t.DestinationArea == "" {
		return result
	}
	result = append(result, t.DestinationArea)
	return result
}

func setMYOriginState(t excel_handler.IExcelRowData, s string) error {
	if s == enum.EmptyStr {
		return errors.New("origin state can not be empty")
	}
	tmp := t.(*routeCodeMYIntermediateData)
	tmp.OriginState = s
	return nil
}

func setMYOriginArea(t excel_handler.IExcelRowData, s string) error {
	tmp := t.(*routeCodeMYIntermediateData)
	tmp.OriginArea = s
	return nil
}

func setMYDestinationState(t excel_handler.IExcelRowData, s string) error {
	if s == enum.EmptyStr {
		return errors.New("destination state can not be empty")
	}
	tmp := t.(*routeCodeMYIntermediateData)
	tmp.DestinationState = s
	return nil

}

func setMYDestinationArea(t excel_handler.IExcelRowData, s string) error {
	tmp := t.(*routeCodeMYIntermediateData)
	tmp.DestinationArea = s
	return nil
}

func setMYRouteCode(t excel_handler.IExcelRowData, s string) error {
	if s == enum.EmptyStr {
		return errors.New("route code can not be empty")
	}
	if strings.Contains(s, ":") {
		return fmt.Errorf("illegal character ':' contains in route code")
	}
	tmp := t.(*routeCodeMYIntermediateData)
	tmp.Route = s
	return nil
}
