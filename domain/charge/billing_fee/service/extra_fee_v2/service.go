package ExtraFeeAdmin

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/entity"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/repository"
	extraFeeFactory "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/service/extra_fee/extra_fee_factory"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

/**
    @author:qingping
    @since:2023/3/30
    @desc: //该文件是创建额外费的主流程；基本不需要修改
**/

const invalidFixedValueAndPercentage = -1.0

type IExtraFeeAdminService interface {
	CreateExtraFeeFee(ctx dbhelper.BmsContext, request dto.ExtraFeeCreateReq) *retcode.SSCError
	GetExtraFeeFee(ctx dbhelper.BmsContext, request dto.ExtraFeeGetReq) (dto.ExtraFeeRsp, *retcode.SSCError)
}

type BasicExtraFeeAdminService struct {
	extraFeeService     extraFeeFactory.ExtraFeeManager
	rateTableRepository repository.IRateTableRepository
}

func (b BasicExtraFeeAdminService) CreateExtraFeeFee(ctx dbhelper.BmsContext, request dto.ExtraFeeCreateReq) *retcode.SSCError {
	var (
		rateTable entity.RateTable
		sscErr    *retcode.SSCError
	)
	//1、校验是否需要rate 辅助
	if request.NeedRateTabHelpValidate() {
		rateTable, sscErr = b.rateTableRepository.GetRateTableById(ctx, uint32(request.GetRateId()))
		if sscErr != nil {
			return sscErr
		}
	}
	//2、不同额外费不同校验策略
	if err := ValidateFactor(ctx, request, rateTable); err != nil {
		logger.CtxLogErrorf(ctx, "validate extra fee:%s get error, detail = {%+v}, err = {%+v}", request.GetExtraFeeComponent().StringView(), request, err)
		return retcode.ParamError.CloneWithError(err)
	}
	//3、删除原来的创建新的
	_, err := b.extraFeeService.DeleteAndCreate(ctx, request.Transfer2ComplexTabs(ctx), request.GetRateId(), request.GetExtraFeeComponent())
	if err != nil {
		logger.CtxLogErrorf(ctx, "create extra fee:%s get error, detail = {%+v}, err = {%+v}", request.GetExtraFeeComponent().StringView(), request, err)
		return retcode.CreateAdditionalFeeError.CloneWithError(err)
	}
	return nil
}

func (b BasicExtraFeeAdminService) GetExtraFeeFee(ctx dbhelper.BmsContext, request dto.ExtraFeeGetReq) (dto.ExtraFeeRsp, *retcode.SSCError) {
	extraFeeList, sscErr := b.extraFeeService.SearchByRateIDAndType(ctx, request.GetRateId(), request.GetExtraFeeComponent())
	if sscErr != nil {
		logger.CtxLogErrorf(ctx, "get extra fee:%s fee detail failed: rate_id = {%v}, err = {%v}", request.GetExtraFeeComponent().StringView(), request.GetRateId(), sscErr)
		return nil, retcode.SearchAdditionalFeeError.CloneWithError(sscErr)
	}
	return b.assemble(extraFeeList)
}

func (b BasicExtraFeeAdminService) assemble(extraFeeList []do.IExtraFee) (dto.ExtraFeeRsp, *retcode.SSCError) {
	var res []dto.ExtraFeeRsp
	var comm interface{}
	for _, extraFee := range extraFeeList {
		var view dto.ExtraFeeRsp
		var err *retcode.SSCError
		if assembleFunc, ok := assembleFuncSet[extraFee.GetExtraFeeComponent()]; ok {
			view, comm, err = assembleFunc(extraFee)
			if err != nil {
				return nil, err
			}
			res = append(res, view)
		}
	}
	return dto.ExtraFeeRspStructure{
		ExtraFeeCommonRsp:   comm,
		ExtraFeeConfigItems: res,
	}, nil
}
