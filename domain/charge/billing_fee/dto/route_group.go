/**
    @author:qingping
    @since:2023/4/10
    @desc: //TODO
**/

package dto

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"strings"

	dto2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/common/task_center/dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/config"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
)

type RouteGroupItem do.RouteGroupConfigTab
type RouteGroupItemList []*do.RouteGroupConfigTab

type ListRouteGroupReq struct {
	Offset      int32                 `json:"offset" form:"offset"`
	Size        int32                 `json:"size" form:"size"`
	RouteName   string                `json:"route_name" form:"route_name"`
	RouteId     uint64                `json:"route_id" form:"route_id"`
	GroupStatus enum.RouteGroupStatus `json:"group_status" form:"group_status"`
}

type ListRouteGroupRsp struct {
	Offset int32              `json:"offset"`
	Size   int32              `json:"size"`
	List   RouteGroupItemList `json:"list"`
	Total  uint64             `json:"total"`
}

type ListRouteReq struct {
	RouteGroupId []uint64              `json:"route_group_id_list" form:"route_group_id_list"`
	GroupStatus  enum.RouteGroupStatus `json:"group_status" form:"group_status"`
}

type LocationInfoArray []LocationInfoView

func (l LocationInfoArray) GetStateName() string {
	if len(l) < 1 {
		return ""
	}
	return l[0].LocationName
}
func (l LocationInfoArray) GetCityName() string {
	if config.GetCID() == enum.TW.StringView() {
		if len(l) < 1 {
			return ""
		}
		return l[0].LocationName
	}
	if len(l) < 2 {
		return ""
	}
	return l[1].LocationName
}
func (l LocationInfoArray) GetDistrictName() string {
	if config.GetCID() == enum.TW.StringView() {
		if len(l) < 2 {
			return ""
		}
		return l[1].LocationName
	}
	if len(l) < 3 {
		return ""
	}
	return l[2].LocationName
}

func (l LocationInfoArray) GetTownName() string {
	if len(l) < 4 {
		return ""
	}
	return l[3].LocationName
}

type RouteItem struct {
	GroupId                 uint64            `json:"group_id"`
	RouteName               string            `json:"route_name" binding:"required"`
	RouteCode               string            `json:"route_code" form:"route_code"`
	OriginAddressArray      LocationInfoArray `json:"origin_address_array"`
	DestinationAddressArray LocationInfoArray `json:"destination_address_array"`
	OriginLocationId        *int64            `json:"origin_location_id"`
	DestinationLocationId   *int64            `json:"destination_location_id"`
}

func (r RouteItem) GetGroupKey() string {
	return r.RouteName
}

type RouteExportView struct {
	RouteName      string `json:"route_name" xlsx:"Route Name"`
	RouteCode      string `json:"route_code" xlsx:"Route Code"`
	OriginState    string `json:"origin_state" xlsx:"Origin State"`
	OriginCity     string `json:"origin_city" xlsx:"Origin City"`
	OriginDistrict string `json:"origin_district" xlsx:"Origin District"`
	DestState      string `json:"dest_state" xlsx:"Dest State"`
	DestCity       string `json:"dest_city" xlsx:"Dest City"`
	DestDistrict   string `json:"dest_district" xlsx:"Dest District"`
}

type PHRouteExportView struct {
	RouteName      string `json:"Route Name" xlsx:"Route Name"`
	RouteCode      string `json:"route_code" xlsx:"Route Code" binding:"required"`
	OriginState    string `json:"origin_state" xlsx:"Origin State" binding:"required"`
	OriginCity     string `json:"origin_city" xlsx:"Origin City"`
	OriginDistrict string `json:"origin_district" xlsx:"Origin District"`
	OriginTown     string `json:"origin_town" xlsx:"Origin Town"`
	DestState      string `json:"dest_state" xlsx:"Destination State" binding:"required"`
	DestCity       string `json:"dest_city" xlsx:"Destination City"`
	DestDistrict   string `json:"dest_district" xlsx:"Destination District"`
	DestTown       string `json:"dest_town" xlsx:"Destination Town"`
}
type TWRouteExportView struct {
	RouteName      string `json:"route_name" xlsx:"Route Name"`
	RouteCode      string `json:"route_code" xlsx:"Route Code"`
	OriginCity     string `json:"origin_city" xlsx:"Origin City"`
	OriginDistrict string `json:"origin_district" xlsx:"Origin District"`
	DestCity       string `json:"dest_city" xlsx:"Destination City"`
	DestDistrict   string `json:"dest_district" xlsx:"Destination District"`
}

type PHRouteParseTemplate struct {
	RouteName      string `json:"Route Name" xlsx:"Route Name"`
	RouteCode      string `json:"route_code" xlsx:"Route Code" binding:"required"`
	OriginState    string `json:"origin_state" xlsx:"Origin State" binding:"required"`
	OriginCity     string `json:"origin_city" xlsx:"Origin City"`
	OriginDistrict string `json:"origin_district" xlsx:"Origin District"`
	OriginTown     string `json:"origin_town" xlsx:"Origin Town"`
	DestState      string `json:"dest_state" xlsx:"Destination State" binding:"required"`
	DestCity       string `json:"dest_city" xlsx:"Destination City"`
	DestDistrict   string `json:"dest_district" xlsx:"Destination District"`
	DestTown       string `json:"dest_town" xlsx:"Destination Town"`
	ErrorMsg       string `json:"error_msg" xlsx:"Error msg"`
}

func (r *PHRouteParseTemplate) GetOriginLocationArray() []string {
	if r == nil {
		return nil
	}
	result := make([]string, 0)
	if r.OriginState == "" {
		return result
	}
	result = append(result, r.OriginState)
	if r.OriginCity == "" {
		return result
	}
	result = append(result, r.OriginCity)
	if r.OriginDistrict == "" {
		return result
	}
	result = append(result, r.OriginDistrict)
	if r.OriginTown == "" {
		return result
	}
	result = append(result, r.OriginTown)
	return result
}

func (r *PHRouteParseTemplate) GetDestLocationArray() []string {
	if r == nil {
		return nil
	}
	result := make([]string, 0)
	if r.DestState == "" {
		return result
	}
	result = append(result, r.DestState)
	if r.DestCity == "" {
		return result
	}
	result = append(result, r.DestCity)
	if r.DestDistrict == "" {
		return result
	}
	result = append(result, r.DestDistrict)
	if r.DestTown == "" {
		return result
	}
	result = append(result, r.DestTown)
	return result
}

func (r *PHRouteParseTemplate) GetUniqueKey() string {
	return fmt.Sprintf("%s_%s_%s_%s_%s_%s_%s_%s",
		r.OriginState,
		r.OriginCity,
		r.OriginDistrict,
		r.OriginTown,
		r.DestState,
		r.DestCity,
		r.DestDistrict,
		r.DestTown,
	)
}

func (r *PHRouteParseTemplate) SetError(errMsg string) {
	r.ErrorMsg = errMsg
}

type MyRouteParseTemplate struct {
	OriginState      string `json:"origin_state" xlsx:"Origin State"`
	OriginArea       string `json:"origin_area" xlsx:"Origin Area"`
	DestinationState string `json:"destination_state" xlsx:"Destination State"`
	DestinationArea  string `json:"destination_area" xlsx:"Destination Area"`
	Route            string `json:"route" xlsx:"Route"`
	ErrorMsg         string `json:"error_msg" xlsx:"Error msg"`
}

func (r *MyRouteParseTemplate) GetOriginLocationArray() []string {
	result := make([]string, 0)
	if r.OriginState == "" {
		return result
	}
	result = append(result, r.OriginState)
	if r.OriginArea == "" {
		return result
	}
	result = append(result, r.OriginArea)
	return result
}

func (r *MyRouteParseTemplate) GetDestLocationArray() []string {
	result := make([]string, 0)
	if r.DestinationState == "" {
		return result
	}
	result = append(result, r.DestinationState)
	if r.DestinationArea == "" {
		return result
	}
	result = append(result, r.DestinationArea)
	return result
}

func (r *MyRouteParseTemplate) GetUniqueKey() string {
	return fmt.Sprintf("%s_%s_%s_%s_%s",
		r.OriginState,
		r.OriginArea,
		r.DestinationState,
		r.DestinationArea,
		r.Route,
	)
}

func (r *MyRouteParseTemplate) SetError(errMsg string) {
	r.ErrorMsg = errMsg
}

type RouteParseTemplate struct {
	RouteCode      string `json:"route_code" xlsx:"* Route Code" binding:"required"`
	OriginState    string `json:"origin_state" xlsx:"* Origin State" binding:"required"`
	OriginCity     string `json:"origin_city" xlsx:"Origin City"`
	OriginDistrict string `json:"origin_district" xlsx:"Origin District"`
	DestState      string `json:"dest_state" xlsx:"* Dest State" binding:"required"`
	DestCity       string `json:"dest_city" xlsx:"Dest City"`
	DestDistrict   string `json:"dest_district" xlsx:"Dest District"`
	ErrorMsg       string `json:"error_msg" xlsx:"Error msg"`
}

type TWRouteParseTemplate struct {
	OriginCity     string `json:"origin_city" xlsx:"Origin City" binding:"required"`
	OriginDistrict string `json:"origin_district" xlsx:"Origin District"`
	DestCity       string `json:"dest_city" xlsx:"Destination City" binding:"required"`
	DestDistrict   string `json:"dest_district" xlsx:"Destination District"`
	RouteCode      string `json:"route_code" xlsx:"Route" binding:"required"`
	ErrorMsg       string `json:"error_msg" xlsx:"Error msg"`
}

func (r *RouteParseTemplate) GetOriginLocationArray() []string {
	if r == nil {
		return nil
	}
	result := make([]string, 0)
	if r.OriginState == "" {
		return result
	}
	result = append(result, r.OriginState)
	if r.OriginCity == "" {
		return result
	}
	result = append(result, r.OriginCity)
	if r.OriginDistrict == "" {
		return result
	}
	result = append(result, r.OriginDistrict)
	return result
}

func (r *RouteParseTemplate) GetDestLocationArray() []string {
	if r == nil {
		return nil
	}
	result := make([]string, 0)
	if r.DestState == "" {
		return result
	}
	result = append(result, r.DestState)
	if r.DestCity == "" {
		return result
	}
	result = append(result, r.DestCity)
	if r.DestDistrict == "" {
		return result
	}
	result = append(result, r.DestDistrict)
	return result
}

func (r *RouteParseTemplate) GetUniqueKey() string {
	return fmt.Sprintf("%s_%s_%s_%s_%s_%s",
		r.OriginState,
		r.OriginCity,
		r.OriginDistrict,
		r.DestState,
		r.DestCity,
		r.DestDistrict,
	)
}

func (r *RouteParseTemplate) SetError(errMsg string) {
	r.ErrorMsg = errMsg
}

func (r *TWRouteParseTemplate) GetOriginLocationArray() []string {
	if r == nil {
		return nil
	}
	result := make([]string, 0)
	if r.OriginCity == "" {
		return result
	}
	result = append(result, r.OriginCity)
	if r.OriginDistrict == "" {
		return result
	}
	result = append(result, r.OriginDistrict)
	return result
}

func (r *TWRouteParseTemplate) GetDestLocationArray() []string {
	if r == nil {
		return nil
	}
	result := make([]string, 0)
	if r.DestCity == "" {
		return result
	}
	result = append(result, r.DestCity)
	if r.DestDistrict == "" {
		return result
	}
	result = append(result, r.DestDistrict)
	return result
}

func (r *TWRouteParseTemplate) GetUniqueKey() string {
	return fmt.Sprintf("%s_%s_%s_%s",
		r.OriginCity,
		r.OriginDistrict,
		r.DestCity,
		r.DestDistrict,
	)
}

func (r *TWRouteParseTemplate) SetError(errMsg string) {
	r.ErrorMsg = errMsg
}

type ListRouteRsp struct {
	Offset int32        `json:"offset"`
	Size   int32        `json:"size"`
	List   []*RouteItem `json:"list"`
	Total  uint64       `json:"total"`
}

type DetailRouteGroupReq struct {
	RouteGroupId uint64 `json:"route_group_id" form:"route_group_id" binding:"required"`
	Offset       uint32 `json:"offset" form:"offset"`
	Size         uint32 `json:"size" form:"size" binding:"required"`
}

type DetailRouteGroupRsp struct {
	RouteGroupItem
	RouteList []*RouteItem `json:"list"`
	Total     uint64       `json:"total"`
}

type CreateReq []*RouteItem

type MassCreateRouteReq struct {
	Url       string `json:"url"`
	RouteName string `json:"route_name" xlsx:"Route Name"`
	RouteList []*RouteItem
}

type MassCreateRouteResp struct {
	TaskInfo *dto2.StandardTaskInfo `json:"task_info"`
}

type MassCreateRouteExtraData struct {
	QtyOfRoutes int64 `json:"qty_of_routes"`
}

func (m *MassCreateRouteExtraData) ToJson() (string, error) {
	jsonStr, err := json.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(jsonStr), nil
}

type DisableRouteGroupReq struct {
	RouteGroupId uint64 `json:"route_group_id" form:"route_group_id" binding:"required"`
}

type MassExportReq = ListRouteGroupReq

type MassExportResp struct {
	Url      string                 `json:"url"`
	TaskId   int                    `json:"task_id"`
	TaskInfo *dto2.StandardTaskInfo `json:"task_info"`
}

type DiscountRuleTemplate struct {
	AccountId              uint64   `json:"account_id" xlsx:"*Account ID" binding:"required"`
	ServiceId              string   `json:"product_id" xlsx:"* Product ID" binding:"required"`
	MaxCapPerOrder         int64    `json:"max_cap_per_order" xlsx:"* Discount Cap Amount" binding:"gte=0,lte=*********"`
	SenderLocationRule     *bool    `json:"sender_location_rule" xlsx:"* Sender Location Applicability" binding:"required"`
	SenderLocation         string   `json:"sender_location" xlsx:"Sender Province"`
	RouteZoneApplicability *bool    `json:"route_zone_applicability" xlsx:"* Route Zone Applicability" binding:"required"`
	RouteName              string   `json:"route_name" xlsx:"Route Name"`
	RouteZone              string   `json:"route_zone" xlsx:"Route Code"`
	MinSpend               int64    `json:"min_spend" xlsx:"* Min Spend" binding:"gte=0,lte=*********"`
	DiscountMode           string   `json:"discount_mode" xlsx:"* Discount Mode for the Basic Shipping Fee" binding:"required"`
	DiscountValue          *float64 `json:"discount_value" xlsx:"Discount Value" binding:"omitempty,gte=0"`
	DiscountRate           *float64 `json:"discount_rate" xlsx:"Discount Rate (%) (Remark: Input a number from 0 to 100)" binding:"omitempty,gte=0,lt=100"`
	Remark                 string   `json:"remark" xlsx:"Remark"`
	ErrorMsg               string   `xlsx:"Error Message"`
}

type DisableTemplate struct {
	RuleId int64 `json:"rule_id" xlsx:"Discount Rule ID" binding:"required,gt=0"`
}

type DisableResponseTemplate struct {
	DisableTemplate `xlsx:"t,squash"`
	Status          string `xlsx:"Status"`
	Reason          string `xlsx:"Reason"`
}

func (d *DiscountRuleTemplate) GetUniqueKey() string {
	return fmt.Sprintf("%d_%s", d.AccountId, d.ServiceId)
}

type DiscountRuleExportTemplate struct {
	DiscountRuleId         int64   `xlsx:"Discount Rule ID"`
	AccountId              uint64  `xlsx:"Account ID"`
	ServiceId              string  `xlsx:"Product ID"`
	MerchantTypeRateCard   string  `xlsx:"Merchant Type Rate Card"`
	MaxCapPerOrder         int     `xlsx:"Discount Cap Amount"`
	SenderLocationRule     bool    `xlsx:"Sender Location Applicability"`
	SenderLocation         string  `xlsx:"Sender Province"`
	RouteZoneApplicability bool    `xlsx:"Route Zone Applicability"`
	RouteZone              string  `xlsx:"Route Code"`
	MinSpend               int     `xlsx:"Min Spend"`
	DiscountMode           string  `xlsx:"Discount Mode"`
	DiscountValue          float64 `xlsx:"Discount Value"`
	DiscountRate           float64 `xlsx:"Discount Rate %"`
	Remark                 string  `xlsx:"Remark"`
	StartTime              string  `xlsx:"Effective Start Time"`
	EndTime                string  `xlsx:"Effective End Time"`
	DisableTime            string  `xlsx:"Disable Time"`
	Status                 string  `xlsx:"Rate Status"`
	Creator                string  `xlsx:"Creator"`
	Operator               string  `xlsx:"Last Operator"`
}

type DiscountRuleImportErrorTemplate struct {
	ErrMsg string `xlsx:"Error Message"`
}

type MerchantTypeDiscountRuleExportTemplate struct {
	DiscountRuleId         int64   `xlsx:"Discount Rule ID"`
	MerchantType           string  `xlsx:"Merchant Type"`
	ServiceId              string  `xlsx:"Product ID"`
	MaxCapPerOrder         int     `xlsx:"Discount Cap Amount"`
	SenderLocationRule     bool    `xlsx:"Sender Location Applicability"`
	SenderLocation         string  `xlsx:"Sender Province"`
	RouteZoneApplicability bool    `xlsx:"Route Zone Applicability"`
	RouteZone              string  `xlsx:"Route Code"`
	MinSpend               int     `xlsx:"Min Spend"`
	DiscountMode           string  `xlsx:"Discount Mode"`
	DiscountValue          float64 `xlsx:"Discount Value"`
	DiscountRate           float64 `xlsx:"Discount Rate %"`
	Remark                 string  `xlsx:"Remark"`
	StartTime              string  `xlsx:"Effective Start Time"`
	EndTime                string  `xlsx:"Effective End Time"`
	DisableTime            string  `xlsx:"Disable Time"`
	Status                 string  `xlsx:"Rate Status"`
	Creator                string  `xlsx:"Creator"`
	Operator               string  `xlsx:"Last Operator"`
}

type PromotionDiscountRuleExportTemplate struct {
	DiscountRuleId         int64   `xlsx:"Discount Rule ID"`
	PromotionGroupId       string  `xlsx:"Promotion Group ID"`
	ServiceId              string  `xlsx:"Product ID"`
	MaxCapPerOrder         int     `xlsx:"Discount Cap Amount"`
	SenderLocationRule     bool    `xlsx:"Sender Location Applicability"`
	SenderLocation         string  `xlsx:"Sender Province"`
	RouteZoneApplicability bool    `xlsx:"Route Zone Applicability"`
	RouteZone              string  `xlsx:"Route Code"`
	MinSpend               int     `xlsx:"Min Spend"`
	DiscountMode           string  `xlsx:"Discount Mode"`
	DiscountValue          float64 `xlsx:"Discount Value"`
	DiscountRate           float64 `xlsx:"Discount Rate %"`
	Remark                 string  `xlsx:"Remark"`
	StartTime              string  `xlsx:"Effective Start Time"`
	EndTime                string  `xlsx:"Effective End Time"`
	DisableTime            string  `xlsx:"Disable Time"`
	Status                 string  `xlsx:"Rate Status"`
	Creator                string  `xlsx:"Creator"`
	Operator               string  `xlsx:"Last Operator"`
}

type MassCreateDiscountRuleTemplate interface {
	GetAccountId() uint64
	GetMerchantType() string
	GetServiceId() string
	GetDiscountMode() string
	GetDiscountValue() *float64
	GetDiscountRate() *float64
	GetRemark() string
	GetErrorMsg() string
	SetErrorMsg(string)
	GetTemplateString() string
	GetUniqueKey() string
	GetMaxCapPerOrder() int64
	GetSenderLocationRule() bool
	GetSenderLocation() []int64
	GetRouteZoneApplicability() bool
	GetRouteZone() []string
	GetRouteName() string
	GetMinSpeed() int64
	GetPromotionGroupId() string
}

type MerchantTypeDiscountRuleTemplate struct {
	MerchantType           string   `json:"merchant_type" xlsx:"* Merchant Type" binding:"required"`
	ServiceId              string   `json:"product_id" xlsx:"* Product ID" binding:"required"`
	MaxCapPerOrder         int64    `json:"max_cap_per_order" xlsx:"* Discount Cap Amount" binding:"gte=0,lte=*********"`
	SenderLocationRule     *bool    `json:"sender_location_rule" xlsx:"* Sender Location Applicability" binding:"required"`
	SenderLocation         string   `json:"sender_location" xlsx:"Sender Province"`
	RouteZoneApplicability *bool    `json:"route_zone_applicability" xlsx:"* Route Zone Applicability" binding:"required"`
	RouteName              string   `json:"route_name" xlsx:"Route Name"`
	RouteZone              string   `json:"route_zone" xlsx:"Route Code"`
	MinSpeed               int64    `json:"min_speed" xlsx:"* Min Spend" binding:"gte=0,lte=*********"`
	DiscountMode           string   `json:"discount_mode" xlsx:"* Discount Mode for the Basic Shipping Fee" binding:"required"`
	DiscountValue          *float64 `json:"discount_value" xlsx:"Discount Value" binding:"omitempty,gte=0"`
	DiscountRate           *float64 `json:"discount_rate" xlsx:"Discount Rate (%) (Remark: Input a number from 0 to 100)" binding:"omitempty,gte=0,lt=100"`
	Remark                 string   `json:"remark" xlsx:"Remark"`
	ErrorMsg               string   `xlsx:"Error Message"`
}

func (m *MerchantTypeDiscountRuleTemplate) GetMaxCapPerOrder() int64 {
	return m.MaxCapPerOrder
}

func (m *MerchantTypeDiscountRuleTemplate) GetSenderLocationRule() bool {
	return *m.SenderLocationRule
}

func (m *MerchantTypeDiscountRuleTemplate) GetSenderLocation() []int64 {
	locationIds := strings.Split(m.SenderLocation, ",")
	res := make([]int64, 0)
	for _, v := range locationIds {
		v = strings.TrimSpace(v)
		res = append(res, cast.ToInt64(v))
	}
	return res
}

func (m *MerchantTypeDiscountRuleTemplate) GetRouteZoneApplicability() bool {
	return *m.RouteZoneApplicability
}

func (m *MerchantTypeDiscountRuleTemplate) GetRouteZone() []string {
	res := make([]string, 0)
	for _, v := range strings.Split(m.RouteZone, ",") {
		res = append(res, strings.TrimSpace(v))
	}
	return res
}

func (m *MerchantTypeDiscountRuleTemplate) GetMinSpeed() int64 {
	return m.MinSpeed
}

func (m *MerchantTypeDiscountRuleTemplate) GetUniqueKey() string {
	return fmt.Sprintf("%s_%s", m.MerchantType, m.ServiceId)
}

func (m *MerchantTypeDiscountRuleTemplate) GetErrorMsg() string {
	return m.ErrorMsg
}

func (m *MerchantTypeDiscountRuleTemplate) GetAccountId() uint64 {
	return 0
}

func (m *MerchantTypeDiscountRuleTemplate) GetMerchantType() string {
	return m.MerchantType
}

func (m *MerchantTypeDiscountRuleTemplate) GetServiceId() string {
	return m.ServiceId
}

func (m *MerchantTypeDiscountRuleTemplate) GetDiscountMode() string {
	return m.DiscountMode
}

func (m *MerchantTypeDiscountRuleTemplate) GetDiscountValue() *float64 {
	return m.DiscountValue
}

func (m *MerchantTypeDiscountRuleTemplate) GetDiscountRate() *float64 {
	return m.DiscountRate
}

func (m *MerchantTypeDiscountRuleTemplate) GetRemark() string {
	return m.Remark
}

func (m *MerchantTypeDiscountRuleTemplate) SetErrorMsg(s string) {
	m.ErrorMsg = s
}

func (m *MerchantTypeDiscountRuleTemplate) GetTemplateString() string {
	return "\"* Merchant Type\", \"* Product ID\",\"* Discount Cap Amount\",\"* Sender Location Applicability\",\"Sender Province\",\"* Route Zone Applicability\",\"Route Name\",\"Route Code\",\"* Min Spend\", \"* Discount Mode for the Basic Shipping Fee\", \"* Discount Value\", \"Discount Rate (%) (Remark: Input a number from 0 to 100)\", \"Remark\""
}

func (m *MerchantTypeDiscountRuleTemplate) GetPromotionGroupId() string {
	return ""
}

func (m *MerchantTypeDiscountRuleTemplate) GetRouteName() string {
	return m.RouteName
}

func (d *DiscountRuleTemplate) GetAccountId() uint64 {
	return d.AccountId
}

func (d *DiscountRuleTemplate) GetMerchantType() string {
	return enum.EmptyStr
}

func (d *DiscountRuleTemplate) GetServiceId() string {
	return d.ServiceId
}

func (d *DiscountRuleTemplate) GetDiscountMode() string {
	return d.DiscountMode
}

func (d *DiscountRuleTemplate) GetDiscountValue() *float64 {
	return d.DiscountValue
}

func (d *DiscountRuleTemplate) GetDiscountRate() *float64 {
	return d.DiscountRate
}

func (d *DiscountRuleTemplate) GetRemark() string {
	return d.Remark
}

func (d *DiscountRuleTemplate) SetErrorMsg(s string) {
	d.ErrorMsg = s
}

func (d *DiscountRuleTemplate) GetTemplateString() string {
	return "\"*Account ID\", \"* Product ID\",\"* Discount Cap Amount\",\"* Sender Location Applicability\",\"Sender Province\",\"* Route Zone Applicability\",\"Route Name\",\"Route Code\",\"* Min Spend\", \"* Discount Mode for the Basic Shipping Fee\", \"* Discount Value\", \"Discount Rate (%) (Remark: Input a number from 0 to 100)\", \"Remark\""
}

func (d *DiscountRuleTemplate) GetErrorMsg() string {
	return d.ErrorMsg
}

func (d *DiscountRuleTemplate) GetMaxCapPerOrder() int64 {
	return d.MaxCapPerOrder
}

func (d *DiscountRuleTemplate) GetSenderLocationRule() bool {
	return *d.SenderLocationRule
}

func (d *DiscountRuleTemplate) GetSenderLocation() []int64 {
	locationIds := strings.Split(d.SenderLocation, ",")
	res := make([]int64, 0)
	for _, v := range locationIds {
		v = strings.TrimSpace(v)
		res = append(res, cast.ToInt64(v))
	}
	return res
}

func (d *DiscountRuleTemplate) GetRouteZoneApplicability() bool {
	return *d.RouteZoneApplicability
}

func (d *DiscountRuleTemplate) GetRouteZone() []string {
	res := make([]string, 0)
	for _, v := range strings.Split(d.RouteZone, ",") {
		res = append(res, strings.TrimSpace(v))
	}
	return res
}

func (d *DiscountRuleTemplate) GetMinSpeed() int64 {
	return d.MinSpend
}

func (d *DiscountRuleTemplate) GetPromotionGroupId() string {
	return ""
}

func (d *DiscountRuleTemplate) GetRouteName() string {
	return d.RouteName
}

type PromotionDiscountRuleTemplate struct {
	PromotionGroupId       string   `json:"promotion_group_id" xlsx:"* Promotion Group ID"  binding:"required"`
	ServiceId              string   `json:"product_id" xlsx:"* Product ID" binding:"required"`
	MaxCapPerOrder         int64    `json:"max_cap_per_order" xlsx:"* Discount Cap Amount" binding:"gte=0,lte=*********"`
	SenderLocationRule     *bool    `json:"sender_location_rule" xlsx:"* Sender Location Applicability" binding:"required"`
	SenderLocation         string   `json:"sender_location" xlsx:"Sender Province"`
	RouteZoneApplicability *bool    `json:"route_zone_applicability" xlsx:"* Route Zone Applicability" binding:"required"`
	RouteName              string   `json:"route_name" xlsx:"Route Name"`
	RouteZone              string   `json:"route_zone" xlsx:"Route Code"`
	MinSpend               int64    `json:"min_spend" xlsx:"* Min Spend" binding:"gte=0,lte=*********"`
	DiscountMode           string   `json:"discount_mode" xlsx:"* Discount Mode for the Basic Shipping Fee" binding:"required"`
	DiscountValue          *float64 `json:"discount_value" xlsx:"Discount Value" binding:"omitempty,gte=0"`
	DiscountRate           *float64 `json:"discount_rate" xlsx:"Discount Rate (%) (Remark: Input a number from 0 to 100)" binding:"omitempty,gte=0,lt=100"`
	Remark                 string   `json:"remark" xlsx:"Remark"`
	ErrorMsg               string   `xlsx:"Error Message"`
}

func (p *PromotionDiscountRuleTemplate) GetMaxCapPerOrder() int64 {
	return p.MaxCapPerOrder
}

func (p *PromotionDiscountRuleTemplate) GetSenderLocationRule() bool {
	return *p.SenderLocationRule
}

func (p *PromotionDiscountRuleTemplate) GetSenderLocation() []int64 {
	locationIds := strings.Split(p.SenderLocation, ",")
	res := make([]int64, 0)
	for _, v := range locationIds {
		v = strings.TrimSpace(v)
		res = append(res, cast.ToInt64(v))
	}
	return res
}

func (p *PromotionDiscountRuleTemplate) GetRouteZoneApplicability() bool {
	return *p.RouteZoneApplicability
}

func (p *PromotionDiscountRuleTemplate) GetRouteZone() []string {
	res := make([]string, 0)
	for _, v := range strings.Split(p.RouteZone, ",") {
		res = append(res, strings.TrimSpace(v))
	}
	return res
}

func (p *PromotionDiscountRuleTemplate) GetMinSpeed() int64 {
	return p.MinSpend
}

func (p *PromotionDiscountRuleTemplate) GetUniqueKey() string {
	return fmt.Sprintf("%s_%s", p.PromotionGroupId, p.ServiceId)
}

func (p *PromotionDiscountRuleTemplate) GetErrorMsg() string {
	return p.ErrorMsg
}

func (p *PromotionDiscountRuleTemplate) GetAccountId() uint64 {
	return 0
}

func (p *PromotionDiscountRuleTemplate) GetMerchantType() string {
	return enum.EmptyStr
}

func (p *PromotionDiscountRuleTemplate) GetServiceId() string {
	return p.ServiceId
}

func (p *PromotionDiscountRuleTemplate) GetDiscountMode() string {
	return p.DiscountMode
}

func (p *PromotionDiscountRuleTemplate) GetDiscountValue() *float64 {
	return p.DiscountValue
}

func (p *PromotionDiscountRuleTemplate) GetDiscountRate() *float64 {
	return p.DiscountRate
}

func (p *PromotionDiscountRuleTemplate) GetRemark() string {
	return p.Remark
}

func (p *PromotionDiscountRuleTemplate) SetErrorMsg(s string) {
	p.ErrorMsg = s
}

func (p *PromotionDiscountRuleTemplate) GetTemplateString() string {
	return "\"* Promotion Group ID\", \"* Product ID\",\"*Discount Cap Amount\",\"*Sender Location Applicability\",\"Sender Province\",\"*Route Zone Applicability\",\"Route Name\",\"Route Code\",\"*Min Spend\", \"* Discount Mode for the Basic Shipping Fee\", \"* Discount Value\", \"Discount Rate (%) (Remark: Input a number from 0 to 100)\", \"Remark\""
}

func (p *PromotionDiscountRuleTemplate) GetPromotionGroupId() string {
	return p.PromotionGroupId
}

func (p *PromotionDiscountRuleTemplate) GetRouteName() string {
	return p.RouteName
}
