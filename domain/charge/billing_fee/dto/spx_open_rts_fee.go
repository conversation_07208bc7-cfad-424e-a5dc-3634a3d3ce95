package dto

import (
	do2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	utils "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/utils/common"
)

type CreateSpxOpenRtsFeeRequest struct {
	RateID uint64              `json:"rate_id" binding:"required"`
	Data   SpxOpenRtsFeeDetail `json:"data" binding:"required"`
}

func (c CreateSpxOpenRtsFeeRequest) NeedRateTabHelpValidate() bool {
	return false
}

func (c CreateSpxOpenRtsFeeRequest) GetRateId() uint64 {
	return c.RateID
}

func (c CreateSpxOpenRtsFeeRequest) GetExtraFeeComponent() enum.ExtraFeeComponent {
	return enum.ReturnServiceFee
}

func (c CreateSpxOpenRtsFeeRequest) Transfer2ComplexTabs(ctx dbhelper.BmsContext) []do2.IExtraFee {
	return []do2.IExtraFee{&do2.ComplexExtraFeeTab{
		RateId:           c.GetRateId(),
		ExtraFeeType:     int8(c.GetExtraFeeComponent()),
		ExtraFeeStrategy: int32(c.Data.ExtraFeeStrategy),
		FixedValue:       c.Data.FixedValue,
		Percentage:       c.Data.Percentage,
		ExtraData:        "{}",
		Ctime:            utils.GetTimestamp(ctx),
		Mtime:            utils.GetTimestamp(ctx),
	}}
}

func (c CreateSpxOpenRtsFeeRequest) Validate(ctx dbhelper.BmsContext) error {
	return nil
}

type SpxOpenRtsFeeDetail struct {
	BaseExtraFeeItem
}

type QuerySpxOpenRtsRequest struct {
	RateID uint64 `form:"rate_id" binding:"required"`
}

func (q QuerySpxOpenRtsRequest) GetRateId() uint64 {
	return q.RateID
}

func (q QuerySpxOpenRtsRequest) GetExtraFeeComponent() enum.ExtraFeeComponent {
	return enum.ReturnServiceFee
}

type QuerySpxOpenRtsResp struct {
	SpxOpenFeeInfo SpxOpenRtsFeeDetail `json:"seller_fee_info"`
}
