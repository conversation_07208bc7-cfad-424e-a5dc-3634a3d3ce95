package persistence

import (
	"context"
	"errors"
	"fmt"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

type IRemoteLocationDao interface {
	HasRemoteLocation(ctx dbhelper.BmsContext, rateID uint64, locationID int64, postcode string) (bool, error)
	// GetRemoteLocationsByRateID 查找当前rate_id 对应的RemoteLocation
	GetRemoteLocationsByRateID(ctx dbhelper.BmsContext, rateID uint64, condition map[string]interface{}) ([]do.RemoteLocationTab, error)
	// BatchDeleteAndCreate 批量删除 rate_id对应的记录 ，然后插入新的值
	BatchDeleteAndCreate(ctx dbhelper.BmsContext, remoteLocations []do.RemoteLocationTab) error
	// HasConfiguredRemoteLocation 是否配置了 remote location
	HasConfiguredRemoteLocation(ctx dbhelper.BmsContext, rateID uint64) (bool, error)
	// DeleteByRateId 按费率表 id 删除
	DeleteByRateId(ctx dbhelper.BmsContext, rateId uint32, condition map[string]interface{}) error
	// DeleteByRateIdWithLimit 按费率表 id 删除
	DeleteByRateIdWithLimit(ctx dbhelper.BmsContext, rateId uint32, limit int) *retcode.SSCError
	// BatchDeleteAndCreateByCondition 根据条件删除并创建
	BatchDeleteAndCreateByCondition(ctx dbhelper.BmsContext, remoteLocations []do.RemoteLocationTab, rateId uint32, condition map[string]interface{}) error

	GetRemoteLocationsWithLimit(ctx dbhelper.BmsContext, condition map[string]interface{}, offset int32, size int32) (int64, []do.RemoteLocationTab, error)
}

type remoteLocationDao struct {
}

func NewRemoteLocationDao() *remoteLocationDao {
	return &remoteLocationDao{}
}

func (r remoteLocationDao) HasConfiguredRemoteLocation(ctx dbhelper.BmsContext, rateID uint64) (bool, error) {
	params := map[string]interface{}{
		"rate_channel_id": rateID,
	}
	dbResult := make([]do.RemoteLocationTab, 0)
	sscError := dbhelper.SearchAllOrderDataWithLimit(ctx, &do.RemoteLocationTab{}, &dbResult, params, "id", 1)
	if sscError != nil {
		return false, sscError.NewError()
	}
	return len(dbResult) > 0, nil
}

func (r remoteLocationDao) HasRemoteLocation(ctx dbhelper.BmsContext, rateID uint64, locationID int64, postcode string) (bool, error) {
	params := map[string]interface{}{
		"rate_channel_id": rateID,
	}
	if postcode != "" {
		params["postcode"] = postcode
	} else {
		params["location_id"] = locationID
	}
	dbResult := make([]do.RemoteLocationTab, 0)
	sscError := dbhelper.SearchAllData(ctx, &do.RemoteLocationTab{}, &dbResult, params)
	if sscError != nil {
		return false, sscError.NewError()
	}
	return len(dbResult) > 0, nil
}

func (r remoteLocationDao) GetRemoteLocationsByRateID(ctx dbhelper.BmsContext, rateID uint64, condition map[string]interface{}) ([]do.RemoteLocationTab, error) {
	var remoteLocationTabs = make([]do.RemoteLocationTab, 0)
	param := map[string]interface{}{}
	if condition != nil {
		param = condition
	}
	param["rate_channel_id"] = rateID

	sscError := dbhelper.SearchAllData(ctx, &do.RemoteLocationTab{}, &remoteLocationTabs, param)
	if sscError != nil {
		logger.CtxLogErrorf(ctx, "con not found the remote locations, error = %v , param is %+v", sscError, param)
		return nil, fmt.Errorf("con not found the remote location error")
	}
	return remoteLocationTabs, nil
}

func (r remoteLocationDao) BatchDeleteAndCreate(ctx dbhelper.BmsContext, remoteLocations []do.RemoteLocationTab) error {
	var rateIds = make([]uint64, len(remoteLocations))
	for idx := range remoteLocations {
		rateIds[idx] = remoteLocations[idx].RateChannelId
	}
	// 设置事务的回调函数
	fc := func() *retcode.SSCError {
		// 批量删除数据根据rate_id
		err := dbhelper.BatchDeleteByCustomIDWithLimit(ctx, &do.RemoteLocationTab{}, rateIds, 500, "rate_channel_id")
		if err != nil {
			logger.CtxLogErrorf(ctx, "batch delete rate location error , error = %+v , rate location ids = %+v", err, rateIds)
			return err
		}
		// create 数据
		err = dbhelper.BatchCreateData(ctx, remoteLocations)
		if err != nil {
			logger.CtxLogErrorf(ctx, "create rate location error,  error = %+v , rate location tabs = %+v", err, remoteLocations)
			return err
		}
		return nil
	}
	// 开启事务
	if err := ctx.Transaction(fc); err != nil {
		return err.NewError()
	}
	return nil
}

func (r remoteLocationDao) DeleteByRateId(ctx dbhelper.BmsContext, rateId uint32, condition map[string]interface{}) error {
	param := map[string]interface{}{}
	if condition != nil {
		param = condition
	}

	param["rate_channel_id"] = rateId
	sscErr := dbhelper.DeleteDataByParams(ctx, &do.RemoteLocationTab{}, param)
	if sscErr != nil {
		return sscErr.NewError()
	}
	return nil

}

func (r remoteLocationDao) DeleteByRateIdWithLimit(ctx dbhelper.BmsContext, rateId uint32, limit int) *retcode.SSCError {
	return dbhelper.BatchDeleteByIDWithLimit(ctx, &do.RemoteLocationTab{}, map[string]interface{}{"rate_channel_id": rateId}, int64(limit))
}

func (r remoteLocationDao) BatchDeleteAndCreateByCondition(ctx dbhelper.BmsContext, remoteLocations []do.RemoteLocationTab, rateId uint32, condition map[string]interface{}) error {
	dbCtx := scormv2.BindContext(ctx, ctx.WriteDB())
	err := scormv2.PropagationRequired(dbCtx, func(iCtx context.Context) error {
		param := map[string]interface{}{}
		if condition != nil {
			param = condition
		}

		param["rate_channel_id"] = rateId
		sscErr := dbhelper.DeleteDataByParams(ctx, &do.RemoteLocationTab{}, param)
		if sscErr != nil {
			return sscErr.NewError()
		}

		// create 数据
		sscErr = dbhelper.BatchCreateData(ctx, remoteLocations)
		if sscErr != nil {
			logger.CtxLogErrorf(ctx, "create rate location error,  error = %+v , rate location tabs = %+v, rateid: %d", sscErr, remoteLocations, rateId)
			return sscErr.NewError()
		}
		return nil
	})

	if err != nil {
		return errors.New(err.Error())
	}
	return nil
}

func (r remoteLocationDao) GetRemoteLocationsWithLimit(ctx dbhelper.BmsContext, condition map[string]interface{}, offset int32, size int32) (int64, []do.RemoteLocationTab, error) {
	var remoteLocationTabs = make([]do.RemoteLocationTab, 0)

	total, sscError := dbhelper.SearchAllDataWithOffset(ctx, do.RemoteLocationTableName, &remoteLocationTabs, int(offset), int(size), condition)
	if sscError != nil {
		logger.CtxLogErrorf(ctx, "con not found the remote locations, error = %v , param is %+v", sscError, condition)
		return 0, nil, sscError
	}
	return total, remoteLocationTabs, nil
}
