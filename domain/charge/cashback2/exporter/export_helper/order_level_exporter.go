package export_helper

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/excel/excel_config"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/excel/excel_exporter"
)

type OrderLevelExporter struct {
}

func (exporter OrderLevelExporter) GetSheetName() string {
	return "3PL TN Level Cashback"
}

func (exporter OrderLevelExporter) GenerateFileName(input string) string {
	return ""
}

func (exporter OrderLevelExporter) GetFilePath() string {
	return "/tmp/cashback_export_file/"
}

func (exporter OrderLevelExporter) GetTemplateHeader() excel_exporter.ExcelTplRow {
	return excel_exporter.ExcelTplRow{
		"Billing ID",
		"3PL TN",
		"Cashback (Fixed Amount per Order)",
		"Cashback (Fixed Amount per Order) success / fail",
		"fail reason",
		"Cashback (Fixed % per Order)",
		"Cashback (Fixed % per Order) success / fail",
		"fail reason",
	}
}

func (exporter OrderLevelExporter) GetColWidth() float64 {
	return excel_config.DefaultGoogleSheetColWidth
}
