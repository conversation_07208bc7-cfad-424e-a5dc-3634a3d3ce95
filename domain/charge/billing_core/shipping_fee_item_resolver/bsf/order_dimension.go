/**
    @author:qingping
    @since:2023/1/30
    @desc: //该bsf计算器使用size id模式进行匹配bsf
**/

package bsf

import (
	"fmt"
	"math"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_core/shipping_fee_item_resolver/definition"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/entity"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/store"
)

type orderDimensionResolver struct {
}

// by dimension计算或匹配 Basic Shipping Fee 需要的参数
type dimensionBsfCalculationInputParams struct {
	rateId               uint32                    // 费率表 id
	basicShippingFeeRule enum.BasicShippingFeeRule // 基础运费规则
	bsfLevel             enum.BsfLevel             // 计费分区类型
	bound                enum.Bound                // 左闭右开
	codes                []string                  // 分区码
	length               float64                   // dimension 长 宽 高, charged dimension 信息，此次暂未用到
	width                float64
	height               float64
	sizeId               int64
	dimension            int32
	feeType              enum.FeeType
	usePastRateTable     bool
}

func (l *dimensionBsfCalculationInputParams) GetShippingFeeComponent() enum.ShippingFeeComponent {
	return enum.ShippingFeeComponentBasicShippingFee
}

func (o orderDimensionResolver) NewShippingFeeItemCalculationParams(ctx dbhelper.BmsContext, calcParam definition.CalculateParams) definition.ShippingFeeItemCalculationParams {
	params := calcParam.(*definition.BaseCalculateTotalFeeComponentParams)
	//回写siz id 只影响本次新增的计费场景
	params.CalculationReq.MatchingInfo.CarrierSizeId = params.ChargedDimensionResult.SizeInfo.SizeId
	rateTable := params.RateTable
	codes := params.LocationCode.GetCodes()
	return &dimensionBsfCalculationInputParams{
		rateId:               rateTable.GetRateId(),
		basicShippingFeeRule: rateTable.GetBasicShippingFeeRule(),
		bsfLevel:             rateTable.GetBsfLevel(),
		codes:                codes,
		length:               params.ChargedDimensionResult.SizeInfo.Length,
		width:                params.ChargedDimensionResult.SizeInfo.Width,
		height:               params.ChargedDimensionResult.SizeInfo.Height,
		sizeId:               params.ChargedDimensionResult.SizeInfo.SizeId,
		feeType:              rateTable.GetFeeTypeId(),
		usePastRateTable:     params.PickOptimalProcessParams.GetUsePastRateTable(),
	}
}

func (o orderDimensionResolver) CalculateRawShippingFee(ctx dbhelper.BmsContext, shippingFeeItemCalculationParams definition.ShippingFeeItemCalculationParams) (float64, definition.FeeItemConfig, *retcode.SSCError) {
	bsfCalculationInputParams := shippingFeeItemCalculationParams.(*dimensionBsfCalculationInputParams)
	bsfConfigList := store.GetInstance(ctx.GetStoreType()).GetBasicShippingFeeConfigList(ctx, bsfCalculationInputParams.rateId, bsfCalculationInputParams.bsfLevel, bsfCalculationInputParams.codes,
		dto.SetUsePastRateTable(bsfCalculationInputParams.usePastRateTable))
	if bsfConfigList == nil || len(bsfConfigList) == 0 {
		return 0, nil, retcode.BasicShippingFeeNotFound.CloneWithDetailFormat("bsfCalculationInputParams.codes:%v", bsfCalculationInputParams.codes)
	}
	dimension := int64(math.Round(bsfCalculationInputParams.length + bsfCalculationInputParams.height + bsfCalculationInputParams.width))
	var fee float64
	var config entity.IBasicShippingFeeConfig
	var err *retcode.SSCError
	//用size id算
	if bsfCalculationInputParams.sizeId != 0 {
		fee, config, err = o.calculateBySizeId(ctx, bsfConfigList, *bsfCalculationInputParams)
	}
	//用l+h+w 计算
	if bsfCalculationInputParams.sizeId == 0 && dimension > 0 {
		fee, config, err = o.calculateByDimension(ctx, bsfConfigList, *bsfCalculationInputParams, dimension)
	}
	if err != nil {
		logger.CtxLogErrorf(ctx, "CalculateRawShippingFee Failed: %v", err)
		return 0, nil, err
	}
	return fee, config, nil
}

func (o orderDimensionResolver) calculateBySizeId(ctx dbhelper.BmsContext, bsfConfigList entity.BasicShippingFeeConfigList, bsfCalculationInputParams dimensionBsfCalculationInputParams) (float64, entity.IBasicShippingFeeConfig, *retcode.SSCError) {
	config, err := o.matchUniqueBasicShippingFeeConfig(bsfConfigList, bsfCalculationInputParams)
	if err != nil {
		return 0, nil, retcode.BasicShippingFeeNotMatch.CloneWithDetailFormat("%v", err.Error())
	}
	fee, err := config.CalculateBySizeId()
	if err != nil {
		return 0, nil, retcode.BasicShippingFeeCalculateException.CloneWithDetailFormat("%v", err.Error())
	}
	//设置可视化数据，计算策略有变动记得修改这个
	config.SetBsfProcessVisualDataByWeightMode(ctx, bsfCalculationInputParams.feeType, 0, fee, nil)
	return fee, config, nil
}
func (o orderDimensionResolver) calculateByDimension(ctx dbhelper.BmsContext, bsfConfigList entity.BasicShippingFeeConfigList, bsfCalculationInputParams dimensionBsfCalculationInputParams, dimension int64) (float64, entity.IBasicShippingFeeConfig, *retcode.SSCError) {
	config, err := o.matchDimensionUniqueBasicShippingFeeConfig(bsfConfigList, bsfCalculationInputParams, dimension)
	if err != nil {
		return 0, nil, retcode.BasicShippingFeeNotMatch.CloneWithDetailFormat("%v", err.Error())
	}
	fee, err := config.CalculateByDimension(float64(dimension))
	if err != nil {
		return 0, nil, retcode.BasicShippingFeeCalculateException.CloneWithDetailFormat("%v", err.Error())
	}
	//设置可视化数据，计算策略有变动记得修改这个
	config.SetBsfProcessVisualDataByWeightMode(ctx, bsfCalculationInputParams.feeType, 0, fee, nil)
	return fee, config, nil
}

func (o orderDimensionResolver) matchUniqueBasicShippingFeeConfig(basicShippingFeeConfigList entity.BasicShippingFeeConfigList, params dimensionBsfCalculationInputParams) (entity.IBasicShippingFeeConfig, error) {
	for i := range basicShippingFeeConfigList {
		v := basicShippingFeeConfigList[i]
		if v.GetSizeID() == params.sizeId && v.GetSizeID() != 0 {
			return v, nil
		}
	}
	return nil, fmt.Errorf("match Unique BasicShippingFeeConfig failed, parameters is %v", params)
}

func (o orderDimensionResolver) matchDimensionUniqueBasicShippingFeeConfig(basicShippingFeeConfigList entity.BasicShippingFeeConfigList, params dimensionBsfCalculationInputParams, dimension int64) (entity.IBasicShippingFeeConfig, error) {
	for i := range basicShippingFeeConfigList {
		v := basicShippingFeeConfigList[i]
		if v.GetFeeStrategy() == enum.BsfStrategyFlat ||
			v.GetFeeStrategy() == enum.BsfMultiply {
			return v, nil
		}
		if basicShippingFeeConfigList[i].GetEndDimension() > dimension && basicShippingFeeConfigList[i].GetStartDimension() < dimension {
			return v, nil
		}
		if params.bound == enum.LeftIncluded && basicShippingFeeConfigList[i].GetStartDimension() == dimension {
			return v, nil
		}
		if params.bound == enum.RightIncluded && basicShippingFeeConfigList[i].GetEndDimension() == dimension {
			return v, nil
		}
	}
	return nil, fmt.Errorf("match Unique BasicShippingFeeConfig failed, parameters is %v", params)
}
