package bsf

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_core/shipping_fee_item_resolver/definition"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/service/visualSupport"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

type equalThreePlPushedFeeBsfResolver struct {
}

func NewEqualThreePlPushedFeeBsfResolver() *equalThreePlPushedFeeBsfResolver {
	return &equalThreePlPushedFeeBsfResolver{}
}

type equalThreePlPushedFeeParams struct {
	threePlPushedFee     float64
	estimatedShippingFee float64
}

func (a *equalThreePlPushedFeeParams) GetShippingFeeComponent() enum.ShippingFeeComponent {
	return enum.ShippingFeeComponentBasicShippingFee
}

func (a *equalThreePlPushedFeeBsfResolver) NewShippingFeeItemCalculationParams(_ dbhelper.BmsContext, calcParam definition.CalculateParams) definition.ShippingFeeItemCalculationParams {
	params := calcParam.(*definition.BaseCalculateTotalFeeComponentParams)
	var esf float64
	if params.CalculationReq.OrderInfo != nil {
		esf = params.CalculationReq.OrderInfo.EstimatedShippingFee
	}
	return &equalThreePlPushedFeeParams{
		threePlPushedFee:     params.CalculationReq.OrderInfo.ThreePlPushedFee, // nolint
		estimatedShippingFee: esf,
	}
}

func (a *equalThreePlPushedFeeBsfResolver) CalculateRawShippingFee(ctx dbhelper.BmsContext, params definition.ShippingFeeItemCalculationParams) (float64, definition.FeeItemConfig, *retcode.SSCError) {
	threePlPushedFeeParams := params.(*equalThreePlPushedFeeParams)
	visualSupport.SetBsfOriginRawFeeVisualData(ctx, threePlPushedFeeParams.threePlPushedFee)
	afAsfFlag, exists := ctx.Get("af_asf_flag")
	if exists && afAsfFlag.(bool) {
		return threePlPushedFeeParams.estimatedShippingFee, nil, nil
	}
	return threePlPushedFeeParams.threePlPushedFee, nil, nil
}
