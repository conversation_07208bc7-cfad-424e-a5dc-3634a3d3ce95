package define

import (
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/cash_back"
	core_dto "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/cash_back/dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
	"github.com/shopspring/decimal"
)

func CalculationFactor(calculationParameter *core_dto.CalculationParameter) (*core_dto.CalculationFactor, *retcode.SSCError) {
	if calculationParameter == nil {
		return nil, retcode.CalculateParamNil
	}
	serviceId, error := strconv.Atoi(calculationParameter.ServiceId)
	if error != nil {
		return nil, retcode.ServiceIdFormatError
	}
	//实时计算
	calculationFactor := &core_dto.CalculationFactor{
		ScfsOrderId:        calculationParameter.ScfsOrderId,
		ThreePLOrderNumber: calculationParameter.ThreePLOrderNumber,
		BillId:             calculationParameter.BillId,
		ServiceId:          int64(serviceId),
		PickUpAddress:      calculationParameter.PickUpAddress,
		DeliveryAddres:     calculationParameter.DeliveryAddres,
		PaymentType:        calculationParameter.PaymentType,
		FulfilmentType:     calculationParameter.FulfilmentType,
		ListingWeight:      calculationParameter.ListingWeight,
		ThreePLPushWeight:  calculationParameter.ThreePLPushWeight,
		BSF:                calculationParameter.BSF,
		CODServiceFee:      calculationParameter.CODServiceFee,
		InsuranceFee:       calculationParameter.InsuranceFee,
		RTSFee:             calculationParameter.RTSFee,
		RemoteFee:          calculationParameter.RemoteFee,
		Country:            calculationParameter.Country,
		FirstPushWeight:    calculationParameter.FirstPushWeight,
	}
	//实时和批量
	switch calculationParameter.TaskType {
	case cash_back.RealTime:
		if calculationParameter.FirstPushWeight {
			calculationFactor.TimeStamp = int64(calculationParameter.FirstPushWeightTime)
			calculationFactor.CalculateType = cash_back.CashBackEstimate
			calculationFactor.CalculateResultType = cash_back.EstimateCashbackAmt
		} else {
			stamp, err := findTimeStamp(calculationParameter.OrderStatusList, cash_back.CREATE_STATUS)
			if err != nil {
				return nil, err
			}
			calculationFactor.TimeStamp = stamp
			calculationFactor.CalculateType = cash_back.CashBackEstimate
			calculationFactor.CalculateResultType = cash_back.EstimateCashbackScAmt
		}
		break
	case cash_back.TaskPlan:
		if calculationParameter.BatchCalculateType == int(enum.CashBackEstimate) {
			calculationFactor.CalculateResultType = cash_back.EstimateCashbackScAmt
			calculationFactor.CalculateType = cash_back.CashBackEstimate
			stamp, err := findTimeStamp(calculationParameter.OrderStatusList, cash_back.CREATE_STATUS)
			if err != nil {
				return nil, err
			}
			calculationFactor.TimeStamp = stamp
		}
		break
	default:
		return nil, retcode.CalculateTypeError
		break
	}
	return calculationFactor, nil
}

// 寻找时间戳
func findTimeStamp(orderStatusList []core_dto.OrderStatus, orderStatus string) (int64, *retcode.SSCError) {
	if orderStatusList == nil {
		return 0, retcode.OrderStatusError
	}
	for _, v := range orderStatusList {
		if strings.Compare(v.OrderStatus, orderStatus) == 0 {
			return int64(v.TimeStemp), nil
		}
	}
	return 0, retcode.OrderStatusError
}

func CalculationResult(calculationParameter *core_dto.CalculationFactor, calculationResults float64) (*core_dto.CalculationResult, *retcode.SSCError) {
	serviceId := strconv.FormatInt(calculationParameter.ServiceId, 10)
	calculationResult := &core_dto.CalculationResult{
		ScfsOrderId:        calculationParameter.ScfsOrderId,
		ThreePLOrderNumber: calculationParameter.ThreePLOrderNumber,
		BillId:             calculationParameter.BillId,
		FeeType:            cash_back.FEE_TYPE,
		ServiceId:          serviceId,
	}
	// deprecated (old cashback)
	calculationResultStr := decimal.NewFromFloat(calculationResults).Round(4).String()
	switch calculationParameter.CalculateResultType {
	case cash_back.EstimateCashbackScAmt:
		calculationResult.EstimateCashbackScAmt = calculationResultStr
		break
	case cash_back.EstimateCashbackAmt:
		calculationResult.EstimateCashbackAmt = calculationResultStr
		break
	case cash_back.ActualCashbackAmt:
		calculationResult.ActualCashbackAmt = calculationResultStr
		break
	}
	return calculationResult, nil
}
