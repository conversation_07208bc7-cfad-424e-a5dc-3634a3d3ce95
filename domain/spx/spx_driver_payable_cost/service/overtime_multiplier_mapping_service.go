package service

import (
	"errors"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_driver_payable_cost/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_driver_payable_cost/dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_driver_payable_cost/repository"
	conf "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/config"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/constant"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum/spx_driver_payable_enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/excel"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/s3"
	utils "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/utils/common"
	"github.com/shopspring/decimal"
	"os"
	"sort"
	"strings"
)

const (
	MinOtHours    = 0
	MaxOtHours    = 24
	MinMultiplier = 1
	MaxMultiplier = 100

	ZeroDecimalPlace = 0
	TwoDecimalPlace  = 2
)

var OvertimeMultiplierMappingExcelHeaders = []string{"*Working Day Type", "*OT hours", "*Overtime Multiplier"}

type ISpxDriverPayableOvertimeMultiplierMappingService interface {
	CheckAndParseUploadFile(ctx dbhelper.BmsContext, s3Url string) ([]*do.SpxDriverPayableOvertimeMultiplierMappingTab, string, *retcode.SSCError)
	MassCreateOvertimeMultiplierMapping(ctx dbhelper.BmsContext, tas []*do.SpxDriverPayableOvertimeMultiplierMappingTab, ruleId int64) *retcode.SSCError
	ListOvertimeMultiplierMapping(ctx dbhelper.BmsContext, req *dto.SearchDetailAttributeConfigReq) (*dto.SearchDetailAttributeConfigResp, *retcode.SSCError)
	GetExportData(ctx dbhelper.BmsContext, req *dto.SearchDetailAttributeConfigReq) ([]string, [][]interface{}, *retcode.SSCError)
}

type spxDriverPayableOvertimeMultiplierMappingService struct {
	overtimeMultiplierMappingDao repository.ISpxDriverPayableOvertimeMultiplierMappingRepository
}

func (s *spxDriverPayableOvertimeMultiplierMappingService) MassCreateOvertimeMultiplierMapping(ctx dbhelper.BmsContext, tas []*do.SpxDriverPayableOvertimeMultiplierMappingTab, ruleId int64) *retcode.SSCError {
	for _, tab := range tas {
		tab.RuleID = ruleId
	}
	err := s.processOvertimeMultiplierMappingData(ctx, tas)
	if err != nil {
		logger.CtxLogErrorf(ctx, "MassCreateOvertimeMutiplierMapping processOvertimeMutiplierMappingData Error: %v", err)
		return retcode.CreateAttributeConfigError.CloneWithError(err)
	}
	return nil
}

func (s *spxDriverPayableOvertimeMultiplierMappingService) CheckAndParseUploadFile(ctx dbhelper.BmsContext, s3Url string) ([]*do.SpxDriverPayableOvertimeMultiplierMappingTab, string, *retcode.SSCError) {
	// 1.下载文件，结束时删除文件
	localFilePath, err := s3.DownloadObject(ctx, s3.GetTmpS3URL(ctx, s3Url))
	if err != nil {
		return nil, enum.EmptyStr, retcode.CreateAttributeConfigError.CloneWithError(err)
	}
	defer func(absolutePath string) {
		if err := os.Remove(absolutePath); err != nil {
			logger.CtxLogErrorf(ctx, "delete file in local error: file name = %s", localFilePath)
		}
	}(localFilePath)
	overtimeMultiplierDetailTabs, errUrl, err := s.parseOvertimeMultiplierMappingExcel(ctx, localFilePath)
	if err != nil {
		logger.CtxLogErrorf(ctx, "MassCreateOvertimeMutiplierMapping parseOvertimeMultiplierMappingExcel Error: %v", err)
		return nil, enum.EmptyStr, retcode.CreateAttributeConfigError.CloneWithError(err)
	}
	if errUrl != enum.EmptyStr {
		return nil, errUrl, nil
	}
	return overtimeMultiplierDetailTabs, "", nil
}

func (s *spxDriverPayableOvertimeMultiplierMappingService) writeErrorMessage(ctx dbhelper.BmsContext, filePath string, errCol []string, headerLen int) (string, error) {
	if err := excel.AddColumn(filePath, errCol, "Error Reason", 1, headerLen); err != nil {
		return "", fmt.Errorf("write excel error: %s", err.Error())
	}
	return s3.UploadObject(ctx, filePath, conf.GetChargeConf(ctx).S3Config)
}

func (s *spxDriverPayableOvertimeMultiplierMappingService) parseOvertimeMultiplierMappingExcel(ctx dbhelper.BmsContext, filePath string) ([]*do.SpxDriverPayableOvertimeMultiplierMappingTab, string, error) {
	rows, err := excel.ParseExcel(filePath, OvertimeMultiplierMappingExcelHeaders)
	if err != nil {
		if strings.Contains(err.Error(), "file is blank") {
			return nil, "", retcode.CreateAttributeConfigError.CloneWithDetail("Empty file, pls check")
		}
		return nil, enum.EmptyStr, retcode.CreateAttributeConfigError.CloneWithError(err)
	}
	res := make([]*do.SpxDriverPayableOvertimeMultiplierMappingTab, 0, len(rows))
	logger.CtxLogInfof(ctx, "content length=%d", len(rows))
	items := checkAndParseOvertimeMultiplierRows(ctx, rows)

	errMessage := make([]string, len(rows))
	var hasErr bool
	var errCount int
	for _, item := range items {
		if item.ErrorMessage != enum.EmptyStr {
			hasErr = true
			errMessage[item.RowId] = item.ErrorMessage
			errCount++
		}
	}

	logger.CtxLogInfof(ctx, "errMesageCount=%d", errCount)
	if hasErr {
		errReportUrl, err := s.writeErrorMessage(ctx, filePath, errMessage, len(OvertimeMultiplierMappingExcelHeaders))
		if err != nil {
			return nil, enum.EmptyStr, retcode.CreateAttributeConfigError.CloneWithError(err)
		}
		return res, errReportUrl, nil
	}
	now := utils.GetTimestamp(ctx)
	for _, item := range items {
		res = append(res, &do.SpxDriverPayableOvertimeMultiplierMappingTab{
			Region:         ctx.GetCountry(),
			WorkingDayType: item.WorkingDayType,
			OvertimeHours:  item.OvertimeHours,
			Multiplier:     item.Multiplier,
			CTime:          now,
			MTime:          now,
		})
	}

	return res, enum.EmptyStr, nil
}

func checkAndParseOvertimeMultiplierRows(ctx dbhelper.BmsContext, rows [][]string) []*dto.OvertimeMultiplierMappingUploadItem {
	workingDayTypeCacheMap := make(map[string]string, len(rows))
	var uploadItems []*dto.OvertimeMultiplierMappingUploadItem
	for rowIdx, vals := range rows {
		item, errMsg := CheckSingleOvertimeMultiplierRowAndCache(ctx, vals, workingDayTypeCacheMap)
		if errMsg != enum.EmptyStr {
			item.ErrorMessage = errMsg
		}
		item.RowId = int64(rowIdx)
		uploadItems = append(uploadItems, item)
	}

	var workingDayTypeGroupMap = make(map[spx_driver_payable_enum.PublicHolidayType][]*dto.OvertimeMultiplierMappingUploadItem, 0)
	for _, item := range uploadItems {
		if item.ErrorMessage != enum.EmptyStr {
			continue
		}
		if _, ok := workingDayTypeGroupMap[item.WorkingDayType]; !ok {
			workingDayTypeGroupMap[item.WorkingDayType] = []*dto.OvertimeMultiplierMappingUploadItem{item}
		} else {
			workingDayTypeGroupMap[item.WorkingDayType] = append(workingDayTypeGroupMap[item.WorkingDayType], item)
		}
	}

	for workingDayType, items := range workingDayTypeGroupMap {
		sort.Slice(items, func(i, j int) bool {
			return items[i].OvertimeHours.LessThan(items[j].OvertimeHours)
		})

		if len(items) == 1 {
			continue
		}

		for i := 1; i < len(items); i++ {
			expectedValue := items[i-1].OvertimeHours.Add(decimal.NewFromInt(1))
			if items[i].OvertimeHours.Cmp(expectedValue) != 0 {
				items[i].ErrorMessage = fmt.Sprintf("'*OT hours' of '%s' is expected as '%s'", workingDayType.StringView(), expectedValue.String())
			}
		}
	}

	return uploadItems
}

func CheckSingleOvertimeMultiplierRowAndCache(ctx dbhelper.BmsContext, vals []string, workingDayTypeMap map[string]string) (*dto.OvertimeMultiplierMappingUploadItem, string) {
	item := &dto.OvertimeMultiplierMappingUploadItem{}
	for colIdx, headerName := range OvertimeMultiplierMappingExcelHeaders {
		if colIdx >= len(vals) {
			return item, fmt.Sprintf("'%s' is required", headerName)
		}
		if len(vals[colIdx]) == 0 {
			return item, fmt.Sprintf("'%s' is required", headerName)
		}
	}

	workdayType, overtimeHours, multiplier, errMsg := ParseRowData(vals)
	if errMsg != enum.EmptyStr {
		return item, errMsg
	}

	rowKey := fmt.Sprintf("%s_%s", workdayType, overtimeHours)
	_, exist := workingDayTypeMap[rowKey]
	if exist {
		return item, fmt.Sprintf("'%s - %s' already exists. Please confirm", workdayType.ExportView(), overtimeHours.String())
	}
	workingDayTypeMap[rowKey] = multiplier.String()

	item.WorkingDayType = workdayType
	item.OvertimeHours = overtimeHours
	item.Multiplier = multiplier.Round(TwoDecimalPlace)
	return item, ""
}

func ParseRowData(vals []string) (spx_driver_payable_enum.PublicHolidayType, decimal.Decimal, decimal.Decimal, string) {
	workdayType := spx_driver_payable_enum.TransferToHolidayType(vals[0]) // TransferToHolidayType
	errDecimal := decimal.Decimal{}
	if workdayType == 0 {
		allHoliday := spx_driver_payable_enum.AllPublicHolidayTypeList
		var holidayList []string
		for _, holiday := range allHoliday {
			holidayList = append(holidayList, holiday.ExportView())
		}
		return 0, errDecimal, errDecimal, fmt.Sprintf("'%s' is invalid.(should be one of '%s')", OvertimeMultiplierMappingExcelHeaders[0], strings.Join(holidayList, ","))
	}

	overtimeHours, err := decimal.NewFromString(vals[1])
	if err != nil {
		return 0, errDecimal, errDecimal, fmt.Sprintf("'%s' is invalid.(should be a integer number)", vals[1])
	}
	if overtimeHours.Exponent() != ZeroDecimalPlace {
		return 0, errDecimal, errDecimal, fmt.Sprintf("'%s' is invalid.(should be a integer number)", vals[1])
	}
	if overtimeHours.LessThan(decimal.NewFromInt(MinOtHours)) || overtimeHours.GreaterThan(decimal.NewFromInt(MaxOtHours)) {
		return 0, errDecimal, errDecimal, fmt.Sprintf("'%s' is invalid.(should be between %d and %d)", OvertimeMultiplierMappingExcelHeaders[1], MinOtHours, MaxOtHours)
	}

	multiplier, err := decimal.NewFromString(vals[2])
	if err != nil {
		return 0, errDecimal, errDecimal, fmt.Sprintf("'%s' is invalid.(should be a 2dp number)", OvertimeMultiplierMappingExcelHeaders[2])
	}

	if multiplier.LessThan(decimal.NewFromInt(MinMultiplier)) || multiplier.GreaterThan(decimal.NewFromInt(MaxMultiplier)) {
		return 0, errDecimal, errDecimal, fmt.Sprintf("'%s' is invalid.(should be between %d and %d)", OvertimeMultiplierMappingExcelHeaders[2], MinMultiplier, MaxMultiplier)
	}

	if multiplier.Exponent()+TwoDecimalPlace < 0 {
		return 0, errDecimal, errDecimal, fmt.Sprintf("'%s' is invalid.(should be a 2dp number)", OvertimeMultiplierMappingExcelHeaders[2])
	}
	return workdayType, overtimeHours, multiplier, ""
}

func (s *spxDriverPayableOvertimeMultiplierMappingService) processOvertimeMultiplierMappingData(ctx dbhelper.BmsContext, cityMonthSalaryMappingDetailTabs []*do.SpxDriverPayableOvertimeMultiplierMappingTab) error {
	// 事务
	if sscErr := ctx.Transaction(
		func() *retcode.SSCError {
			// 2 创建zone mapping tab
			_, err := s.overtimeMultiplierMappingDao.BatchCreateOvertimeMultiplierMapping(ctx, cityMonthSalaryMappingDetailTabs)
			if err != nil {
				return err
			}
			return nil
		},
	); sscErr != nil {
		return errors.New(sscErr.Msg)
	}
	return nil
}

func (s *spxDriverPayableOvertimeMultiplierMappingService) ListOvertimeMultiplierMapping(ctx dbhelper.BmsContext, req *dto.SearchDetailAttributeConfigReq) (*dto.SearchDetailAttributeConfigResp, *retcode.SSCError) {
	offset, size := constant.DefaultOffset, constant.DefaultSize
	if req.Offset != 0 {
		offset = req.Offset
	}
	if req.Size != 0 {
		size = req.Size
	}

	total, tabs, dbErr := s.overtimeMultiplierMappingDao.ListOvertimeMultiplierMappingWithOffset(ctx, req.ToSearchDetailQueryMap(), offset, size)
	if dbErr != nil {
		return nil, dbErr
	}

	overtimeMultiplierMappingViewList, sscErr := s.getMappingListDetail(ctx, tabs)
	if sscErr != nil {
		return nil, sscErr
	}

	return &dto.SearchDetailAttributeConfigResp{
		AttributeConfigType:           req.AttributeConfigType,
		OvertimeMultiplierMappingList: overtimeMultiplierMappingViewList,

		Total:  total,
		Offset: offset,
		Size:   size,
	}, nil
}

func (s *spxDriverPayableOvertimeMultiplierMappingService) getMappingListDetail(ctx dbhelper.BmsContext, tabs []*do.SpxDriverPayableOvertimeMultiplierMappingTab) ([]*dto.OvertimeMultiplierMappingViewItem, *retcode.SSCError) {
	var results []*dto.OvertimeMultiplierMappingViewItem
	for _, tab := range tabs {
		results = append(results, &dto.OvertimeMultiplierMappingViewItem{
			ID:             tab.ID,
			WorkingDayType: tab.WorkingDayType,
			OvertimeHours:  tab.OvertimeHours.String(),
			Multiplier:     tab.Multiplier.String(),
		})
	}
	return results, nil
}

func (s *spxDriverPayableOvertimeMultiplierMappingService) GetExportData(ctx dbhelper.BmsContext, req *dto.SearchDetailAttributeConfigReq) ([]string, [][]interface{}, *retcode.SSCError) {
	tabs, dbErr := s.overtimeMultiplierMappingDao.ListOvertimeMultiplierMapping(ctx, req.ToSearchDetailQueryMap())
	if dbErr != nil {
		return nil, nil, dbErr
	}

	if len(tabs) == 0 {
		return nil, nil, retcode.ParamError.AppendDetail("no zone mapping result")
	}

	mappingDetailList, sscErr := s.getMappingListDetail(ctx, tabs)
	if sscErr != nil {
		return nil, nil, sscErr.AppendDetail("get zone mapping detail list error")
	}

	var dataList [][]interface{}
	for _, item := range mappingDetailList {
		var row []interface{}
		for _, col := range item.ToStringList() {
			row = append(row, col)
		}
		dataList = append(dataList, row)
	}

	logger.CtxLogInfof(ctx, "export data list length=%d", len(dataList))
	return OvertimeMultiplierMappingExcelHeaders, dataList, nil
}
