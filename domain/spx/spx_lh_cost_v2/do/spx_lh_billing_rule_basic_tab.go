package do

import "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"

const SpxLhBillingRuleBasicTabName = "spx_lh_billing_rule_basic_tab"

type SpxLhBillingRuleBasicTab struct {
	Id                       uint64                        `json:"id" gorm:"id"`               // billing rule id
	CostType                 enum.CostType                 `json:"cost_type" gorm:"cost_type"` // cost type
	Region                   enum.Country                  `json:"region" gorm:"region"`
	BillingRuleStatus        enum.SpxLhBillingRuleDBStatus `json:"billing_rule_status" gorm:"billing_rule_status"`               // status
	EffectiveStartTime       uint32                        `json:"effective_start_time" gorm:"effective_start_time"`             // effective time
	MilkRunInfo              string                        `json:"milk_run_info" gorm:"milk_run_info"`                           // milk run info
	ChargeableWeightInfo     string                        `json:"chargeable_weight_info" gorm:"chargeable_weight_info"`         // chargeable_weight_info
	PeriodInfo               string                        `json:"period_info" gorm:"period_info"`                               // period_info
	Operator                 string                        `json:"operator" gorm:"operator"`                                     // operator
	Ctime                    uint32                        `json:"ctime" gorm:"ctime"`                                           // create time
	Mtime                    uint32                        `json:"mtime" gorm:"mtime"`                                           // modified time
	RouteCoverage            enum.RouteCoverage            `json:"route_coverage" gorm:"route_coverage"`                         // route_coverage
	ExcludeTripWithNoTos     bool                          `json:"exclude_trip_with_no_tos" gorm:"exclude_trip_with_no_tos"`     // 默认是false todo 建表语句
	RouteCoverageRuleId      uint64                        `json:"route_coverage_rule_id" gorm:"route_coverage_rule_id"`         // route_coverage
	BillingGenerationTrigger enum.BillingGenerationTrigger `json:"billing_generation_trigger" gorm:"billing_generation_trigger"` // BillingGenerationTrigger
}

func (s *SpxLhBillingRuleBasicTab) TableName() string {
	return SpxLhBillingRuleBasicTabName
}
