// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package spx_lh_cost

import (
	persistence2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/persistence"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/service/lh_fee"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/repository"
	service2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/service"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_role/persistence"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_role/service"
	service3 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/service"
	persistence4 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_lh_cost/persistence"
	repository3 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_lh_cost/repository"
	persistence3 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_lh_recalculate_task/persistence"
	repository2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_lh_recalculate_task/repository"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/third_party/idlib"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/third_party/location_server_lib"
)

// Injectors from wire.go:

func NewSpxLhCostService() *SpxLhCostService {
	idCreator := idlib.NewIdCreator()
	financeBillingDAO := repository.NewFinanceBillingDao(idCreator)
	feeTypeConfigDAO := persistence.NewFeeTypeConfigDAO()
	billingRoleDAO := persistence.NewBillingRoleDAO()
	billingRoleService := service.NewBillingRoleService(billingRoleDAO)
	feeTypeConfigService := service.NewFeeTypeConfigService(feeTypeConfigDAO, billingRoleService)
	lhCalculatorWrapper := lh_fee.NewLhCalculatorWrapper()
	calculateLhCostFee := lh_fee.NewCalculateLhCostFee(lhCalculatorWrapper)
	locationServerStoreStub := location_server_lib.NewLocationServerStub()
	orderService := service2.InitOrderService()
	billingVersionHBaseDAO := repository.NewBillingVersionHBaseDAO()
	bizOrderDAO := repository.NewBizOrderDao(idCreator)
	billingService := service2.NewBillingService(billingVersionHBaseDAO, financeBillingDAO, bizOrderDAO)
	spxLhCostCalculateDao := persistence2.NewSpxLhCostCalculateDao()
	billingPushV2DAO := repository.NewBillingPushV2Dao()
	billingPushService := service2.NewBillingPushService(billingPushV2DAO, financeBillingDAO)
	spxLhRecalculateTaskResultDao := persistence3.NewSpxLhRecalculateTaskResultDao()
	spxLhRecalculateTaskResultRepository := repository2.NewSpxLhRecalculateTaskResultRepository(spxLhRecalculateTaskResultDao)
	persistenceSpxLhCostCalculateDao := persistence4.NewSpxLhCostCalculateDao()
	spxLhCostCalculateRepository := repository3.NewSpxLhCostCalculateRepository(persistenceSpxLhCostCalculateDao)
	spxLhCostCalculatePeriodDao := persistence4.NewSpxLhCostCalculatePeriodDao()
	spxLhCostCalculatePeriodRepository := repository3.NewSpxLhCostCalculatePeriodRepository(spxLhCostCalculatePeriodDao)
	billingOrderService := service3.NewBillingOrderService()
	spxLhCostBackHaulMatchDao := persistence4.NewSpxLhCostBackHaulMatchDao()
	spxLhContractDao := persistence4.NewSpxLhContractDao()
	spxLhByDayRegisterDao := persistence4.NewSpxLhByDayRegisterDao()
	spxLhByMonthExceptionDao := persistence4.NewSpxLhByMonthExceptionDao()
	spxLhPeriodCalculateDao := persistence4.NewSpxLhPeriodCalculateDao()
	spxLhCostTripMatchDao := persistence4.NewSpxLhCostTripMatchDao()
	spxLhCostTripMatchRepo := repository3.NewSpxLhCostTripMatchRepo(spxLhCostTripMatchDao)
	spxLhCostTripOrderDao := persistence4.NewSpxLhCostTripOrderDao()
	spxLhCostTripOrderRepo := repository3.NewSpxLhCostTripOrderRepo(spxLhCostTripOrderDao)
	spxLhCostService := &SpxLhCostService{
		financeBillingDAO:              financeBillingDAO,
		feeTypeService:                 feeTypeConfigService,
		calculateService:               calculateLhCostFee,
		locationService:                locationServerStoreStub,
		orderService:                   orderService,
		billingService:                 billingService,
		spxLhCostCalculateDao:          spxLhCostCalculateDao,
		billingPushService:             billingPushService,
		spxLhRecalculateTaskResultRepo: spxLhRecalculateTaskResultRepository,
		newSpxLhCostRepo:               spxLhCostCalculateRepository,
		spxLhCostCalculatePeriodRepo:   spxLhCostCalculatePeriodRepository,
		newBillingOrderService:         billingOrderService,
		spxLhCostBachHaulMatchDao:      spxLhCostBackHaulMatchDao,
		contractDao:                    spxLhContractDao,
		registerDao:                    spxLhByDayRegisterDao,
		breakDownDao:                   spxLhByMonthExceptionDao,
		newSpxLhCostCalculateDao:       spxLhPeriodCalculateDao,
		ISpxLhCostTripMatchRepo:        spxLhCostTripMatchRepo,
		ISpxLhCostTripOrderRepo:        spxLhCostTripOrderRepo,
	}
	return spxLhCostService
}
