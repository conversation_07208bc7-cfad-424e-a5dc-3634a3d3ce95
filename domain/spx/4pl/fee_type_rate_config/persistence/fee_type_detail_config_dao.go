package persistence

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/4pl/fee_type_rate_config/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

type IFeeTypeDetailConfigDao interface {
	BatchCreateFeeTypeDetailConfig(ctx dbhelper.BmsContext, tabs []*do.FeeTypeDetailConfigTab) *retcode.SSCError
	DeleteFeeTypeDetailConfigByRateConfigId(ctx dbhelper.BmsContext, rateConfigId uint64, limit int64) *retcode.SSCError
	GetRateDetailConfigByConfigId(ctx dbhelper.BmsContext, rateConfigId uint64) ([]*do.FeeTypeDetailConfigTab, *retcode.SSCError)
}

type feeTypeDetailConfigDao struct {
}

func NewFeeTypeDetailConfigDao() *feeTypeDetailConfigDao {
	return &feeTypeDetailConfigDao{}
}

func (f *feeTypeDetailConfigDao) BatchCreateFeeTypeDetailConfig(ctx dbhelper.BmsContext, tabs []*do.FeeTypeDetailConfigTab) *retcode.SSCError {
	err := dbhelper.BatchCreateDataWithLimit(ctx, tabs)
	if err != nil {
		logger.CtxLogErrorf(ctx, "BatchCreateFeeTypeDetailConfig err: %+v", err)
		return err
	}
	return nil
}

func (f *feeTypeDetailConfigDao) DeleteFeeTypeDetailConfigByRateConfigId(ctx dbhelper.BmsContext, rateConfigId uint64, limit int64) *retcode.SSCError {
	err := dbhelper.BatchDeleteByIDWithLimit(ctx, &do.FeeTypeDetailConfigTab{}, map[string]interface{}{
		"rate_config_id": rateConfigId,
	}, limit)
	if err != nil {
		logger.CtxLogErrorf(ctx, "DeleteFeeTypeDetailConfigByRateConfigId failed, rateConfigId: %d, err: %+v", rateConfigId, err)
		return err
	}
	return nil
}

func (f *feeTypeDetailConfigDao) GetRateDetailConfigByConfigId(ctx dbhelper.BmsContext, rateConfigId uint64) ([]*do.FeeTypeDetailConfigTab, *retcode.SSCError) {
	var feeTypeRateConfigList []*do.FeeTypeDetailConfigTab
	searchData := map[string]interface{}{
		"rate_config_id": rateConfigId,
	}
	err := dbhelper.SearchAllData(ctx, &do.FeeTypeDetailConfigTab{}, &feeTypeRateConfigList, searchData)
	if err != nil {
		logger.CtxLogErrorf(ctx, "GetAllActiveFeeTypeRateConfig failed, feeType: %d, err: %+v", err)
		return nil, err
	}
	return feeTypeRateConfigList, nil
}
