package exporter

import (
	"fmt"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/excel/excel_exporter"
)

var (
	failedTripOrRegisterHeader = excel_exporter.ExcelTplRow{
		"Task ID",
		"Trip Number",
		"Contract ID",
		"Recalculate Failed Reason",
	}
)

type failedTripOrRegisterExporter struct {
	templateHeader excel_exporter.ExcelTplRow
	colWidth       float64
	baseSheetName  string
	sheetName      string
}

func (c *failedTripOrRegisterExporter) GetSheetName() string {
	return c.sheetName
}

func (c *failedTripOrRegisterExporter) SetSheetName(idx int) {
	c.sheetName = fmt.Sprintf("%s-%02d", c.baseSheetName, idx)
}

func (c *failedTripOrRegisterExporter) GenerateFileName(fileName string) string {
	return c.GetFilePath() + "Spx_DriverPayable_Recalculate_Reporter_" + fileName + ".xlsx"
}

func (c *failedTripOrRegisterExporter) GetTemplateHeader() excel_exporter.ExcelTplRow {
	return c.templateHeader
}

func (c *failedTripOrRegisterExporter) GetFilePath() string {
	return Dir
}

func (c *failedTripOrRegisterExporter) GetColWidth() float64 {
	return c.colWidth
}

func NewFailedTripOrRegisterExporter() excel_exporter.BasicExcelExporter {
	return excel_exporter.BasicExcelExporter{
		ExcelExporter: &failedTripOrRegisterExporter{
			templateHeader: failedTripOrRegisterHeader,
			baseSheetName:  "Failed Trips&Registrations",
		},
	}
}
