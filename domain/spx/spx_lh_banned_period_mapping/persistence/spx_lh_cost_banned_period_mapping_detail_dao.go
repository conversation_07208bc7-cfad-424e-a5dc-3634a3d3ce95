package persistence

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_lh_banned_period_mapping/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

type ISpxLhBannedPeriodMappingDetailDao interface {
	CreateSpxLhBannedPeriodMappingDetail(ctx dbhelper.BmsContext, tabs []*do.SpxLhBannedPeriodMappingDetailTab) *retcode.SSCError
	SelectSpxLhBannedPeriodMappingDetailByCondWithLimit(ctx dbhelper.BmsContext, params map[string]interface{}, offset, size int, order string) (int64, []*do.SpxLhBannedPeriodMappingDetailTab, *retcode.SSCError)
	SelectBannedPeriodsByMappingIdAndStationId(ctx dbhelper.BmsContext, mappingId int64, stationId int64) ([]*do.SpxLhBannedPeriodMappingDetailTab, *retcode.SSCError)
	BatchSelectDetailByMappingId(ctx dbhelper.BmsContext, mappingId int64) ([]*do.SpxLhBannedPeriodMappingDetailTab, *retcode.SSCError)
}

type SpxLhBannedPeriodMappingDetailDao struct {
}

func NewSpxLhBannedPeriodMappingDetailDao() *SpxLhBannedPeriodMappingDetailDao {
	return &SpxLhBannedPeriodMappingDetailDao{}
}

func (s *SpxLhBannedPeriodMappingDetailDao) CreateSpxLhBannedPeriodMappingDetail(ctx dbhelper.BmsContext, tabs []*do.SpxLhBannedPeriodMappingDetailTab) *retcode.SSCError {
	err := dbhelper.CreateData(ctx, tabs)
	if err != nil {
		logger.CtxLogErrorf(ctx, "create spx lh BannedPeriod mapping detail tab error: [%v], BannedPeriod mapping id :[%d]", err, tabs[0].ID)
		return err
	}
	return nil
}

func (s *SpxLhBannedPeriodMappingDetailDao) SelectSpxLhBannedPeriodMappingDetailByCondWithLimit(ctx dbhelper.BmsContext, params map[string]interface{}, offset, size int, order string) (int64, []*do.SpxLhBannedPeriodMappingDetailTab, *retcode.SSCError) {
	var tabList []*do.SpxLhBannedPeriodMappingDetailTab
	tab := &do.SpxLhBannedPeriodMappingDetailTab{}
	total, err := dbhelper.SearchAllDataWithOffsetAndOrder(ctx, tab.TableName(),
		&tabList, offset, size, params, order)
	if err != nil {
		logger.CtxLogErrorf(ctx, "list spx lh BannedPeriod mapping detail with limit order error: [%v], query: [%v]", err, params)
		return 0, nil, err
	}
	return total, tabList, nil
}

func (s *SpxLhBannedPeriodMappingDetailDao) SelectBannedPeriodsByMappingIdAndStationId(ctx dbhelper.BmsContext, mappingId int64, stationId int64) ([]*do.SpxLhBannedPeriodMappingDetailTab, *retcode.SSCError) {
	var tabList []*do.SpxLhBannedPeriodMappingDetailTab
	model := &do.SpxLhBannedPeriodMappingDetailTab{}
	searchParam := map[string]interface{}{"banned_period_mapping_id": mappingId, "station_id": stationId}
	err := dbhelper.SearchAllData(ctx, model, &tabList, searchParam)
	if err != nil {
		logger.CtxLogErrorf(ctx, "search spx lh BannedPeriod mapping detail error: [%v], query: [%v]", err, searchParam)
		return nil, err
	}
	return tabList, nil
}

func (s *SpxLhBannedPeriodMappingDetailDao) BatchSelectDetailByMappingId(ctx dbhelper.BmsContext, mappingId int64) ([]*do.SpxLhBannedPeriodMappingDetailTab, *retcode.SSCError) {
	var results, tmp []*do.SpxLhBannedPeriodMappingDetailTab
	fn := func(tx scormv2.SQLCommon, batch int) error {
		results = append(results, tmp...)
		return nil
	}
	d := ctx.ReadDB().WithContext(ctx.GetContext()).Where("banned_period_mapping_id = ?", mappingId).FindInBatches(&tmp, 50000, fn)
	if d.GetError() != nil {
		logger.CtxLogErrorf(ctx, "select banned period error, err is %v", d.GetError())
		return nil, retcode.DBReadError.CloneWithError(d.GetError())
	}
	return results, nil
}
