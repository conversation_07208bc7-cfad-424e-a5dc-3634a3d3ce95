package do

import "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"

const SpxLhRecalculateExportTabName = "spx_lh_recalculate_export_tab"

type SpxLhRecalculateExportTab struct {
	Id           int64             `gorm:"column:id" json:"id"`
	Operator     string            `gorm:"column:operator" json:"operator"`
	ExportTime   int64             `gorm:"column:export_time" json:"export_time"`
	ExportStatus enum.ExportStatus `gorm:"column:export_status" json:"export_status"`
	ExportUrl    string            `gorm:"column:export_url" json:"export_url"`
	TaskIdList   string            `gorm:"column:task_id_list" json:"task_id_list"`
	Ctime        int64             `gorm:"column:ctime" json:"ctime"`
	Mtime        int64             `gorm:"column:mtime" json:"mtime"`
}

func (s *SpxLhRecalculateExportTab) TableName() string {
	return SpxLhRecalculateExportTabName
}
