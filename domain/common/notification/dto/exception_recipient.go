package dto

//http://apidoc.i.ssc.shopeemobile.com/project/2817/interface/api/121673

type ExceptionRecipientSearchReq struct {
	Country string `json:"country" form:"country"`
}

type ExceptionRecipientSearchResp struct {
	//EmailTimes       []int    `json:"email_times"`       //时分选择器
	Recipients   []string `json:"recipients"`    //收件人
	EmailTitle   string   `json:"email_title"`   //邮件标题
	EmailContent string   `json:"email_content"` //邮件内容
	//AttachmentFields []int    `json:"attachment_fields"` //选中的复选框枚举集合
	Initialized bool `json:"initialized"` //是否初始化过
}

type ExceptionRecipientUpdateReq struct {
	//EmailTimes       []int    `json:"email_times"`       //时分选择器
	Recipients   []string `json:"recipients"`    //收件人
	EmailTitle   string   `json:"email_title"`   //邮件标题
	EmailContent string   `json:"email_content"` //邮件内容
	//AttachmentFields []int    `json:"attachment_fields"` //选中的复选框枚举集合
}

func (e *ExceptionRecipientUpdateReq) Validate() error {
	return nil
}

type UploadBillingOrderFileReq struct {
	Region string `json:"region"`
	S3Url  string `json:"s3_url"`
	TaskID string `json:"task_id"`
}

func (u *UploadBillingOrderFileReq) Validate() error {
	return nil
}
