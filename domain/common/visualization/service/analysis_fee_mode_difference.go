package service

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/common/visualization/dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	proto "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/protocol/protobuf/go"
)

func isFlatFeeMode(feeMode int32) bool {
	return enum.BasicShippingFeeStrategy(feeMode) == enum.BsfStrategyFlat
}

func isWeightRange(feeMode int32) bool {
	return enum.BasicShippingFeeStrategy(feeMode) == enum.BsfWeightRange
}

func isIncrement(feeMode int32) bool {
	return enum.BasicShippingFeeStrategy(feeMode) == enum.BsfIncrement
}

// 当 esf, asf 其一的 FeeMode 为 Flat，另一个不是 => true
func oneFlat(esf *proto.EsfAsfDiscrepancySourceData, asf *dto.AsfDiscrepancySourceData) bool {
	if esf.CalFeeMode == asf.CalFeeMode {
		return false
	}
	return isFlatFeeMode(esf.CalFeeMode) || isFlatFeeMode(asf.CalFeeMode)
}

// ASF-ESF Fee Mode 都是一口价
func twoFlat(esf *proto.EsfAsfDiscrepancySourceData, asf *dto.AsfDiscrepancySourceData) bool {
	return isFlatFeeMode(esf.CalFeeMode) && isFlatFeeMode(asf.CalFeeMode)
}

func oneWeightRange(esf *proto.EsfAsfDiscrepancySourceData, asf *dto.AsfDiscrepancySourceData) bool {
	if esf.CalFeeMode == asf.CalFeeMode {
		return false
	}
	return isWeightRange(esf.CalFeeMode) || isWeightRange(asf.CalFeeMode)
}

func oneIncrement(esf *proto.EsfAsfDiscrepancySourceData, asf *dto.AsfDiscrepancySourceData) bool {
	if esf.CalFeeMode == asf.CalFeeMode {
		return false
	}
	return isIncrement(esf.CalFeeMode) || isIncrement(asf.CalFeeMode)
}

// ASF-ESF 一个为 weight range, 另一个为 Increment
func weightRangeAndIncrement(esf *proto.EsfAsfDiscrepancySourceData, asf *dto.AsfDiscrepancySourceData) bool {
	return oneWeightRange(esf, asf) && oneIncrement(esf, asf)
}

func twoWeightRange(esf *proto.EsfAsfDiscrepancySourceData, asf *dto.AsfDiscrepancySourceData) bool {
	return isWeightRange(esf.CalFeeMode) && isWeightRange(asf.CalFeeMode)
}

func twoIncrement(esf *proto.EsfAsfDiscrepancySourceData, asf *dto.AsfDiscrepancySourceData) bool {
	return isIncrement(esf.CalFeeMode) && isIncrement(asf.CalFeeMode)
}
