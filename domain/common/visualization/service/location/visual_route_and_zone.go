package location

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_core/protocol"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/entity"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/common/visualization/dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
)

type visualRouteAndZoneResolver struct {
	routeResolver *visualRouteResolver
	zoneResolver  *visualZoneResolver
}

func (r *visualRouteAndZoneResolver) getCode(ctx dbhelper.BmsContext, rateTable entity.RateTable, location protocol.CalculateLocation) *dto.HitLocationInfo {
	routeCodeInfo := r.routeResolver.getCode(ctx, rateTable, location)
	result := &dto.HitLocationInfo{PickUpLocationIds: []int64{}, DeliverLocationIds: []int64{}}
	if nil != routeCodeInfo {
		result.PickUpLocationIds = routeCodeInfo.PickUpLocationIds
		result.DeliverLocationIds = routeCodeInfo.DeliverLocationIds
	}
	zoneCodeInfo := r.zoneResolver.getCode(ctx, rateTable, location)
	if nil != zoneCodeInfo {
		result.DeliverPostcode = zoneCodeInfo.DeliverPostcode
		if len(zoneCodeInfo.DeliverLocationIds) == 1 {
			result.DeliverLocationIds = append(result.DeliverLocationIds, zoneCodeInfo.DeliverLocationIds[0])
		}
	}
	return result
}
