package do

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
)

type ShippingFeeBatchRecordTab struct {
	Id                          uint64                    `gorm:"column:id" json:"id"`
	BatchId                     uint64                    `gorm:"column:batch_id" json:"batch_id"`
	Operator                    string                    `gorm:"column:operator" json:"operator"`
	BatchStatus                 enum.BatchStatus          `gorm:"column:batch_status" json:"batch_status"`
	ResultFilepath              string                    `gorm:"column:result_filepath" json:"result_filepath"`
	ErrorFilepath               string                    `gorm:"column:error_filepath" json:"error_filepath"`
	UpdateType                  enum.UpdateType           `gorm:"column:update_type" json:"update_type"`
	Ctime                       int64                     `gorm:"column:ctime" json:"ctime"`
	Mtime                       int64                     `gorm:"column:mtime" json:"mtime"`
	StateOfMergeErrorFile       enum.StateOfMergeFileType `gorm:"column:state_of_merge_error_file" json:"state_of_merge_error_file"`
	StateOfMergeResultFile      enum.StateOfMergeFileType `gorm:"column:state_of_merge_result_file" json:"state_of_merge_result_file"`
	DetailRecordCtime           int64                     `gorm:"column:detail_record_ctime" json:"detail_record_ctime"`
	DetailRecordTotal           uint32                    `gorm:"column:detail_record_total" json:"detail_record_total"`
	UploadFileUrl               string                    `gorm:"column:upload_file_url" json:"upload_file_url"`
	DraftReportUrl              string                    `gorm:"column:draft_report_url" json:"draft_report_url"`
	ApproveRecordId             uint32                    `gorm:"column:approve_record_id" json:"approve_record_id"`
	Detection                   uint32                    `gorm:"column:detection" json:"detection"`
	StateOfMergeDraftReportFile enum.StateOfMergeFileType `gorm:"column:state_of_merge_draft_report_file" json:"state_of_merge_draft_report_file"`
	RecordVersion               uint32                    `gorm:"column:record_version" json:"record_version"`
}

func (m *ShippingFeeBatchRecordTab) TableName() string {
	return ShippingFeeBatchRecordTableName()
}

func ShippingFeeBatchRecordTableName() string {
	return "shipping_fee_batch_record_tab"
}
