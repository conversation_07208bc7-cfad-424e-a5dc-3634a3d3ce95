package diagnosis_processor

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/sls/recon_discrepancy_diagnosis/aggregate/diagnosis_request"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/sls/recon_discrepancy_diagnosis/aggregate/diagnosis_response"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/sls/recon_discrepancy_diagnosis/aggregate/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

type ReconDiscrepancyDiagnosisProcessorList []IReconDiscrepancyDiagnosisProcessor

type IReconDiscrepancyDiagnosisProcessor interface {
	TriggerDiagnosis(ctx dbhelper.BmsContext, request diagnosis_request.IDiscrepancyDiagnosisRequest, response diagnosis_response.IDiscrepancyDiagnosisResponse) (bool, *retcode.SSCError)
	ParamPrepare(ctx dbhelper.BmsContext, request diagnosis_request.IDiscrepancyDiagnosisRequest, response diagnosis_response.IDiscrepancyDiagnosisResponse) *retcode.SSCError
	Process(ctx dbhelper.BmsContext, request diagnosis_request.IDiscrepancyDiagnosisRequest, response diagnosis_response.IDiscrepancyDiagnosisResponse) (enum.DiagnosisProcessControlFlag, *retcode.SSCError)
}
