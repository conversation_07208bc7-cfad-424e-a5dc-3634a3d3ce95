package protocol

type InvoiceLocationInfo struct {
	PickupLocationInfo  LocationInfo `json:"pickup_location_info"`
	DeliverLocationInfo LocationInfo `json:"deliver_location_info"`
}

type LocationInfo struct {
	StateLocationId    int64  `json:"state_location_id"`
	CityLocationId     int64  `json:"city_location_id"`
	DistrictLocationId int64  `json:"district_location_id"`
	Postcode           string `json:"postcode"`
}

type ShopeeLocationInfo struct {
	PickupLocation  CommonLocationInfo `json:"pickup_location"`
	DeliverLocation CommonLocationInfo `json:"deliver_location"`
}

type CommonLocationInfo struct {
	LocationIds []int64 `json:"location_ids"`
	Postcode    string  `json:"postcode"`
}
