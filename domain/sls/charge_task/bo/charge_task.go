package bo

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/application/task_api/msg_job/charge_task_msg_job/charge_task_msg_job_schema"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/service"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/common/task_center/dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/sls/charge_task/po"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/sls/charge_task/repository"
	persistence2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/sls/ilh_sf/forecast_task/persistence"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_lh_recalculate_task/persistence"
	repository2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_lh_recalculate_task/repository"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/third_party/bff_lib"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/third_party/charge_lib"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/third_party/reconlib"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/protocol/http_protocol/api_protocol"
)

type IChargeTask interface {
	GenerateSearchMainTaskParams(ctx dbhelper.BmsContext, req *api_protocol.GetChargeTaskReq) (map[string]interface{}, *retcode.SSCError)
	ConvertToTaskInfo(ctx dbhelper.BmsContext, tasks []*po.ChargeMainTaskTab) (*api_protocol.GetChargeTaskResp, *retcode.SSCError)
	CheckAndGenerateMainTask(ctx dbhelper.BmsContext, req *api_protocol.CreateChargeTaskReq) (*po.ChargeMainTaskTab, *retcode.SSCError)
	CreateBFF(ctx dbhelper.BmsContext, task *po.ChargeMainTaskTab) *retcode.SSCError
	ConvertToBFFTasks(ctx dbhelper.BmsContext, tasks []*po.ChargeMainTaskTab) (*dto.PollingTasksResp, *retcode.SSCError)

	SplitMainTask(ctx dbhelper.BmsContext, task *po.ChargeMainTaskTab, batchSize int) ([]*po.ChargeSubTaskTab, *retcode.SSCError)
	MergeSubTasks(ctx dbhelper.BmsContext, mainTask *po.ChargeMainTaskTab, subTasks []*po.ChargeSubTaskTab) *retcode.SSCError
	NotifyCaller(ctx dbhelper.BmsContext, task *po.ChargeMainTaskTab) *retcode.SSCError
	PostHandleFinalTask(ctx dbhelper.BmsContext, mainTask *po.ChargeMainTaskTab) *retcode.SSCError

	GetAsyncJobName() string
	GetAsyncJobData(mainTask *po.ChargeMainTaskTab, subTask *po.ChargeSubTaskTab) charge_task_msg_job_schema.ChargeTaskMsgJobSchema
}

func GetExactChargeTask(taskType enum.ChargeTaskType) (IChargeTask, *retcode.SSCError) {
	switch taskType {
	case enum.KerryReconTask:
		return &KerryReconTask{
			reconOrderService: service.NewReconOrderService(),
			chargeTaskRepo:    repository.NewChargeTaskRepo(),
			reconLib:          reconlib.NewReconLib(),
		}, nil
	case enum.ReCalculateKerryTask:
		return &RecalculateKerryTask{
			bffService:     bff_lib.NewBffService(),
			chargeTaskRepo: repository.NewChargeTaskRepo(),
		}, nil
	case enum.TplRecalculateTask:
		return &TplRecalculateTask{
			chargeTaskRepo:             repository.NewChargeTaskRepo(),
			ThreePlRecalculateTaskRepo: repository2.NewThreePlRecalculateTaskRepository(persistence.NewThreePlRecalculateTaskDao()),
		}, nil
	case enum.ILHForecastFeeTask:
		return &ILHForecastTask{
			forecastTaskDAO: persistence2.NewForecastTaskDAO(),
			chargeTaskRepo:  repository.NewChargeTaskRepo(),
			chargeClient:    charge_lib.NewChargeClient(),
		}, nil
	}
	return nil, retcode.GetChargeTaskError.CloneWithDetail("invalid task type")
}
