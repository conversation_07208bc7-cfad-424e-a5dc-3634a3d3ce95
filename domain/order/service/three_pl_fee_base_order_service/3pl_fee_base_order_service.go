package three_pl_fee_base_order_service

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_core/charged_weight"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/service"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/biz_order"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/dto"
	order_service "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/service"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

// ThreePLFeeBaseOrderService
// 3PL Fee 存储业务单的时候, 除了更新or创建biz order,
// 还会创建 referenceNumMapping, 处理 order flag, 推送 biz status
type ThreePLFeeBaseOrderService struct {
	*order_service.BaseOrderService
	CompensationService        order_service.ICompensationService
	ReferenceNumMappingService service.IReferenceNumMappingService
	BillingPushService         service.IBillingPushService
	ForceWeightCalculator      charged_weight.IForceWeightCalculator
}

func (t ThreePLFeeBaseOrderService) StoreBizOrder(ctx dbhelper.BmsContext, calculateDto dto.ICalculateShippingFeeDTO) (oldBizOrder, newBizOrder biz_order.IBizOrder, err *retcode.SSCError) {
	threePLFeeDTO := threePLFeeCalculateReqDTO(calculateDto)
	var onErr = func(err *retcode.SSCError) *retcode.SSCError {
		logger.CtxLogErrorf(ctx, "ThreePLFeeBaseOrderService StoreBizOrder err:%s FeeType:%s ServiceID:%s BizOrderNo:%s", err.Message(), calculateDto.GetFeeType().StringView(), calculateDto.GetServiceId(), threePLFeeDTO.OrderInfo.LmTrackingNo)
		return err
	}

	if !NeedStoreBizOrder(calculateDto) {
		return threePLFeeDTO.OldBizOrder, threePLFeeDTO.NewBizOrder, nil
	}
	t.AssignForceSellerListingWeight(ctx, calculateDto)

	// update or insert biz order
	oldBizOrder, newBizOrder, err = t.BaseOrderService.StoreBizOrder(ctx, calculateDto)
	if err != nil {
		return nil, nil, onErr(err)
	}

	if !calculateDto.NeedStore(ctx) {
		newBizOrder.SetScfsOrderID(threePLFeeDTO.OldBizOrder.GetScfsOrderID()) // 3PL 费用流量对比特殊逻辑
		return oldBizOrder, newBizOrder, nil
	}

	// create reference num mapping when not found.
	err = t.ReferenceNumMappingService.CreateReferenceNumMappings(ctx, service.CreateReferenceNumMappingReq{
		Tabs:      threePLFeeDTO.OrderInfo.GetReferenceNumMappings(newBizOrder.GetServiceID()),
		ServiceId: newBizOrder.GetServiceID(),
		FeeType:   enum.ThreePLASF,
		Region:    enum.Country(ctx.GetCountry()),
	})
	if err != nil {
		return nil, nil, onErr(err)
	}

	// order flag
	updateOrderFlag(ctx, oldBizOrder, newBizOrder, threePLFeeDTO)

	// handle exception order
	newBizOrder, err = t.handleExceptionBizOrder(ctx, threePLFeeDTO, newBizOrder)
	if err != nil {
		return nil, nil, onErr(err)
	}

	// push biz status
	t.pushOrderStatus(ctx, newBizOrder)
	return oldBizOrder, newBizOrder, nil
}

// NeedStoreBizOrder 按照 3PL Fee 的计算顺序, Cod Amount => 3PL ASF => 3PL ESF => RTS => Compensation
// / 由于 3PL Fee 公用一个 biz order, 所以如果前面的费用存储过了, 后面的费用就不需要再存储了
func NeedStoreBizOrder(calculateDTO dto.ICalculateShippingFeeDTO) bool {
	threePLFeeDTO := threePLFeeCalculateReqDTO(calculateDTO)
	return threePLFeeDTO.NewBizOrder == nil
}
