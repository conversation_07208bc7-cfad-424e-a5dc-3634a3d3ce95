package spx_biz_order_factory

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/do"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/biz_order"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/factory/biz_order_factory"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

var _ ISpxBizOrderFactory = &spxBizOrderFactory{}

type ISpxBizOrderFactory biz_order_factory.IBizOrderFactory

type spxBizOrderFactory struct {
	biz_order_factory.BaseBizOrderFactory
}

func (s spxBizOrderFactory) GenerateFromBizOrderTab(ctx dbhelper.BmsContext, tab *do.BizOrderTab) (biz_order.IBizOrder, *retcode.SSCError) {
	baseBizOrder, err := s.BaseBizOrderFactory.GenerateFromBizOrderTab(ctx, tab)
	if err != nil {
		return nil, err
	}
	var bizOrder = new(biz_order.BaseBizOrder)
	bizOrder.BizOrderEntity = baseBizOrder.GetBizOrderEntity()
	return bizOrder, nil
}
