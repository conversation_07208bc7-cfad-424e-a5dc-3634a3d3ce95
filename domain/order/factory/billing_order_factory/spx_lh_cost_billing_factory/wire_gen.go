// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package spx_lh_cost_billing_factory

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/factory/billing_order_factory"
)

// Injectors from wire.go:

func NewSpxLhCostBillingFactory() *spxLhCostBillingOrderFactory {
	baseBillingFactory := billing_order_factory.NewBaseBillingFactory()
	spx_lh_cost_billing_factorySpxLhCostBillingOrderFactory := &spxLhCostBillingOrderFactory{
		BaseBillingFactory: baseBillingFactory,
	}
	return spx_lh_cost_billing_factorySpxLhCostBillingOrderFactory
}

func NewSpxLhCostByMonthBillingFactory() *spxLhCostByMonthBillingOrderFactory {
	baseBillingFactory := billing_order_factory.NewBaseBillingFactory()
	spx_lh_cost_billing_factorySpxLhCostByMonthBillingOrderFactory := &spxLhCostByMonthBillingOrderFactory{
		BaseBillingFactory: baseBillingFactory,
	}
	return spx_lh_cost_billing_factorySpxLhCostByMonthBillingOrderFactory
}

func NewSpxLhCostByDayBillingFactory() *spxLhCostByDayBillingOrderFactory {
	baseBillingFactory := billing_order_factory.NewBaseBillingFactory()
	spx_lh_cost_billing_factorySpxLhCostByDayBillingOrderFactory := &spxLhCostByDayBillingOrderFactory{
		BaseBillingFactory: baseBillingFactory,
	}
	return spx_lh_cost_billing_factorySpxLhCostByDayBillingOrderFactory
}

// wire.go:

func NewSpxLhCostByTripBillingFactory() *spxLhCostByTripBillingOrderFactory {
	baseBillingFactory := billing_order_factory.NewBaseBillingFactory()
	spx_lh_cost_billing_factorySpxLhCostByTripBillingOrderFactory := &spxLhCostByTripBillingOrderFactory{
		BaseBillingFactory: baseBillingFactory,
	}
	return spx_lh_cost_billing_factorySpxLhCostByTripBillingOrderFactory
}
