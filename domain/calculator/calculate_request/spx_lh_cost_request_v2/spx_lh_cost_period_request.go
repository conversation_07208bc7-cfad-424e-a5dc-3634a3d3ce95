package spx_lh_cost_request_v2

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_request"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/service/lh_fee_v2"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_lh_cost_v2/entity"
)

type SpxLhCostPeriodCalculateRequest struct {
	*calculate_request.BaseCalculateRequest
	SpxLhRateBasic            entity.ISPXLhRateBasicTable
	SpxLhCostRouteRelatedList []entity.ISpxLhCostRelateCost
	SpxLhCostUnRouteRelated   *entity.SpxLhRouteUnRelatedCost
}

func (s *SpxLhCostPeriodCalculateRequest) SetSpxLhRateBasic(spxLhRateBasic entity.ISPXLhRateBasicTable) {
	if s == nil {
		return
	}
	s.SpxLhRateBasic = spxLhRateBasic
}

func (s *SpxLhCostPeriodCalculateRequest) SetSpxLhRouteRelatedList(routeRelatedList []entity.ISpxLhCostRelateCost) {
	if s == nil {
		return
	}
	s.SpxLhCostRouteRelatedList = routeRelatedList
}

func (s *SpxLhCostPeriodCalculateRequest) SetSpxLhRouteUnrelated(SpxLhCostUnRouteRelated *entity.SpxLhRouteUnRelatedCost) {
	if s == nil {
		return
	}
	s.SpxLhCostUnRouteRelated = SpxLhCostUnRouteRelated
}

func (s *SpxLhCostPeriodCalculateRequest) SetSpxLhPickupRouteRelatedParamList(pickupRouteRelatedParamList []lh_fee_v2.PickUpRouteRelatedParam) {
	return
}

func (s *SpxLhCostPeriodCalculateRequest) GetSpxLhRateBasic() entity.ISPXLhRateBasicTable {
	if s == nil {
		return nil
	}
	return s.SpxLhRateBasic
}

func (s *SpxLhCostPeriodCalculateRequest) GetSpxLhRouteRelatedList() []entity.ISpxLhCostRelateCost {
	if s == nil {
		return nil
	}
	return s.SpxLhCostRouteRelatedList
}

func (s *SpxLhCostPeriodCalculateRequest) GetSpxLhRouteUnrelated() *entity.SpxLhRouteUnRelatedCost {
	if s == nil {
		return nil
	}
	return s.SpxLhCostUnRouteRelated
}

func (s *SpxLhCostPeriodCalculateRequest) GetSpxLhPickupRouteRelatedParamList() []lh_fee_v2.PickUpRouteRelatedParam {
	return nil
}
