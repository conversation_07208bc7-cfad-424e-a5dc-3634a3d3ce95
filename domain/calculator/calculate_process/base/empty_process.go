package base

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_request"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_response"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

type EmptyProcess struct {
	ICalculateProcess
}

func (emptyProcess *EmptyProcess) Process(ctx dbhelper.BmsContext, request calculate_request.ICalculateRequest, response calculate_response.ICalculateResponse) (enum.RetryType, *retcode.SSCError) {
	return enum.NoNeedRetry, nil
}

func (emptyProcess *EmptyProcess) Report(ctx dbhelper.BmsContext, request calculate_request.ICalculateRequest, response calculate_response.ICalculateResponse, sscError *retcode.SSCError) *retcode.SSCError {
	return nil
}
