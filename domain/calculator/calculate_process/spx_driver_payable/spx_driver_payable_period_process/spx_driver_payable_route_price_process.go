package spx_driver_payable_period_process

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_process/base"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_request"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_response"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/entity/spx_driver_payable_entity"
	entity4 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/spx/spx_driver_payable_cost/entity"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum/spx_driver_payable_enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/store"
)

type SpxDriverPayableRoutePriceProcess struct {
	base.ICalculateProcess
}

func (s *SpxDriverPayableRoutePriceProcess) Process(ctx dbhelper.BmsContext, request calculate_request.ICalculateRequest, response calculate_response.ICalculateResponse) (enum.RetryType, *retcode.SSCError) {
	var (
		relatedCostList     []*spx_driver_payable_entity.SpxDriverPayableRouteRelatedCost
		unrelatedCostConfig *entity4.SpxDriverPayableRouteUnrelatedCost
		sscErr              *retcode.SSCError
	)
	matchSpxRoutePriceInfoList := request.GetMatchSpxRoutePriceInfoList()
	rateId := request.GetSpecialRateChannelId()
	for _, matchSpxRoutePriceInfo := range matchSpxRoutePriceInfoList {
		if matchSpxRoutePriceInfo.BillingStructureType == spx_driver_payable_enum.BillingStructureByFlat && unrelatedCostConfig == nil {
			unrelatedCostConfig, sscErr = store.GetInstance(ctx.GetStoreType()).GetSpxDriverPayableRouteUnrelatedConfig(ctx, uint64(rateId))

			if unrelatedCostConfig == nil {
				return 0, retcode.GetRoutePriceError.AppendDetail("can't found flat route config")
			}
			if sscErr != nil {
				return 0, sscErr
			}
		}
		if matchSpxRoutePriceInfo.BillingStructureType == spx_driver_payable_enum.BillingStructureByTiered {
			feeItem := spx_driver_payable_enum.GetFeeItemTypeByFeeComponentType(matchSpxRoutePriceInfo.FeeComponentType)
			feeConfigureType := spx_driver_payable_enum.TransferToConfigurationType(feeItem, matchSpxRoutePriceInfo.FeeComponentType, 0)
			relatedCostConfig, sscErr := store.GetInstance(ctx.GetStoreType()).GetSpxDriverPayableRouteRelatedConfig(ctx, uint64(rateId), feeConfigureType, &matchSpxRoutePriceInfo.RouteRelatedMatchInfo)
			if sscErr != nil {
				return 0, sscErr
			}

			if relatedCostConfig == nil {
				return 0, retcode.GetRoutePriceError.AppendDetail("can't found match config: %v", matchSpxRoutePriceInfo.FeeComponentType.StringView())
			}
			relatedCostList = append(relatedCostList, relatedCostConfig)
		}
	}
	request.SetSpxDriverPayableRouteUnRelatedInfo(unrelatedCostConfig)
	request.SetSpxDriverPayableRouteRelatedCostList(relatedCostList)
	return 0, nil
}

func (s *SpxDriverPayableRoutePriceProcess) Report(ctx dbhelper.BmsContext, request calculate_request.ICalculateRequest, response calculate_response.ICalculateResponse, sscError *retcode.SSCError) *retcode.SSCError {
	return nil
}
