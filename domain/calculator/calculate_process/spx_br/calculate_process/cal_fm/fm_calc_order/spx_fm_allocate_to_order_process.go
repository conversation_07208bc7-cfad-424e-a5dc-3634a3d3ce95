package fm_calc_order

import (
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_process/base"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_request"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_request/spx_br_request/fm/fm_req_order"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_response"
	fm2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_response/spx_br_response/fm/fm_resp_order"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

type SpxBrFmAllocateToOrderProcess struct {
	base.ICalculateProcess
}

func (b *SpxBrFmAllocateToOrderProcess) Process(ctx dbhelper.BmsContext, request calculate_request.ICalculateRequest, response calculate_response.ICalculateResponse) (enum.RetryType, *retcode.SSCError) {
	allocateResp := response.(fm2.ISpxBrAllocateFeeToOrderService)
	req := request.(*fm_req_order.SpxBrFmOrderCalculateRequest)
	//开始分摊
	allocateResp.AllocateFeeToOrder(req)
	return enum.NoNeedRetry, nil
}

func (b *SpxBrFmAllocateToOrderProcess) Report(ctx dbhelper.BmsContext, request calculate_request.ICalculateRequest, response calculate_response.ICalculateResponse, sscError *retcode.SSCError) *retcode.SSCError {
	return nil
}
