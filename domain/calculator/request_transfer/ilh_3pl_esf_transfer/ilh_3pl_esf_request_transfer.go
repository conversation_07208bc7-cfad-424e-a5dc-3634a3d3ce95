package ilh_3pl_esf_transfer

import (
	"fmt"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/config/namespace"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_info"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_request"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_request/ilh_3pl_esf_request"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_core/protocol"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/order/dto/fee_type_calculate_dto"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

type Ilh3PlEsfRequestTransfer struct {
}

func (t Ilh3PlEsfRequestTransfer) TransferCalculateRequest(ctx dbhelper.BmsContext, request dto.ICalculateShippingFeeDTO) (calculate_request.ICalculateRequest, *retcode.SSCError) {
	calculateDto := request.(*fee_type_calculate_dto.Ilh3PlEsfCalculateShippingDTO)
	sscErr := CheckMandatoryParams(calculateDto)
	if sscErr != nil {
		logger.CtxLogErrorf(ctx, "Ilh3PlEsfRequestTransfer CheckMandatoryParcelInfo error: %v", sscErr)
		return nil, sscErr
	}
	// 特有的入参
	return CalculateDto2Request(calculateDto), nil
}

func CalculateDto2Request(calculateDto *fee_type_calculate_dto.Ilh3PlEsfCalculateShippingDTO) *ilh_3pl_esf_request.Ilh3PLEsfCalculateRequest {

	orderInfo := calculateDto.GetParcelPerLegOrderInfo()
	// base req
	var baseReq = new(calculate_request.BaseCalculateRequest)
	baseReq.SetServiceId(calculateDto.GetServiceId())
	baseReq.SetFeeType(calculateDto.GetFeeType())
	baseReq.SetRegion(orderInfo.GetFinalDestRegion())
	//baseReq.SetCalculateBasicInfo(getCalculateBasicInfo(calculateDto))
	baseReq.SetCalculateMatchingInfo(getCalculateMatchingInfo(calculateDto))
	//baseReq.SetCalculateLocationInfo(getLocationInfo(calculateDto, calculateInfo.GetCalculateInfo().GetMerchantRegion()))
	//baseReq.SetCalculateOrderInfo(getCalculateOrderInfo(calculateDto))

	return &ilh_3pl_esf_request.Ilh3PLEsfCalculateRequest{
		BaseCalculateRequest: baseReq,
		ParcelOrderInfo:      orderInfo,
		FeeUnitsConvertInfo:  calculateDto.GetFeeUnitsConvertInfo(),
		PastMonthDateRange:   calculateDto.GetPastMonthDateRange(),
	}
}

func getCalculateMatchingInfo(calculateInfo *fee_type_calculate_dto.Ilh3PlEsfCalculateShippingDTO) *calculate_info.CalculateMatchingInfo {
	return &calculate_info.CalculateMatchingInfo{
		MatchingInfo: protocol.MatchingInfo{
			Timestamp:      uint32(calculateInfo.GetParcelPerLegOrderInfo().GetTwsOutboundDate()),
			MerchantRegion: calculateInfo.GetParcelPerLegOrderInfo().GetDestRegion().StringView(),
		},
	}
}

func AssertCalculateDto(request *fee_type_calculate_dto.Ilh3PlEsfCalculateShippingDTO) (needStore bool, sscErr *retcode.SSCError) {
	if request == nil {
		return false, retcode.CalculateParamsError.CloneWithDetail("request cannot be empty")
	}
	orderInfo := request.GetParcelPerLegOrderInfo()
	err := CheckMandatoryParcelInfo(request)
	// 如果必须字段校验失败，则需要判断两种情况
	if err != nil {
		if request.GetServiceId() != enum.EmptyStr && orderInfo.GetSloTn() != enum.EmptyStr && orderInfo.GetParcelLegNo() > 0 {
			//情况1：(service_id + sls_tn + leg_no)都不为空，但是其他必须字段有为空的；这种情况下需要落单，fail reason 记录：xxx 字段缺失
			return true, err
		} else {
			//情况2：(service_id + sls_tn + leg_no)有任何一个为空；这种情况下，没办法落单，直接给上游报错
			return false, err
		}
	}
	return true, nil
}

func CheckMandatoryParams(calculateShippingDTO *fee_type_calculate_dto.Ilh3PlEsfCalculateShippingDTO) *retcode.SSCError {
	err := CheckMandatoryParcelInfo(calculateShippingDTO)
	if err != nil {
		return err
	}
	// FeeUnitsConvertInfo 字段有效性校验
	err = checkFeeUnitsConvertInfo(calculateShippingDTO.GetFeeUnitsConvertInfo())
	if err != nil {
		return err
	}
	// PastMonthDateRange 字段有效性校验
	err = checkPastMonthDateRange(calculateShippingDTO.GetPastMonthDateRange())
	if err != nil {
		return err
	}
	return nil
}

func checkPastMonthDateRange(pastMonthDateRange *ilh_3pl_esf_request.PastMonthDateRange) *retcode.SSCError {
	if pastMonthDateRange == nil {
		return retcode.CalculateParamsError.CloneWithDetail("PastMonthDateRange can not be empty")
	}
	if pastMonthDateRange.GetStartTimestamp() <= 0 {
		return retcode.CalculateParamsError.CloneWithDetail("StartTimestamp of PastMonthDateRange must be greater than 0")
	}
	if pastMonthDateRange.GetEndTimestamp() <= 0 {
		return retcode.CalculateParamsError.CloneWithDetail("EndTimestamp of PastMonthDateRange must be greater than 0")
	}
	if pastMonthDateRange.GetStartTimestamp() > pastMonthDateRange.GetEndTimestamp() {
		return retcode.CalculateParamsError.CloneWithDetail("EndTimestamp of PastMonthDateRange must be greater than StartTimestamp")
	}

	return nil
}

func checkFeeUnitsConvertInfo(feeUnitsConvertInfo *ilh_3pl_esf_request.FeeUnitsConvertInfo) *retcode.SSCError {
	errFmt := "missing %s of forecast setting‘s Fee Component Rate Conversion Units"
	if feeUnitsConvertInfo == nil {
		return retcode.CalculateParamsError.CloneWithDetail("forecast setting‘s Fee Component Rate Conversion Units cannot be empty")
	}
	if feeUnitsConvertInfo.GetPackageFeeUnits().Undefined() {
		return retcode.CalculateParamsError.CloneWithError(fmt.Errorf(errFmt, "PackageFeeUnits"))
	}
	if feeUnitsConvertInfo.GetDocumentFeeUnits().Undefined() {
		return retcode.CalculateParamsError.CloneWithError(fmt.Errorf(errFmt, "DocumentFeeUnits"))
	}
	if feeUnitsConvertInfo.GetExtraDeliveryFeeUnits().Undefined() {
		return retcode.CalculateParamsError.CloneWithError(fmt.Errorf(errFmt, "ExtraDeliveryFeeUnits"))
	}
	if feeUnitsConvertInfo.GetOtherFeeUnits().Undefined() {
		return retcode.CalculateParamsError.CloneWithError(fmt.Errorf(errFmt, "OtherFeeUnits"))
	}
	if feeUnitsConvertInfo.GetBSFUnits().Undefined() {
		return retcode.CalculateParamsError.CloneWithError(fmt.Errorf(errFmt, "BSFUnits"))
	}
	if feeUnitsConvertInfo.GetCCServiceFeeUnits().Undefined() {
		return retcode.CalculateParamsError.CloneWithError(fmt.Errorf(errFmt, "CCServiceFeeUnits"))
	}
	return nil
}

func CheckMandatoryParcelInfo(calculateShippingDTO *fee_type_calculate_dto.Ilh3PlEsfCalculateShippingDTO) *retcode.SSCError {
	errFmt := "missing %s information of the parcel"
	orderInfo := calculateShippingDTO.GetParcelPerLegOrderInfo()

	var onErr = func(err error) *retcode.SSCError {
		return retcode.MissingMandatoryParcelInformation.CloneWithError(err)
	}
	if orderInfo == nil {
		return onErr(fmt.Errorf(errFmt, "ParcelOrderInfo"))
	}
	if orderInfo.GetSloTn() == enum.EmptyStr {
		return onErr(fmt.Errorf(errFmt, "SloTn"))
	}
	if calculateShippingDTO.GetServiceId() == enum.EmptyStr {
		return onErr(fmt.Errorf(errFmt, "ServiceId"))
	}
	if orderInfo.GetParcelLegNo() <= 0 {
		return onErr(fmt.Errorf(errFmt, "ParcelLegNo"))
	}
	if orderInfo.GetTwsOutboundTimestamp() <= 0 {
		return onErr(fmt.Errorf(errFmt, "TwsOutboundTimestamp"))
	}
	if orderInfo.GetTwsWeight() <= 0 {
		return onErr(fmt.Errorf(errFmt, "TwsWeight"))
	}
	if orderInfo.GetTransferMode().Undefined() {
		return onErr(fmt.Errorf(errFmt, "TransferMode"))
	}
	if orderInfo.GetLineId() == enum.EmptyStr {
		return onErr(fmt.Errorf(errFmt, "LineId"))
	}
	if orderInfo.GetDestRegion().Undefined() {
		if !namespace.GetSlsBillingConfWithoutCtx().IlhEsfSgCanSupportCalcRegion(orderInfo.GetDestRegion().String()) {
			return onErr(fmt.Errorf(errFmt, "DestinationRegion"))
		}
	}
	if orderInfo.GetDestCode() == enum.EmptyStr {
		return onErr(fmt.Errorf(errFmt, "DestinationCode"))
	}
	if orderInfo.GetFinalDestRegion().Undefined() {
		return onErr(fmt.Errorf(errFmt, "FinalDestRegion"))
	}
	if orderInfo.GetTpType() != enum.Pu && orderInfo.GetTpType() != enum.Te {
		return onErr(fmt.Errorf("the TpType of the order must be P or T"))
	}
	if orderInfo.GetVendorName() == enum.EmptyStr {
		return retcode.LineIdTransferToVendorNameFailed.CloneWithDetail(fmt.Sprintf("transfer line_id to vender name failed, lind_id is %s", orderInfo.GetLineId()))
	}

	if orderInfo.GetIlhCategory().Undefined() {
		return onErr(fmt.Errorf(errFmt, fmt.Sprintf("IlhCategory(line_sub_type:%s)", orderInfo.GetVendorSubType())))
	}

	return nil
}
