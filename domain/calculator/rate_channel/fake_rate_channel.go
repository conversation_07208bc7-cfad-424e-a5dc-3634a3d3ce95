package rate_channel

import (
	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/calculator/calculate_request"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/entity"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/factory"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
)

type fakeRateChannelResolver struct {
}

func (fakeRateChannelResolver fakeRateChannelResolver) GetRateTable(ctx dbhelper.BmsContext, request calculate_request.ICalculateRequest, _ ...enum.StoreType) (entity.RateTable, *retcode.SSCError) {
	rateTable, err := factory.CreateEmptyRateTable(ctx, request.GetFeeType(), request.GetBasicShippingFeeRule(), request.GetServiceId(), request.GetBillingRule().GetServiceType())
	if err != nil {
		logger.CtxLogErrorf(ctx, "generate fake rate table error, message: %s, billing rule: %+v, feeType: %v , serviceId : %s", err.Message(), request.GetBasicShippingFeeRule(), request.GetFeeType(), request.GetServiceId())
		return nil, err
	}
	return rateTable, nil
}
