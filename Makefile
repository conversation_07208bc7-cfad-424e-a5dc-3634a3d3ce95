run-admin:
	ENV=test CID=vn CHASSIS_CONF_DIR=`pwd`/conf go run ./cmd/admin_server

run-grpc:
	ENV=test CID=vn CHASSIS_CONF_DIR=`pwd`/conf go run ./cmd/grpc_server

run-task:
	ENV=test CID=vn CHASSIS_CONF_DIR=`pwd`/conf go run ./cmd/task_server

build:
	go mod tidy
	go build -o server ./cmd/grpc_server
	go build -o server ./cmd/admin_server
	go build -o server ./cmd/task_server
	go build -o server ./cmd/batask_server
	rm -rf server

wire:
	go get github.com/google/wire/cmd/wire
	@go generate -run="wire" ./...
