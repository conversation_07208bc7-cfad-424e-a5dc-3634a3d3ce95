package core_api_test

import (
	"encoding/json"

	billing_rule_do "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_rule/do"
	entity2 "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_rule/entity"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_rule/factory"
)

func AsfBillingRuleSample() *entity2.SellerAsfBillingRule {
	billingRuleStr := `
{
    "basic_info_config": "{\"category\":2,\"country\":\"PH\",\"rule_id\":13,\"fee_type\":2,\"service_id\":\"40066\",\"service_type\":2,\"service_category\":1,\"rule_status\":2,\"operator\":\"<EMAIL>\",\"ctime\":1655881833,\"mtime\":1660905959,\"support_escrow\":true,\"deduct_asf_from_escrow\":true,\"fulfillment_esf\":false}",
    "country": "PH",
    "ctime": 1655881833,
    "fee_type_id": 2,
    "mtime": 1660905959,
    "operator": "<EMAIL>",
    "product_category": 0,
    "rule_status": 2,
    "service_category": 1,
    "service_id": "40066",
    "service_type": 2,
    "shipping_fee_rule_config": "{\"shipping_fee\":{\"fallback_rule\":3,\"fallback_trigger\":0,\"basic_shipping_fee_rule\":0},\"weight\":{\"weight_rule\":1,\"multi_leg_mode\":0,\"weight_data_source\":2,\"volumetric_factor\":3500,\"weight_formula\":5,\"volumetric_weight_included\":true,\"volumetric_ratio_cap_relationship\":1,\"minimum_dimension_including\":false,\"ratio_cap_including\":false},\"dimension\":{\"priority_rule\":null,\"dimension_source_rule\":null},\"distance\":{\"priority_rule\":null},\"calculate_by_shop_group\":true}",
    "trigger_event_config": "{\"status_triggered\":false,\"status_list\":null,\"data_triggered\":false,\"data_list\":null,\"calculate_time_logic\":4}"
}
`

	obj := getBillingRule(billingRuleStr)
	if obj == nil {
		return nil
	}

	res, ok := obj.(*entity2.SellerAsfBillingRule)
	if !ok {
		return nil
	}

	return res
}

func TplBillingRule() *entity2.ThreePLASFBillingRule {
	str := `
  {
    "basic_info_config": "{\"category\":0,\"country\":\"PH\",\"rule_id\":164,\"fee_type\":4,\"service_id\":\"177\",\"service_type\":2,\"service_category\":4,\"rule_status\":2,\"operator\":\"<EMAIL>\",\"ctime\":1661928194,\"mtime\":1662437512,\"order_volume_tiers\":1,\"default_estimate_order_volume\":0,\"default_estimate_order_volume_list\":{},\"order_volume_counting_status\":[]}",
    "country": "PH",
    "ctime": 1661928194,
    "fee_type_id": 4,
    "mtime": 1662437512,
    "operator": "<EMAIL>",
    "product_category": 0,
    "rule_status": 2,
    "service_category": 4,
    "service_id": "177",
    "service_type": 2,
    "shipping_fee_rule_config": "{\"shipping_fee\":{\"shipping_fee_rule\":2,\"calculation_based_on\":1,\"basic_shipping_fee_rule\":0},\"location\":{\"priority_rule\":[]},\"distance\":{\"priority_rule\":[{\"priority\":1},{\"priority\":2}]},\"weight\":{\"priority_rule\":[{\"priority\":1,\"weight_source_a\":7,\"weight_logic\":1},{\"priority\":2},{\"priority\":3},{\"priority\":4}],\"weight_source_rule\":[{\"weight_source\":7,\"volumetric_weight_included\":false,\"volumetric_factor\":0,\"weight_formula\":4}]},\"dimension\":{\"priority_rule\":[{\"priority\":1},{\"priority\":2}],\"dimension_source_rule\":[{\"dimension_source\":0,\"dimension_formula\":0},{\"dimension_source\":0,\"dimension_formula\":0}]}}",
    "trigger_event_config": "{\"calculate_trigger_event\":{\"status_config\":{\"triggered\":true,\"status_list\":[\"F666\",\"F980\",\"F999\",\"F960\",\"F969\",\"F196\",\"F965\",\"F510\",\"F440\",\"F422\",\"F420\",\"F540\",\"F550\",\"F500\",\"F580\",\"F450\",\"F599\"],\"trigger_time_logic\":2,\"fallback_time_logic\":1},\"data_source_config\":{\"triggered\":false},\"cod_order_validation_config\":{\"status_validation\":false,\"matched_status\":[]}}}"
  }
	`
	obj := getBillingRule(str)
	if obj == nil {
		return nil
	}

	res, ok := obj.(*entity2.ThreePLASFBillingRule)
	if !ok {
		return nil
	}

	return res
}

func getBillingRule(str string) entity2.BillingRuleEntity {
	tab := billing_rule_do.BillingRuleV2Tab{}
	err := json.Unmarshal([]byte(str), &tab)
	if err != nil {
		return nil
	}

	obj, err := factory.CreateBillingRuleFromDbObj(&tab)
	if err != nil {
		return nil
	}

	return obj
}
