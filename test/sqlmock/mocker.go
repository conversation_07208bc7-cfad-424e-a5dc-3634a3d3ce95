package sqlmock

import (
	"database/sql"
	scorm "git.garena.com/shopee/bg-logistics/go/scormv2"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-core-service/internal/common/utils/dbhelper"
	mock "github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"testing"
)

const (
	insertCommon = "^INSERT INTO .+"
	deleteCommon = "^DELETE .+"
	updateCommon = "^UPDATE .+ SET"
	selectCommon = "^(SELECT|select)"
)

type Mocker struct {
	mocker mock.Sqlmock
	db     *sql.DB
}

func NewMock() *Mocker {
	db, mocker, _ := mock.New()
	newMocker := &Mocker{
		mocker: mocker,
		db:     db,
	}
	return newMocker
}

func (m *Mocker) GetRealSqlMock() mock.Sqlmock {
	return m.mocker
}

// just use for comment
func (m *Mocker) Comment(s string) *Mocker {
	return m
}

func (m *Mocker) Finish(t *testing.T) {
	dbhelper.SetLCOSConnection(nil, nil)
	if err := m.mocker.ExpectationsWereMet(); err != nil {
		t.Errorf("sqlmock:there were unfulfilled expectations: %s", err)
	}
}

func (m *Mocker) ExpectInsert() *ExecMocker {
	exec := m.mocker.ExpectExec(insertCommon)
	return &ExecMocker{
		exec: exec,
	}
}

func (m *Mocker) ExpectDelete() *ExecMocker {
	exec := m.mocker.ExpectExec(deleteCommon)
	return &ExecMocker{
		exec: exec,
	}
}

func (m *Mocker) ExpectUpdate() *ExecMocker {
	exec := m.mocker.ExpectExec(updateCommon)
	return &ExecMocker{
		exec: exec,
	}
}

func (m *Mocker) ExpectSelect() *QueryMocker {
	query := m.mocker.ExpectQuery(selectCommon)
	return &QueryMocker{
		query: query,
	}
}

func (m *Mocker) ExpectCommitTranscation(f func(*Mocker)) *Mocker {
	m.mocker.ExpectBegin()
	f(m)
	m.mocker.ExpectCommit()
	return m
}

func (m *Mocker) ExpectrRollbackTransaction(f func(*Mocker)) *Mocker {
	m.mocker.ExpectBegin()
	f(m)
	m.mocker.ExpectRollback()
	return m
}

func (m *Mocker) ExpectInsertInTransaction() *ExecMocker {
	m.mocker.ExpectBegin()
	exec := m.mocker.ExpectExec(insertCommon)
	m.mocker.ExpectCommit()
	return &ExecMocker{
		exec: exec,
	}
}

func (m *Mocker) ExpectDeleteInTransaction() *ExecMocker {
	m.mocker.ExpectBegin()
	exec := m.mocker.ExpectExec(deleteCommon)
	m.mocker.ExpectCommit()
	return &ExecMocker{
		exec: exec,
	}
}

func (m *Mocker) ExpectUpdateInTransaction() *ExecMocker {
	m.mocker.ExpectBegin()
	exec := m.mocker.ExpectExec(updateCommon)
	m.mocker.ExpectCommit()
	return &ExecMocker{
		exec: exec,
	}
}

func (m *Mocker) SetGormDB() {
	m.setGormDB(false)
}

func (m *Mocker) SetGormDBWithPrintSql() {
	m.setGormDB(true)
}

func (m *Mocker) setGormDB(logMode bool) {
	//todo: 很久未使用的代码，使用时记得测试一下
	db, _ := scorm.Open(&mysql.Dialector{
		Config: &mysql.Config{
			SkipInitializeWithVersion: true,
			Conn:                      m.db,
		},
	}, &scorm.Config{})
	dbhelper.SetLCOSConnection(db, db)
}
