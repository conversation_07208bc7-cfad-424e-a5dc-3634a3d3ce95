package excel_parser

import (
	"os"
	"strings"
	"testing"

	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_fee/service/excel_handler/excel_parser"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/third_party/lpslib"

	cf "git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/config"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/enum"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/utils/bms_testing"
)

var (
	ctx         dbhelper.BmsContext
	region      string
	basicParser excel_parser.IExcelParser
)

func init() {
	basicParser = excel_parser.GenerateZoneCodeParser()
	region = enum.VN.StringView()
	ctx = bms_testing.InitCtxWithCountryAndOperatorWithoutDB(region, "jiachang")
	_ = os.Setenv("CID", region)
	_ = os.Setenv("ENV", strings.ToLower("test"))
	cf.InitConfig(ctx)
	//_ = os.Setenv("CHASSIS_CONF_DIR", "/Users/<USER>/Documents/go/src/logistics-charge-platform/conf/coreapi")
	//_ = os.Setenv("SSC_ENV", "test")
	//c, _ := cf.InitConfig(ctx)
	//gin.SetMode(c.GinMode)
	//server := &ginServer.GinMgr{Engine: gin.New()}
	//server.Use(
	//	middleware.Recovery(),
	//)
	//chassis.RegisterSchema("rest", server)
	//err := chassis.Init()
	//if err != nil {
	//	fmt.Printf("chassis.Init error: %v", err)
	//}
	//fmt.Println("chassis.Init done!")
}

func TestParseFromLocalFilePath(t *testing.T) {
	localFilePath := "../../../../../zone_template_(9)_(1).xlsx"
	basicParser.ParseFromLocalFilePath(ctx, localFilePath)
}

func TestZoneCodeParser(t *testing.T) {
	localFilePath := "../../../../../AHM-zone.xlsx"

	parser := excel_parser.GenerateZoneCodeParser()
	dbObjs, err := parser.ParseFromLocalFilePath(ctx, localFilePath)
	if err != nil {
		t.Fatalf("Fail, dbObjs:%v, err:%v", dbObjs, err)
	}
	t.Logf("Pass, dbObjs:%v, err:%v", dbObjs, err)
}

func TestPriceListParser(t *testing.T) {
	localFilePath := "../../../../../price-list-test.xlsx"
	originCodeSet := map[string]struct{}{
		"CBN20200": {},
		"LOP20900": {},
		"MJK10100": {},
		// testcase: Origin code 在price-list中配置不全情况
		"AJK10100": {},
	}

	destinationCodeSet := map[string]struct{}{
		"UPG23200": {},
		"BDO20117": {},
		// testcase: Destination code 在price-list中配置不全情况
		//"ADO20117": {},
		//"ZDO20117": {},
	}

	parser := excel_parser.GeneratePriceListParser(originCodeSet, destinationCodeSet, enum.ESF)
	dbObjs, err := parser.ParseFromLocalFilePath(ctx, localFilePath)
	if err != nil {
		t.Fatalf("Fail, dbObjs:%v, err:%v", dbObjs, err)
	}
	t.Logf("Pass, dbObjs:%v, err:%v", dbObjs, err)
}

func TestPriceListEnhancedParser(t *testing.T) {
	localFilePath := "../../../../../new-80020-4284-7912-pricelist.xlsx"
	originCodeSet := map[string]struct{}{
		"CBN20200": {},
		"LOP20900": {},
		"MJK10100": {},
		// testcase: Origin code 在price-list中配置不全情况
		"AJK10100": {},
	}

	destinationCodeSet := map[string]struct{}{
		"UPG23200": {},
		"BDO20117": {},
		// testcase: Destination code 在price-list中配置不全情况
		//"ADO20117": {},
		//"ZDO20117": {},
	}

	parser := excel_parser.GeneratePriceListEnhancedParser(originCodeSet, destinationCodeSet, enum.ESF)
	dbObjs, err := parser.ParseFromLocalFilePath(ctx, localFilePath)
	if err != nil {
		t.Fatalf("Fail, dbObjs:%v, err:%v", dbObjs, err)
	}
	t.Logf("Pass, dbObjs:%v, err:%v", dbObjs, err)
}

func TestPriceListWeightParser(t *testing.T) {
	localFilePath := "../../../../../price-list-weight-test.xlsx"

	minWeight := float64(0)
	productOrderLimit := &lpslib.ProductOrderLimit{
		OrderMinWeight:          &minWeight,
		OrderMaxWeightUnlimited: true,
	}
	parser := excel_parser.GeneratePriceListWeightParser(enum.ESF, productOrderLimit)
	dbObjs, err := parser.ParseFromLocalFilePath(ctx, localFilePath)
	if err != nil {
		t.Fatalf("Fail, dbObjs:%v, err:%v", dbObjs, err)
	}
	t.Logf("Pass, dbObjs:%v, err:%v", dbObjs, err)
}

func TestPriceListWeightEnhancedParser(t *testing.T) {
	localFilePath := "../../../../../price-list-weight-enhanced-test.xlsx"

	odCodeSet := map[string]struct{}{
		"1": {},
		"3": {},
		// testcase: weight bound rules 中没配置的odcode, 预期：报错
		//"4": {},
	}

	minWeight := float64(1)
	maxWeight := float64(50301) // testcase: 50301 大于End weight的值
	productOrderLimit := &lpslib.ProductOrderLimit{
		OrderMinWeight:          &minWeight,
		OrderMaxWeight:          &maxWeight,
		OrderMaxWeightUnlimited: false,
	}
	parser := excel_parser.GeneratePriceListWeightEnhancedParser(odCodeSet, enum.ESF, productOrderLimit)
	dbObjs, err := parser.ParseFromLocalFilePath(ctx, localFilePath)
	if err != nil {
		t.Fatalf("Fail, dbObjs:%v, err:%v", dbObjs, err)
	}
	t.Logf("Pass, dbObjs:%v, err:%v", dbObjs, err)
}

func TestPriceListDistanceParser(t *testing.T) {
	localFilePath := "../../../../../price-list-distance-test.xlsx"

	minDistance := float64(1)
	maxDistance := float64(100003) // testcase: 100003 大于End distance的值
	productOrderLimit := &lpslib.ProductOrderLimit{
		MinDistance:          &minDistance,
		MaxDistance:          &maxDistance,
		MaxDistanceUnlimited: false,
	}
	parser := excel_parser.GeneratePriceListDistanceParser(enum.SellerASF, productOrderLimit)
	dbObjs, err := parser.ParseFromLocalFilePath(ctx, localFilePath)
	if err != nil {
		t.Fatalf("Fail, dbObjs:%+v, err:%v", dbObjs, err)
	}
	t.Logf("Pass, dbObjs:%+v, err:%v", dbObjs, err)
}

func TestParcelVolumeFeeParser(t *testing.T) {
	localFilePath := "../../../../../parcelVolume-test.xlsx"

	minVolume := float64(1)
	maxVolume := float64(8000001) // testcase: 8000001 大于End volume 的值
	productOrderLimit := &lpslib.ProductOrderLimit{
		MinVolume:               &minVolume,
		MaxVolume:               &maxVolume,
		OrderMaxVolumeUnlimited: false,
	}
	parser := excel_parser.GenerateParcelVolumeFeeParser(enum.SellerASF, productOrderLimit)
	dbObjs, err := parser.ParseFromLocalFilePath(ctx, localFilePath)
	if err != nil {
		t.Fatalf("Fail, dbObjs:%+v, err:%v", dbObjs, err)
	}
	t.Logf("Pass, dbObjs:%+v, err:%v", dbObjs, err)
}

func TestBasicShippingFeeDistanceParser(t *testing.T) {
	localFilePath := "../../../../../BasicShippingFeeDistance-test.xlsx"

	routeCodeSet := map[string]struct{}{}
	zoneCodeSet := map[string]struct{}{
		"3A": {},
		"4A": {},
		"5A": {},
		"6A": {},
		"7A": {},
		"8A": {},
		"9A": {},
	}

	minDistance := float64(1)
	maxDistance := float64(40000) // testcase: 40001 大于End distance 的值
	productOrderLimit := &lpslib.ProductOrderLimit{
		MinDistance:          &minDistance,
		MaxDistance:          &maxDistance,
		MaxDistanceUnlimited: false,
	}
	parser := excel_parser.GenerateBasicShippingFeeDistanceParser(enum.Zone, routeCodeSet, zoneCodeSet, enum.SellerASF, productOrderLimit)
	dbObjs, err := parser.ParseFromLocalFilePath(ctx, localFilePath)
	if err != nil {
		t.Fatalf("Fail, dbObjs:%+v, err:%v", dbObjs, err)
	}
	t.Logf("Pass, dbObjs:%+v, err:%v", dbObjs, err)
}

func TestBasicShippingFeeParser(t *testing.T) {
	var bsfLevel enum.BsfLevel

	// ========== testcase1: Route 模式 ==========
	//bsfLevel = enum.Route
	//localFilePath := "../../../../../BasicShippingFee-Route-test.xlsx"
	//routeCodeSet := map[string]struct{}{
	//	"CA1AA5BAN405": {},
	//	"CA1AA5BAN425": {},
	//	"CA1AA5BAN470": {},
	//	//"CA1AA5BAN471": {}, //测试不在excel中的route code
	//}
	//zoneCodeSet := map[string]struct{}{}
	//minWeight := int64(1)
	//maxWeight := int64(50300) // testcase: 50301 大于End weight的值

	// ========== testcase2: Zone 模式 ==========
	bsfLevel = enum.Zone
	localFilePath := "../../../../../BasicShippingFee-Zone-test.xlsx"
	routeCodeSet := map[string]struct{}{}
	zoneCodeSet := map[string]struct{}{
		"A1": {},
		"A2": {},
		"B":  {},
		"C":  {},
	}
	minWeight := float64(6)
	maxWeight := float64(2000000.000) // testcase: 1000001 大于sEnd weight的值

	// ========== testcase3：Route&Zone 模式 ==========
	//bsfLevel = enum.RouteAndZone
	//localFilePath := "../../../../../BasicShippingFee-Route&Zone-test.xlsx"
	//routeCodeSet := map[string]struct{}{
	//	"R1": {},
	//	"R2": {},
	//}
	//zoneCodeSet := map[string]struct{}{
	//	"Rural": {},
	//	"Urban": {},
	//}
	//minWeight := float64(1)
	//maxWeight := float64(100002) // testcase: 100002 大于End weight的值

	productOrderLimit := &lpslib.ProductOrderLimit{
		OrderMinWeight:          &minWeight,
		OrderMaxWeight:          &maxWeight,
		OrderMaxWeightUnlimited: false,
	}
	parser := excel_parser.GenerateBasicShippingFeeParser(bsfLevel, routeCodeSet, zoneCodeSet, enum.ESF, productOrderLimit)
	dbObjs, err := parser.ParseFromLocalFilePath(ctx, localFilePath)
	if err != nil {
		t.Fatalf("Fail, dbObjs:%+v, err:%v", dbObjs, err)
	}
	t.Logf("Pass, dbObjs:%+v, err:%v", dbObjs, err)
}

func TestBasicShippingFeeDistanceDsAndWeightParser(t *testing.T) {
	// ========== 只能选择 Zone 模式 ==========
	localFilePath := "../../../../../BasicShippingFeeDistanceDsAndWeight-Zone-test.xlsx"
	routeCodeSet := map[string]struct{}{}
	zoneCodeSet := map[string]struct{}{
		"1A":  {},
		"8A":  {},
		"9A":  {},
		"10A": {},
		"11A": {},
		"12A": {},
		"13A": {},
		"14A": {},
		"15A": {},
		"16A": {},
		"17A": {},
		"18A": {},
	}
	minWeight := float64(0)
	maxWeight := float64(10301) // testcase: 10301 大于End weight 的值

	minDistance := float64(0)
	maxDistance := float64(100001) // testcase: 100001 大于End distance 的值

	productOrderLimit := &lpslib.ProductOrderLimit{
		OrderMinWeight:          &minWeight,
		OrderMaxWeight:          &maxWeight,
		OrderMaxWeightUnlimited: false,
		MinDistance:             &minDistance,
		MaxDistance:             &maxDistance,
		MaxDistanceUnlimited:    false,
	}
	parser := excel_parser.GenerateBasicShippingFeeDistanceDsAndWeightParser(enum.Zone, routeCodeSet, zoneCodeSet, enum.ESF, productOrderLimit)
	dbObjs, err := parser.ParseFromLocalFilePath(ctx, localFilePath)
	if err != nil {
		t.Fatalf("Fail, dbObjs:%+v, err:%v", dbObjs, err)
	}
	t.Logf("Pass, dbObjs:%+v, err:%v", dbObjs, err)
}
