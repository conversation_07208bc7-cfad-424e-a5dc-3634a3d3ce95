package service

import (
	"context"
	"fmt"
	"git.garena.com/shopee/bg-logistics/go/chassis"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/domain/charge/billing_order/service"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/config"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/constant"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/dbhelper"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/infrastructure/retcode"
	"git.garena.com/shopee/bg-logistics/logistics/sls-finance/logistics-charge-platform/initialize/base"
	"github.com/stretchr/testify/assert"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
)

var once sync.Once

func initEnv() {
	// 当前框架: archaius未初始化，需要确认成因
	//archaius.Init(archaius.WithMemorySource())
	curPath, err := os.Getwd()
	if err != nil {
		fmt.Printf("os.Getwd Fail, err=%+v", err)
		return
	}
	ctx := dbhelper.NewCommonCtx(context.Background())
	projectName := "logistics-charge-platform"
	homeDir := curPath[:strings.Index(curPath, projectName)+len(projectName)]
	confDir := filepath.Join(homeDir, "./conf/coreapi")
	fmt.Printf("confDir=%q\n", confDir)
	_ = os.Setenv("CHASSIS_HOME", homeDir)
	_ = os.Setenv("CHASSIS_CONF_DIR", confDir)
	_ = os.Setenv("SSC_ENV", strings.ToLower(config.GetEnv(ctx)))
	_ = os.Setenv("CID", "VN")
	_ = os.Setenv("ENV", "test")
	err = chassis.Init(
		chassis.WithChassisConfigPrefix("coreapi"),
		chassis.WithDefaultProviderHandlerChain(),
	)

	conf, err := config.InitConfig(ctx)
	initFunc := []func(ctx context.Context, conf *config.Config) error{
		base.InitLog,
		base.InitSysConfig,
		base.InitV2AllConfig,
		//base.InitAllDb,
		base.InitChargeDb,
		base.InitRedis,
	}

	for index, fc := range initFunc {
		err := fc(ctx, conf)
		if err != nil {
			log.Printf("index=%d, err=%+v\n", index, err)
		}
	}
}

func TestRefreshOrderVolume(t *testing.T) {
	once.Do(func() {
		initEnv()
	})
	bmsCtx := dbhelper.NewCommonCtx(context.WithValue(context.Background(), constant.Country, config.GetCID()))
	adoVolumeSvr := service.NewAdoVolumeStatisticalService(bmsCtx)
	rtn := adoVolumeSvr.RefreshAdoVolumeStatisticInfo(bmsCtx)
	assert.Equal(t, retcode.Success.Retcode(), rtn.Retcode())
	return
}
