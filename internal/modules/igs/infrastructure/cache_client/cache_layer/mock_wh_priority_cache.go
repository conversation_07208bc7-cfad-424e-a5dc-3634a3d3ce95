package cache_layer

import (
	"context"

	group_dm "git.garena.com/shopee/marketplace-logistics/fulfilment_planning/internal/modules/igs/application"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_planning/internal/modules/igs/infrastructure/service/oms"
)

type MockWhPriorityCache struct {
	MockBatchGetWhPriority func(
		ctx context.Context,
		country string,
		address *group_dm.BuyerAddress,
		whIds []string,
	) ([]*oms.WhsPriorityEntity, error)
}

func (m MockWhPriorityCache) BatchGetWhPriority(
	ctx context.Context,
	country string,
	address *group_dm.BuyerAddress,
	shopId uint64,
	whIds []string,
) ([]*oms.WhsPriorityEntity, error) {
	if m.MockBatchGetWhPriority == nil {
		return nil, nil
	}
	return m.MockBatchGetWhPriority(ctx, country, address, whIds)
}

func (m MockWhPriorityCache) BatchGetAllWarehousePriority(
	ctx context.Context,
	country string,
	address *group_dm.BuyerAddress,
	shopId uint64,
) ([]*oms.WhsPriorityEntity, error) {
	return nil, nil
}
