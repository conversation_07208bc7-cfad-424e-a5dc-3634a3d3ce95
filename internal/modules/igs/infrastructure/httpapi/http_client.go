package httpapi

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_planning/internal/config"
	"io/ioutil"
	"net/http"
	"net/url"
	"time"

	"git.garena.com/shopee/common/ulog"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_planning/internal/logger"
	httpspi "git.garena.com/shopee/mts/go-application-server/spi/http"
)

type BaseHttpClientImpl struct {
	omsHttpRequestOptions HttpRequestOptions
	pmsHttpRequestOptions HttpRequestOptions

	omsHttpClient  httpspi.Client  `inject:"oms_http_client"`
	pmsHttpClient  httpspi.Client  `inject:"pms_http_client"`
	configAccessor config.Accessor `inject:""`
}

func (impl *BaseHttpClientImpl) Init() error {
	err := impl.initOmsHttpRequestOptions()
	if err != nil {
		return err
	}

	err = impl.initPmsHttpRequestOptions()
	if err != nil {
		return err
	}

	return nil
}

func (impl *BaseHttpClientImpl) initOmsHttpRequestOptions() error {
	omsConfig := impl.configAccessor.GetIGSDynamicConfig().GetOmsConfig()
	region := config.GetRegion()
	host := omsConfig.Host
	if host == "" {
		return errors.New(fmt.Sprintf("corresponding OMS host not exist,region=%s", region))
	}
	timeout := OmsDefaultRequestTimeout
	if omsConfig.Timeout != 0 {
		timeout = time.Duration(omsConfig.Timeout) * time.Millisecond
	}

	omsHttpRequestOptions := HttpRequestOptions{
		RemoteService: OmsServiceName,
		Host:          host,
		Region:        region,
		Timeout:       timeout,
	}
	impl.omsHttpRequestOptions = omsHttpRequestOptions
	return nil
}

func (impl *BaseHttpClientImpl) initPmsHttpRequestOptions() error {
	pmsConfig := impl.configAccessor.GetIGSDynamicConfig().GetPmsConfig()
	region := config.GetRegion()
	host := pmsConfig.Host
	if host == "" {
		return errors.New(fmt.Sprintf("pms host not exist, region=%s", region))
	}
	timeout := PmsDefaultRequestTimeout
	if pmsConfig.Timeout != 0 {
		timeout = time.Duration(pmsConfig.Timeout) * time.Millisecond
	}

	pmsHttpRequestOptions := HttpRequestOptions{
		RemoteService: PmsServiceName,
		Host:          host,
		Region:        region,
		Timeout:       timeout,
	}
	impl.pmsHttpRequestOptions = pmsHttpRequestOptions
	return nil
}

type BaseHttpClient interface {
	Get(
		ctx context.Context,
		scheme Scheme,
		path string,
		queryParams *url.Values,
		body []byte,
		headers map[string]string,
		serviceName string,
	) (*http.Response, error)
	Post(
		ctx context.Context,
		scheme Scheme,
		path string,
		queryParams url.Values,
		body []byte,
		headers map[string]string,
		contentType ContentType,
		serviceName string,
	) (*http.Response, error)
	PostForm(
		ctx context.Context,
		scheme Scheme,
		path string,
		queryParams url.Values,
		bodyParams url.Values,
		headers map[string]string,
		serviceName string,
	) (*http.Response, error)
	GetRemoteService(serviceName string) string
}

type Scheme string
type ContentType string

const (
	SchemeHttps     Scheme      = "https"
	ApplicationJson ContentType = "application/json"
	FromContent     ContentType = "application/x-www-form-urlencoded"
)

func (impl *BaseHttpClientImpl) Get(
	ctx context.Context,
	scheme Scheme,
	path string,
	queryParams *url.Values,
	body []byte,
	headers map[string]string,
	serviceName string,
) (*http.Response, error) {
	host := impl.GetRequestOptions(serviceName).GetHost()
	reqUrl := &url.URL{
		Scheme:   string(scheme),
		Host:     host,
		Path:     path,
		RawQuery: queryParams.Encode(),
	}
	urlString := reqUrl.String()

	logCtx := logger.WithCtx(ctx).Withs(
		"scheme",
		scheme,
		"host",
		host,
		"path",
		path,
		"http_header",
		headers,
		"params",
		queryParams,
		"url",
		urlString,
	)

	request, err := http.NewRequest(http.MethodGet, urlString, bytes.NewBuffer(body))
	if err != nil {
		logCtx.Error("new request error", ulog.Error(err))
		return nil, err
	}

	for key, value := range headers {
		request.Header.Set(key, value)
	}
	response, err := impl.doHttp(request, serviceName)
	if err != nil {
		logCtx.Error("do http error", ulog.Error(err))
		return nil, err
	}

	logCtx.Withs("status_code", response.StatusCode).Info("success")
	return response, nil
}

func (impl *BaseHttpClientImpl) Post(
	ctx context.Context,
	scheme Scheme,
	path string,
	queryParams url.Values,
	body []byte,
	headers map[string]string,
	contentType ContentType,
	serviceName string,
) (*http.Response, error) {
	host := impl.GetRequestOptions(serviceName).GetHost()
	reqUrl := &url.URL{
		Scheme:   string(scheme),
		Host:     host,
		Path:     path,
		RawQuery: queryParams.Encode(),
	}
	urlString := reqUrl.String()
	request, err := http.NewRequest(http.MethodPost, urlString, bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}

	logger.WithCtx(ctx).Withs("http_header", headers).Info("post method header")
	for key, value := range headers {
		request.Header.Set(key, value)
	}
	request.Header.Set("Content-Type", string(contentType))
	response, err := impl.doHttp(request, serviceName)
	if err != nil {
		return nil, err
	}
	return response, nil
}

func (impl *BaseHttpClientImpl) PostForm(
	ctx context.Context,
	scheme Scheme,
	path string,
	queryParams url.Values,
	bodyParams url.Values,
	headers map[string]string,
	serviceName string,
) (*http.Response, error) {
	host := impl.GetRequestOptions(serviceName).GetHost()
	reqUrl := &url.URL{
		Scheme:   string(scheme),
		Host:     host,
		Path:     path,
		RawQuery: queryParams.Encode(),
	}
	body := []byte(bodyParams.Encode())
	urlString := reqUrl.String()
	request, err := http.NewRequest(http.MethodPost, urlString, bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}

	logger.WithCtx(ctx).Withs("http_header", headers).Info("post form method header")
	for key, value := range headers {
		request.Header.Set(key, value)
	}
	request.Header.Set("Content-Type", string(FromContent))
	response, err := impl.doHttp(request, serviceName)
	if err != nil {
		return nil, err
	}
	return response, nil
}

func (impl *BaseHttpClientImpl) doHttp(
	request *http.Request,
	serviceName string,
) (response *http.Response, err error) {
	requestOptions := impl.GetRequestOptions(serviceName)
	timeout := requestOptions.GetTimeout()

	switch serviceName {
	case OmsServiceName:
		response, err = impl.omsHttpClient.New().Do(request.Context(), request, timeout, nil, nil)
	case PmsServiceName:
		response, err = impl.pmsHttpClient.New().Do(request.Context(), request, timeout, nil, nil)
	default:
		response, err = impl.omsHttpClient.New().Do(request.Context(), request, timeout, nil, nil)
	}

	if err != nil {
		return nil, err
	}
	return response, nil
}

func (impl *BaseHttpClientImpl) GetRemoteService(serviceName string) string {
	return impl.GetRequestOptions(serviceName).GetRemoteService()
}

func GetResponseBody(ctx context.Context, httpResponse *http.Response) ([]byte, error) {
	logCtx := logger.WithCtx(ctx)

	defer func() {
		err := httpResponse.Body.Close()
		if err != nil {
			logCtx.Error("close http response stream error", ulog.Error(err))
		}
	}()

	responseBody, err := ioutil.ReadAll(httpResponse.Body)
	if err != nil {
		logCtx.Error("read http response stream error", ulog.Error(err))
		return nil, err
	}
	return responseBody, nil
}
