// Code generated by MockGen. DO NOT EDIT.
// Source: ../../../../../proto/spex/gen/go/marketplace_order_processing_fulfilment_sbs.pb/sbs.gas.client.go
//
// Generated by this command:
//
//	mockgen --build_flags=--mod=mod -source=../../../../../proto/spex/gen/go/marketplace_order_processing_fulfilment_sbs.pb/sbs.gas.client.go -destination=mock_spex_client.go -package=sbsapi -mock_names=Client=MockSpexClient
//

// Package sbsapi is a generated GoMock package.
package sbsapi

import (
	context "context"
	reflect "reflect"

	marketplace_order_processing_fulfilment_sbs "git.garena.com/shopee/marketplace-logistics/fulfilment_planning/internal/proto/spex/gen/go/marketplace_order_processing_fulfilment_sbs.pb"
	spex "git.garena.com/shopee/mts/go-application-server/spi/spex"
	gomock "go.uber.org/mock/gomock"
)

// MockSpexClient is a mock of Client interface.
type MockSpexClient struct {
	ctrl     *gomock.Controller
	recorder *MockSpexClientMockRecorder
}

// MockSpexClientMockRecorder is the mock recorder for MockSpexClient.
type MockSpexClientMockRecorder struct {
	mock *MockSpexClient
}

// NewMockSpexClient creates a new mock instance.
func NewMockSpexClient(ctrl *gomock.Controller) *MockSpexClient {
	mock := &MockSpexClient{ctrl: ctrl}
	mock.recorder = &MockSpexClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSpexClient) EXPECT() *MockSpexClientMockRecorder {
	return m.recorder
}

// AddBatchSbsShipmentGroup mocks base method.
func (m *MockSpexClient) AddBatchSbsShipmentGroup(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.AddBatchSbsShipmentGroupRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.AddBatchSbsShipmentGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddBatchSbsShipmentGroup", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.AddBatchSbsShipmentGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBatchSbsShipmentGroup indicates an expected call of AddBatchSbsShipmentGroup.
func (mr *MockSpexClientMockRecorder) AddBatchSbsShipmentGroup(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBatchSbsShipmentGroup", reflect.TypeOf((*MockSpexClient)(nil).AddBatchSbsShipmentGroup), varargs...)
}

// AddBatchShipmentGroupShop mocks base method.
func (m *MockSpexClient) AddBatchShipmentGroupShop(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.AddBatchShipmentGroupShopRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.AddBatchShipmentGroupShopResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddBatchShipmentGroupShop", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.AddBatchShipmentGroupShopResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddBatchShipmentGroupShop indicates an expected call of AddBatchShipmentGroupShop.
func (mr *MockSpexClientMockRecorder) AddBatchShipmentGroupShop(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBatchShipmentGroupShop", reflect.TypeOf((*MockSpexClient)(nil).AddBatchShipmentGroupShop), varargs...)
}

// AddShipmentGroupShopChangeLog mocks base method.
func (m *MockSpexClient) AddShipmentGroupShopChangeLog(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.AddShipmentGroupShopChangeLogRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.AddShipmentGroupShopChangeLogResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddShipmentGroupShopChangeLog", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.AddShipmentGroupShopChangeLogResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddShipmentGroupShopChangeLog indicates an expected call of AddShipmentGroupShopChangeLog.
func (mr *MockSpexClientMockRecorder) AddShipmentGroupShopChangeLog(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddShipmentGroupShopChangeLog", reflect.TypeOf((*MockSpexClient)(nil).AddShipmentGroupShopChangeLog), varargs...)
}

// BatchGetItemShopLocation mocks base method.
func (m *MockSpexClient) BatchGetItemShopLocation(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.BatchGetItemShopLocationRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.BatchGetItemShopLocationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetItemShopLocation", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.BatchGetItemShopLocationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetItemShopLocation indicates an expected call of BatchGetItemShopLocation.
func (mr *MockSpexClientMockRecorder) BatchGetItemShopLocation(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetItemShopLocation", reflect.TypeOf((*MockSpexClient)(nil).BatchGetItemShopLocation), varargs...)
}

// CheckSbsWarning mocks base method.
func (m *MockSpexClient) CheckSbsWarning(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.CheckSbsWarningRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.CheckSbsWarningResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckSbsWarning", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.CheckSbsWarningResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckSbsWarning indicates an expected call of CheckSbsWarning.
func (mr *MockSpexClientMockRecorder) CheckSbsWarning(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckSbsWarning", reflect.TypeOf((*MockSpexClient)(nil).CheckSbsWarning), varargs...)
}

// DeleteBatchShipmentGroupShop mocks base method.
func (m *MockSpexClient) DeleteBatchShipmentGroupShop(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.DeleteBatchShipmentGroupShopRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.DeleteBatchShipmentGroupShopResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteBatchShipmentGroupShop", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.DeleteBatchShipmentGroupShopResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteBatchShipmentGroupShop indicates an expected call of DeleteBatchShipmentGroupShop.
func (mr *MockSpexClientMockRecorder) DeleteBatchShipmentGroupShop(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBatchShipmentGroupShop", reflect.TypeOf((*MockSpexClient)(nil).DeleteBatchShipmentGroupShop), varargs...)
}

// EditBatchSbsShipmentGroup mocks base method.
func (m *MockSpexClient) EditBatchSbsShipmentGroup(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.EditBatchSbsShipmentGroupRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.EditBatchSbsShipmentGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EditBatchSbsShipmentGroup", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.EditBatchSbsShipmentGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EditBatchSbsShipmentGroup indicates an expected call of EditBatchSbsShipmentGroup.
func (mr *MockSpexClientMockRecorder) EditBatchSbsShipmentGroup(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditBatchSbsShipmentGroup", reflect.TypeOf((*MockSpexClient)(nil).EditBatchSbsShipmentGroup), varargs...)
}

// GetSbsConfig mocks base method.
func (m *MockSpexClient) GetSbsConfig(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.GetSbsConfigRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.GetSbsConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSbsConfig", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.GetSbsConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSbsConfig indicates an expected call of GetSbsConfig.
func (mr *MockSpexClientMockRecorder) GetSbsConfig(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSbsConfig", reflect.TypeOf((*MockSpexClient)(nil).GetSbsConfig), varargs...)
}

// GetSbsItemInfo mocks base method.
func (m *MockSpexClient) GetSbsItemInfo(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.GetSbsItemInfoRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.GetSbsItemInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSbsItemInfo", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.GetSbsItemInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSbsItemInfo indicates an expected call of GetSbsItemInfo.
func (mr *MockSpexClientMockRecorder) GetSbsItemInfo(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSbsItemInfo", reflect.TypeOf((*MockSpexClient)(nil).GetSbsItemInfo), varargs...)
}

// GetSbsShipmentGroupByStatus mocks base method.
func (m *MockSpexClient) GetSbsShipmentGroupByStatus(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.GetSbsShipmentGroupByStatusRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.GetSbsShipmentGroupByStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSbsShipmentGroupByStatus", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.GetSbsShipmentGroupByStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSbsShipmentGroupByStatus indicates an expected call of GetSbsShipmentGroupByStatus.
func (mr *MockSpexClientMockRecorder) GetSbsShipmentGroupByStatus(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSbsShipmentGroupByStatus", reflect.TypeOf((*MockSpexClient)(nil).GetSbsShipmentGroupByStatus), varargs...)
}

// GetSbsShipmentGroupInfo mocks base method.
func (m *MockSpexClient) GetSbsShipmentGroupInfo(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.GetSbsShipmentGroupInfoRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.GetSbsShipmentGroupInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSbsShipmentGroupInfo", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.GetSbsShipmentGroupInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSbsShipmentGroupInfo indicates an expected call of GetSbsShipmentGroupInfo.
func (mr *MockSpexClientMockRecorder) GetSbsShipmentGroupInfo(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSbsShipmentGroupInfo", reflect.TypeOf((*MockSpexClient)(nil).GetSbsShipmentGroupInfo), varargs...)
}

// GetShipmentGroupAuditLog mocks base method.
func (m *MockSpexClient) GetShipmentGroupAuditLog(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.GetShipmentGroupAuditLogRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.GetShipmentGroupAuditLogResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShipmentGroupAuditLog", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.GetShipmentGroupAuditLogResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShipmentGroupAuditLog indicates an expected call of GetShipmentGroupAuditLog.
func (mr *MockSpexClientMockRecorder) GetShipmentGroupAuditLog(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShipmentGroupAuditLog", reflect.TypeOf((*MockSpexClient)(nil).GetShipmentGroupAuditLog), varargs...)
}

// GetShipmentGroupIdByShopId mocks base method.
func (m *MockSpexClient) GetShipmentGroupIdByShopId(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.GetShipmentGroupIdByShopIdRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.GetShipmentGroupIdByShopIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShipmentGroupIdByShopId", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.GetShipmentGroupIdByShopIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShipmentGroupIdByShopId indicates an expected call of GetShipmentGroupIdByShopId.
func (mr *MockSpexClientMockRecorder) GetShipmentGroupIdByShopId(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShipmentGroupIdByShopId", reflect.TypeOf((*MockSpexClient)(nil).GetShipmentGroupIdByShopId), varargs...)
}

// GetShipmentGroupShop mocks base method.
func (m *MockSpexClient) GetShipmentGroupShop(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.GetShipmentGroupShopRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.GetShipmentGroupShopResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShipmentGroupShop", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.GetShipmentGroupShopResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShipmentGroupShop indicates an expected call of GetShipmentGroupShop.
func (mr *MockSpexClientMockRecorder) GetShipmentGroupShop(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShipmentGroupShop", reflect.TypeOf((*MockSpexClient)(nil).GetShipmentGroupShop), varargs...)
}

// GetShipmentGroupShopAuditLog mocks base method.
func (m *MockSpexClient) GetShipmentGroupShopAuditLog(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.GetShipmentGroupShopAuditLogRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.GetShipmentGroupShopAuditLogResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShipmentGroupShopAuditLog", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.GetShipmentGroupShopAuditLogResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShipmentGroupShopAuditLog indicates an expected call of GetShipmentGroupShopAuditLog.
func (mr *MockSpexClientMockRecorder) GetShipmentGroupShopAuditLog(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShipmentGroupShopAuditLog", reflect.TypeOf((*MockSpexClient)(nil).GetShipmentGroupShopAuditLog), varargs...)
}

// GetShipmentGroupShopChangeLog mocks base method.
func (m *MockSpexClient) GetShipmentGroupShopChangeLog(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.GetShipmentGroupShopChangeLogRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.GetShipmentGroupShopChangeLogResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShipmentGroupShopChangeLog", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.GetShipmentGroupShopChangeLogResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShipmentGroupShopChangeLog indicates an expected call of GetShipmentGroupShopChangeLog.
func (mr *MockSpexClientMockRecorder) GetShipmentGroupShopChangeLog(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShipmentGroupShopChangeLog", reflect.TypeOf((*MockSpexClient)(nil).GetShipmentGroupShopChangeLog), varargs...)
}

// GetShipmentGroupShopListByShopIdList mocks base method.
func (m *MockSpexClient) GetShipmentGroupShopListByShopIdList(ctx context.Context, req *marketplace_order_processing_fulfilment_sbs.GetShipmentGroupShopListByShopIdListRequest, opts ...spex.InvokeOption) (*marketplace_order_processing_fulfilment_sbs.GetShipmentGroupShopListByShopIdListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetShipmentGroupShopListByShopIdList", varargs...)
	ret0, _ := ret[0].(*marketplace_order_processing_fulfilment_sbs.GetShipmentGroupShopListByShopIdListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShipmentGroupShopListByShopIdList indicates an expected call of GetShipmentGroupShopListByShopIdList.
func (mr *MockSpexClientMockRecorder) GetShipmentGroupShopListByShopIdList(ctx, req any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShipmentGroupShopListByShopIdList", reflect.TypeOf((*MockSpexClient)(nil).GetShipmentGroupShopListByShopIdList), varargs...)
}
