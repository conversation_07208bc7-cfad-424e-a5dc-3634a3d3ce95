package approval_unit

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/business_audit"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type BusinessUnit interface {
	// Listener 监听审批结果
	Listener(ctx context.Context, ticketID string, approvalStatus string, tab *business_audit.BusinessAuditTab) *srerr.Error

	BusinessType() string
	GetExtraData() string //获取审批请求时的extra data
	//	{"application_info": {"Application Details:": {"href": "链接","text": "文案"}}}
	GetApplicationInfo(configDbId int64) string //获取审批请求时的application info
	GenerateAuditTab(ctx context.Context, configDbId uint64) *business_audit.BusinessAuditTab
}

var BusinessUnitMap = make(map[string]BusinessUnit, 0)

func AppendBusinessUnitMap(unit BusinessUnit) {
	if unit == nil {
		return
	}
	if _, ok := BusinessUnitMap[unit.BusinessType()]; !ok {
		BusinessUnitMap[unit.BusinessType()] = unit
	}
}

func GetBusinessUnit(businessType string) BusinessUnit {
	unit, ok := BusinessUnitMap[businessType]
	if ok {
		return unit
	}
	return nil
}
