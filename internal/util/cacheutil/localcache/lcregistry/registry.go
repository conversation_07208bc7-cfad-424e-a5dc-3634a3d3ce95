package lcregistry

import (
	"encoding/gob"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/config"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/parcel_type_definition"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/pickup_priority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule"
	rulevolume2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lane/lane_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/locationzone"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/masking_forecast/forecast_volume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/product"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/productpriority/entity"
	rule2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/routing_role"
	ruledata "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/rule_data"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrentity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volumerouting/vrrepo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/rulevolume"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/cc_routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/cacheutil/localcache"
	"time"
)

func init() {
	//不改变下面RoutingRule的model重新注册一个RoutingRuleParsed
	gob.Register(([]*rule2.RoutingRuleParsed)(nil))
	gob.Register((*rulevolume.MaskZoneVolume)(nil))
}

var LocalCacheConfig = []*localcache.Conf{
	localcache.NewConf(constant.LaneInfo, lane.DumpLaneInfo, (*lane_entity.LaneInfo)(nil)).WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.RoutingRule, ruledata.DumpRoutingRule, ([]*ruledata.RoutingRuleTab)(nil)).WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.RoutingRole, routing_role.DumpRoutingRole, (*routing_role.ProductRoutingRoleMap)(nil)).WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.LocationZone, locationzone.DumpLocationZone, false).WithReloadInterval(time.Minute * 3),
	localcache.NewConf(constant.VolumeZoneGroup, vrrepo.DumpZoneGroup, (*vrentity.ZoneGroupInfo)(nil)).WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.ProductVolumeRoutingRule, vrrepo.DumpProductVolumeRoutingRule, (*vrentity.VolumeRule)(nil)).WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.LineToVolumeGroup, vrrepo.DumpLineToVolumeGroup, "").WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.MaskLocationVolume, rulevolume.DumpMaskLocationVolume, (*rulevolume.MaskRouteVolume)(nil)).WithReloadInterval(time.Minute * 5),
	localcache.NewConf(constant.MaskAllocationConfig, config.DumpMaskAllocationConfig, (*config.MaskAllocationConfigTab)(nil)).WithReloadInterval(time.Minute * 5),
	localcache.NewConf(constant.MaskLogisticProductPriority, productpriority.DumpMaskLogisticProductPriority, (*entity.ProductPriority)(nil)).WithReloadInterval(time.Minute * 5),
	localcache.NewConf(constant.MaskEffectiveRule, rule.DumpMaskAllocationRule, (*rule.MaskAllocationRuleTab)(nil)).WithReloadInterval(time.Minute * 1),
	localcache.NewConf(constant.CCRoutingRule, cc_routing.DumpCCRoutingRule, (*cc_routing_rule.CCRoutingRule)(nil)).WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.ProductBaseInfoList, product.DumpProductBaseInfo, (*lpsclient.LogisticProductTab)(nil)).WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.ForecastMaskLocationVolume, forecast_volume.DumpMaskingForecastLocationVolume, (*forecast_volume.ForecastZoneVolumeTab)(nil)).WithReloadInterval(time.Minute * 5),
	localcache.NewConf(constant.MaskingProductRef, product.DumpMaskingProductRef, ([]*lpsclient.GetMaskingProductRefData)(nil)).WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.GroupCodeList, product.DumpGroupCodeList, "").WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.ProductLaneCode, product.DumpProductLaneCode, ([]lpsclient.LaneCodesInfo)(nil)).WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.BatchAllocateRuleVolume, rulevolume.DumpBatchAllocateRuleVolume, (*rulevolume2.MaskRuleVolumeTab)(nil)).WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.ProductNameDict, product.DumpProductNameMap, "").WithReloadInterval(time.Minute * 2),
	localcache.NewConf(constant.PickupPriority, pickup_priority.DumpPickupPriority, (map[int64]int64)(nil)).WithReloadInterval(time.Minute * 1),          //与MaskEffectiveRule保持一致
	localcache.NewConf(constant.MaskingWhitelist, whitelist.DumpMaskingWhitelist, (*whitelist.FulfillmentType)(nil)).WithReloadInterval(time.Minute * 1), //与MaskEffectiveRule保持一致
	localcache.NewConf(constant.ParcelTypeDefinition, parcel_type_definition.DumpParcelTypeDefinition, (*parcel_type_definition.ParcelTypeDefinitionTab)(nil)).WithReloadInterval(time.Minute * 2),
}
