package masking

import (
	"context"
	"git.garena.com/shopee/bg-logistics/go/chassis-saturn-server/saturn"
	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/constant"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/rule_mode"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/volumecounter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/prometheusutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/redisutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type ReportMaskingOrderCount struct {
	LpsApi lpsclient.LpsApi
}

func NewReportMaskingOrderCount(lpsApi lpsclient.LpsApi) *ReportMaskingOrderCount {
	return &ReportMaskingOrderCount{
		LpsApi: lpsApi,
	}
}

func (s *ReportMaskingOrderCount) Name() string {
	return constant.TaskNameReportMaskingOrderCount
}

func (s *ReportMaskingOrderCount) RpcHandle(ctx context.Context, args saturn.JobArgs) error {
	if err := s.ReportMaskingVolume(ctx); err != nil {
		return err
	}
	return nil
}

func (s *ReportMaskingOrderCount) ReportMaskingVolume(ctx context.Context) *srerr.Error {
	maskingProductRefList, err := s.LpsApi.GetMaskingProductRefList(ctx)
	if err != nil {
		return err
	}
	for _, maskingProductRef := range maskingProductRefList {
		if rErr := s.ReportMaskingProductVolume(ctx, int64(maskingProductRef.MaskingProductId)); rErr != nil {
			return rErr
		}
		// 上报非仓单运力
		if rErr := s.ReportMplFulfillmentProductVolume(ctx, maskingProductRef); rErr != nil {
			return rErr
		}
		// 上报仓单运力
		if rErr := s.ReportWmsFulfillmentProductVolume(ctx, maskingProductRef); rErr != nil {
			return rErr
		}
	}
	return nil
}

func (s *ReportMaskingOrderCount) ReportMaskingProductVolume(ctx context.Context, maskingProductId int64) *srerr.Error {
	key := volumecounter.ProductMaskVolumeKey(ctx, maskingProductId)
	maskProductVolume, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
	// The redis.Nil error indicates that the key does not exist, and no error is returned when the key does not exist
	if err != nil && err == redis.Nil {
		return nil
	}
	if err != nil {
		return srerr.With(srerr.RedisErr, "get masking product volume error", err)
	}
	prometheusutil.ReportMaskingProductVolume(ctx, maskingProductId, maskProductVolume)
	return nil
}

// ReportMplFulfillmentProductVolume 上报非仓单单运力
func (s *ReportMaskingOrderCount) ReportMplFulfillmentProductVolume(ctx context.Context, maskingProductRef *lpsclient.GetMaskingProductRefData) *srerr.Error {
	for _, fulfillmentProductId := range maskingProductRef.ComponentProductId {
		key := volumecounter.MaskProductVolumeKey(ctx, int64(maskingProductRef.MaskingProductId), int64(fulfillmentProductId), rule_mode.MplOrderRule)
		fulfillmentProductVolume, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
		// The redis.Nil error indicates that the key does not exist, and no error is returned when the key does not exist
		if err != nil && err == redis.Nil {
			continue
		}
		if err != nil {
			return srerr.With(srerr.RedisErr, "get fulfillment product volume error", err)
		}
		prometheusutil.ReportFulfillmentProductVolume(ctx, int64(maskingProductRef.MaskingProductId), int64(fulfillmentProductId), rule_mode.MplOrderRule.String(), fulfillmentProductVolume)
	}
	return nil
}

// ReportWmsFulfillmentProductVolume 上报仓单单运力
func (s *ReportMaskingOrderCount) ReportWmsFulfillmentProductVolume(ctx context.Context, maskingProductRef *lpsclient.GetMaskingProductRefData) *srerr.Error {
	for _, fulfillmentProductId := range maskingProductRef.ComponentProductId {
		key := volumecounter.MaskProductVolumeKey(ctx, int64(maskingProductRef.MaskingProductId), int64(fulfillmentProductId), rule_mode.WmsOrderRule)
		fulfillmentProductVolume, err := redisutil.GetDefaultInstance().Get(ctx, key).Int64()
		// The redis.Nil error indicates that the key does not exist, and no error is returned when the key does not exist
		if err != nil && err == redis.Nil {
			continue
		}
		if err != nil {
			return srerr.With(srerr.RedisErr, "get fulfillment product volume error", err)
		}
		prometheusutil.ReportFulfillmentProductVolume(ctx, int64(maskingProductRef.MaskingProductId), int64(fulfillmentProductId), rule_mode.WmsOrderRule.String(), fulfillmentProductVolume)
	}
	return nil
}
