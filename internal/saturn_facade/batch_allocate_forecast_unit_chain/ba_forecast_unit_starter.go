package batch_allocate_forecast_unit_chain

import (
	"context"
	"fmt"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-common/client/algorithm_client"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/forecast/repo"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/model"
	whitelist2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/batch_allocate/allocation"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/saturn_facade/batch_entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/allocation/whitelist"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/timeutil"
	"runtime"
	"time"
)

const (
	forecastUnitStarterName = "Forecast unit starter"
)

type ForecastUnitStarterImpl struct {
	next                      Job
	AlgoClient                algorithm_client.AlgoClientInterface
	BatchAllocateForecastRepo repo.BatchAllocateForecastRepo
	ShopWhitelistService      whitelist.ShopWhitelistService
}

func NewForecastUnitStarterImpl(
	BatchAllocateForecastRepo repo.BatchAllocateForecastRepo,
	shopWhitelistService whitelist.ShopWhitelistService,
) *ForecastUnitStarterImpl {
	return &ForecastUnitStarterImpl{
		AlgoClient:                algorithm_client.NewAlgoClient(),
		BatchAllocateForecastRepo: BatchAllocateForecastRepo,
		ShopWhitelistService:      shopWhitelistService,
	}
}

func (f *ForecastUnitStarterImpl) ExecuteJob(ctx context.Context, inv *Invocation) *srerr.Error {
	respChan := make(chan *batch_entity.BatchAllocateResp, 100) //todo:SSCSMR-1698:size 如何设置?
	go func(chan *batch_entity.BatchAllocateResp) {
		defer func() {
			logger.CtxLogInfof(ctx, "ForecastUnitStarterImpl| resp chan closed")
			close(respChan)
		}()
		defer func() {
			if err := recover(); err != nil {
				inv.shouldClosed.Set(true)
				var buf [panicMsgSize]byte
				n := runtime.Stack(buf[:], false)
				errMsg := fmt.Sprintf("[Recovery|ForecastUnitStarterImpl] panic recovered:\n%v \n%v", err, string(buf[:n]))
				logger.CtxLogErrorf(ctx, errMsg)
			}
		}()
		for batchAllocateReq := range inv.BatchAllocateReqChan {
			if inv.shouldClosed.Get() {
				break
			}

			if inv.AllocationRule.PickupEfficiencyWhitelistEnabled {
				// 走一遍Pickup Efficiency Whitelist的过滤逻辑
				for _, orderInfo := range batchAllocateReq.BAReq.Orders {
					orderInfo.ProductInfo = f.pickupEfficiencyWhitelistFilter(
						ctx, inv.BatchAllocateForecastTask.MaskingProductID, orderInfo.OrderId, int64(orderInfo.ShopID), orderInfo.ProductInfo,
					)
				}
			}

			// TODO go live时改成debug
			logger.CtxLogInfof(ctx, "request sdk args %s", objutil.JsonString(batchAllocateReq))
			startTime := timeutil.GetCurrentUnixTimeStamp(ctx)
			allocate, err := f.AlgoClient.BatchAllocate(ctx, batchAllocateReq.BAReq)
			logger.CtxLogInfof(ctx, "sdk allocate cost time:%v", timeutil.GetCurrentUnixTimeStamp(ctx)-startTime)
			if err != nil || allocate == nil || allocate.OrderResult == nil {
				inv.SetAllowFillIn(true)
				logger.CtxLogInfof(ctx, "ForecastUnitStarterImpl|set allow fill in true")
				logger.CtxLogErrorf(ctx, "ForecastUnitStarterImpl|batch allocate failed err:%v and allocate:%+v", err, allocate)
				//某个批次失败了，不影响整体流程，打上日志
				continue
			}
			respChan <- &batch_entity.BatchAllocateResp{
				BatchUnitId: batchAllocateReq.BatchUnitId,
				BAResp:      allocate,
				BAReq:       batchAllocateReq.BAReq,
				OrderInfo:   batchAllocateReq.OrderInfo,
				ExecuteTime: time.Now().Unix() - startTime, // nolint
			}
		}
	}(respChan)

	inv.BatchAllocateRespChan = respChan

	return f.next.ExecuteJob(ctx, inv)
}

func (f *ForecastUnitStarterImpl) SetNext(job Job) {
	f.next = job
}

func (f *ForecastUnitStarterImpl) UnitStarterJobName() string {
	return forecastUnitStarterName
}

func (f *ForecastUnitStarterImpl) UpdateForecastUnit(ctx context.Context, condition map[string]interface{}, tab model.BatchAllocateForecastUnitTab) *srerr.Error {
	return f.BatchAllocateForecastRepo.UpdateForecastUnit(ctx, condition, tab)
}

func (f *ForecastUnitStarterImpl) pickupEfficiencyWhitelistFilter(
	ctx context.Context, maskProductId int, orderId uint64, shopID int64, products []*algorithm_client.ProductInfoBo,
) []*algorithm_client.ProductInfoBo {

	if hit := f.ShopWhitelistService.CheckShopWhitelistAndFulfillmentType(ctx, shopID, whitelist2.MPLFulfillment); !hit {
		logger.CtxLogDebugf(ctx, "shop not in pickup efficiency whitelist | orderId:%d, shopId:%d", orderId, shopID)
		return products
	}

	priorityMap, err := f.ShopWhitelistService.GetPickupPriorityByMaskProductIDWithCache(ctx, int64(maskProductId))
	if err != nil {
		logger.CtxLogDebugf(ctx, "mask product no pickup priority")
	}

	return allocation.GetPickupEfficiencyWhitelistProduct(ctx, orderId, products, priorityMap)
}
