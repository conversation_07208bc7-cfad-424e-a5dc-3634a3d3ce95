package logger

import (
	"context"

	ctxutils2 "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/ctxutils"
)

func IsLogHit(ctx context.Context) bool {
	return ctxutils2.GetCtxData(ctx).Has(ctxutils2.LogHit)
}

func IsOpenLogTrace(ctx context.Context) bool {
	return ctxutils2.GetCtxData(ctx).Has(ctxutils2.OpenLogTrace)
}

func IsOpenLogParams(ctx context.Context) bool {
	return ctxutils2.GetCtxData(ctx).Has(ctxutils2.OpenLogParams)
}

func isForcePrintLog(ctx context.Context) bool {
	return ctxutils2.GetCtxData(ctx).Has(ctxutils2.LogForcePrint)
}

func isDisablePrintLog(ctx context.Context) bool {
	return ctxutils2.GetCtxData(ctx).Has(ctxutils2.LogDisabledPrint)
}
