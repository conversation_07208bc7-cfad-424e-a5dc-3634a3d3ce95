package adminfacade

import (
	"git.garena.com/shopee/bg-logistics/go/chassis/protocol/server/restful"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/schema/routing"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/usecase/routing_visualization"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/apiutil"
)

type RoutingVisual struct {
	RoutingVisualService routing_visualization.RoutingVisualService
}

func (r *RoutingVisual) URLPatterns() []restful.Route {
	////调度可视化数据展示接口
	routingVisual := restful.NewRouterGroup("api/admin/visual")
	routingVisual.POST("/list", r.GetList)
	routingVisual.GET("/detail", r.GetDetail)
	return routingVisual.GetRouters()
}

func (r *RoutingVisual) GetList(ctx *restful.Context) {
	req := &routing.RoutingVisualListReq{}

	if err := apiutil.SchemaParseValidate(ctx, req); err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}

	result, err := r.RoutingVisualService.GetList(ctx.Ctx, req.RequestId, req.ForderId, req.SlsTn, req.Pageno, req.Count)

	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, result)

}

func (r *RoutingVisual) GetDetail(ctx *restful.Context) {
	rowKey := ctx.ReadQueryParameter("row_key")

	if rowKey == "" {
		apiutil.FailJSONResp(ctx, nil, "row_key can not be empty")
		return
	}
	detail, err := r.RoutingVisualService.GetDetail(ctx.Ctx, rowKey)
	if err != nil {
		apiutil.FailJSONResp(ctx, err, err.Error())
		return
	}
	apiutil.SuccessJSONResp(ctx, detail)
}
