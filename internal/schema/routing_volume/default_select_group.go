package routing_volume

type (
	ListDefaultSelectGroupByPageRequest struct {
		PageNo   int64 `json:"page_no" validate:"min=1"`
		PageSize int64 `json:"page_size" validate:"min=1"`
	}

	ListDefaultSelectGroupByPageResponse struct {
		Total                  int64                     `json:"total"`
		DefaultSelectGroupList []*DefaultSelectGroupInfo `json:"default_select_group_info_list"`
	}

	DefaultSelectGroupInfo struct {
		DefaultSelectGroup int64          `json:"default_select_group"`
		ProductInfoList    []*ProductInfo `json:"product_info_list"`
	}

	ProductInfo struct {
		ProductId   int64  `json:"product_id"`
		ProductName string `json:"product_name"`
	}

	CreateDefaultSelectGroupRequest struct {
		DefaultSelectGroup int64   `json:"default_select_group" validate:"required"`
		ProductIdList      []int64 `json:"product_id_list"`
	}

	UpdateDefaultSelectGroupRequest struct {
		DefaultSelectGroup int64   `json:"default_select_group" validate:"required"`
		ProductIdList      []int64 `json:"product_id_list"`
	}

	GetDefaultSelectGroupRequest struct {
		DefaultSelectGroup int64 `json:"default_select_group" validate:"required"`
	}

	GetDefaultSelectGroupResponse struct {
		DefaultSelectGroupInfo
	}

	DeleteDefaultSelectGroupRequest struct {
		DefaultSelectGroup int64 `json:"default_select_group" validate:"required"`
	}

	SearchDefaultSelectGroupByProductIdRequest struct {
		ProductId int64 `json:"product_id" validate:"required"`
	}

	SearchDefaultSelectGroupByProductIdResponse struct {
		DefaultSelectGroupInfo
	}
)
