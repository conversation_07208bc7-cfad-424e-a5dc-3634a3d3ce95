package routing

import (
	"context"
	"sort"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/monitoring"

	"modernc.org/mathutil"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/objutil"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/available_lh/entity"
	entity2 "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/lh_capacity/entity"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/schedule_factor"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/volume_counter"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/external_gateway/lfslib"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

type ILHRoutingService interface {
	RevampILHRouting(ctx context.Context, req RevampILHRoutingRequest) (*RevampILHRoutingResult, *srerr.Error)
}

type ILHRoutingServiceImpl struct {
	ilhWeightCounter volume_counter.ILHWeightCounter
}

func NewILHRoutingServiceImpl(
	ilhWeightCounter volume_counter.ILHWeightCounter,
) *ILHRoutingServiceImpl {
	return &ILHRoutingServiceImpl{
		ilhWeightCounter: ilhWeightCounter,
	}
}

// buildSelectionStrategies 构建ILH选择策略链
func (rs *ILHRoutingServiceImpl) buildSelectionStrategies() []ILHSelectionStrategy {
	return []ILHSelectionStrategy{
		&ReservedBSAStrategy{rs: rs},
		&BSAStrategy{rs: rs},
		&AdhocStrategy{rs: rs},
		&WeightageStrategy{},
	}
}

// selectAvailableILHLinesWithCapacityUsage 选择可用的ILH线路并计算容量使用量
func (rs *ILHRoutingServiceImpl) selectAvailableILHLinesWithCapacityUsage(
	ctx context.Context,
	input ILHSelectionInput,
	availableLanes []*rule.RoutingLaneInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
) (*ILHSelectionResult, *srerr.Error) {
	// 提取ILH线路
	ilhLines := ExtractResourceLineIDs(availableLanes, lfslib.ILHLine)
	if len(ilhLines) == 0 {
		logger.CtxLogErrorf(ctx, "No ILH lines found in available lanes")
		return nil, srerr.New(srerr.DataErr, nil, "no ILH lines found in available lanes")
	}

	// 按优先级尝试各种选择策略
	strategies := rs.buildSelectionStrategies()
	for _, strategy := range strategies {
		selectedILH, found, result := strategy.Select(
			ctx, ilhLines, ilhCapacitySettingMap, availableLineInfoMap, input,
		)

		if found && selectedILH != "" && result != nil {
			// 获取策略名称并上报
			strategyName := strategy.StrategyName()
			monitoring.ReportSuccess(ctx, monitoring.CatModuleRevampILHRouting, "ILHStrategy"+strategyName, "")

			// 上报容量模式
			switch result.CapacityMode {
			case CapacityModeReservedBSA:
				monitoring.ReportSuccess(ctx, monitoring.CatModuleRevampILHRouting, monitoring.ILHCapacityModeReservedBSA, "")
			case CapacityModeBSA:
				monitoring.ReportSuccess(ctx, monitoring.CatModuleRevampILHRouting, monitoring.ILHCapacityModeBSA, "")
			case CapacityModeAdhoc:
				monitoring.ReportSuccess(ctx, monitoring.CatModuleRevampILHRouting, monitoring.ILHCapacityModeAdhoc, "")
			case CapacityModeSingleLane:
				monitoring.ReportSuccess(ctx, monitoring.CatModuleRevampILHRouting, monitoring.ILHCapacityModeSingleLane, "")
			}

			// 记录详细信息
			logger.CtxLogInfof(ctx, "Selected ILH line %s using capacity mode %s (Slot: %s), Usage: ReservedBSA=%d, NonReservedBSA=%d, Adhoc=%d",
				selectedILH, result.CapacityMode, result.SlotID, result.ReservedBSAUsage, result.NonReservedBSAUsage, result.AdhocUsage)
			return result, nil
		}
	}

	// 所有策略都失败，返回错误
	logger.CtxLogErrorf(ctx, "No available ILH line found after trying all strategies")
	monitoring.ReportError(ctx, monitoring.CatModuleRevampILHRouting, monitoring.AllILHStrategiesFailed, "")
	return nil, srerr.New(srerr.NoAvailableLane, nil, "no available ilh line found after trying all strategies")
}

// saveOrderUsageInfo 保存订单使用情况到Redis
func (rs *ILHRoutingServiceImpl) saveOrderUsageInfo(
	ctx context.Context,
	input *volume_counter.ILHOrderUsageInfo,
) {
	if input == nil || input.PackageNo == "" || input.ILHLineID == "" {
		logger.CtxLogErrorf(ctx, "Skipping save order usage info due to nil input, empty packageNo or lineID")
		return
	}

	err := rs.ilhWeightCounter.SaveOrderUsageInfo(ctx, input)
	if err != nil {
		// 保存失败通常不阻塞主流程，记录错误即可
		logger.CtxLogErrorf(ctx, "Failed to save order usage info for packageNo=%s, lineID=%s: %v", input.PackageNo, input.ILHLineID, err)
	}
}

// buildRoutingResult 构建路由结果
func buildRoutingResult(
	lane *rule.RoutingLaneInfo,
	reservedBSAUsage int64,
	nonReservedBSAUsage int64,
	adhocUsage int64,
	capacityMode string,
	slotID string,
	inheritedBSASlotUsage map[string]int64,
	inheritedAdhocSlotUsage map[string]int64,
) *RevampILHRoutingResult {
	return &RevampILHRoutingResult{
		Lane:                    lane,
		ReservedBSAUsage:        reservedBSAUsage,
		NonReservedBSAUsage:     nonReservedBSAUsage,
		AdhocUsage:              adhocUsage,
		InheritedBSASlotUsage:   inheritedBSASlotUsage,
		InheritedAdhocSlotUsage: inheritedAdhocSlotUsage,
		CapacityMode:            capacityMode,
		SlotID:                  slotID,
	}
}

// handleSingleAvailableLane 处理只有单一可用Lane的情况
func (rs *ILHRoutingServiceImpl) handleSingleAvailableLane(
	ctx context.Context,
	lane *rule.RoutingLaneInfo,
	packageNo string,
	input ILHSelectionInput,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
) (*RevampILHRoutingResult, *srerr.Error) {
	lineID := extractILHLineIDFromLane(lane)
	logger.CtxLogInfof(ctx, "Only one available lane found (%s) for packageNo=%s, selecting directly.", lane.LaneCode, packageNo)

	// 计算容量使用情况
	reservedBSAUsage, nonReservedBSAUsage, adhocUsage, slotID := rs.calculateSingleLaneCapacityUsage(
		ctx, lineID, input, ilhCapacitySettingMap,
	)

	// 调用 buildRoutingResult 时传递空 map
	result := buildRoutingResult(
		lane, reservedBSAUsage, nonReservedBSAUsage, adhocUsage, CapacityModeSingleLane, slotID,
		make(map[string]int64), make(map[string]int64),
	)

	// 保存订单使用情况
	usageInfo := &volume_counter.ILHOrderUsageInfo{
		PackageNo:               packageNo,
		ILHLineID:               lineID,
		ReservedBSAUsage:        result.ReservedBSAUsage,
		NonReservedBSAUsage:     result.NonReservedBSAUsage,
		AdhocUsage:              result.AdhocUsage,
		CapacityMode:            result.CapacityMode,
		SlotID:                  result.SlotID,
		InheritedBSASlotUsage:   result.InheritedBSASlotUsage,
		InheritedAdhocSlotUsage: result.InheritedAdhocSlotUsage,
	}
	rs.saveOrderUsageInfo(ctx, usageInfo)

	return result, nil
}

// handleSingleLaneAfterILHFiltering 处理ILH过滤后只剩一个Lane的情况
func (rs *ILHRoutingServiceImpl) handleSingleLaneAfterILHFiltering(
	ctx context.Context,
	lane *rule.RoutingLaneInfo,
	ilhResult *ILHSelectionResult,
	packageNo string,
) (*RevampILHRoutingResult, *srerr.Error) {
	logger.CtxLogInfof(ctx, "Only one lane (%s) remains after filtering by ILH %s for packageNo=%s, selecting it.",
		lane.LaneCode, ilhResult.LineID, packageNo)

	// 调用 buildRoutingResult 时传递 ilhResult 中的继承信息
	result := buildRoutingResult(
		lane, ilhResult.ReservedBSAUsage, ilhResult.NonReservedBSAUsage, ilhResult.AdhocUsage, ilhResult.CapacityMode, ilhResult.SlotID,
		ilhResult.InheritedBSASlotUsage, ilhResult.InheritedAdhocSlotUsage,
	)

	// 保存订单使用情况
	usageInfo := &volume_counter.ILHOrderUsageInfo{
		PackageNo:               packageNo,
		ILHLineID:               ilhResult.LineID,
		ReservedBSAUsage:        result.ReservedBSAUsage,
		NonReservedBSAUsage:     result.NonReservedBSAUsage,
		AdhocUsage:              result.AdhocUsage,
		CapacityMode:            result.CapacityMode,
		SlotID:                  result.SlotID,
		InheritedBSASlotUsage:   result.InheritedBSASlotUsage,
		InheritedAdhocSlotUsage: result.InheritedAdhocSlotUsage,
	}
	rs.saveOrderUsageInfo(ctx, usageInfo)

	return result, nil
}

// RevampILHRouting 实现ILH路由选择功能
//
// 整体流程如下：
// 1. 按配置过滤可用Lanes
// 2. 如只有单一Lane可用，则直接返回结果
// 3. 选择可用的ILH线路并计算容量使用量（按策略顺序：预留BSA容量、BSA容量、Adhoc容量、权重）
// 4. 按选中的ILH线路过滤可用Lane
// 5. 如按ILH过滤后只有单一Lane可用，则直接返回结果
// 6. 选择CC线路，按选中的CC线路过滤可用Lane
// 7. 构建最终的路由结果
// 8. 保存订单使用情况
func (rs *ILHRoutingServiceImpl) RevampILHRouting(ctx context.Context, req RevampILHRoutingRequest) (*RevampILHRoutingResult, *srerr.Error) {
	logger.CtxLogInfof(ctx, "Starting RevampILHRouting for Request: %s", objutil.JsonString(req))

	// 获取可用的线路信息
	availableLineInfoMap := req.AvailableLHRule.GetAvailableLineMap()

	// 1. 按配置过滤可用Lanes
	availableLanes, err := rs.filterAvailableLanesByConfig(ctx, req.AvailableLanes, req.LMID, req.AvailableLHRule)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Error filtering available lanes by config: %v", err)
		return nil, err
	}

	if len(availableLanes) == 0 {
		logger.CtxLogErrorf(ctx, "No available lanes after filtering by config for packageNo=%s", req.PackageNo)
		monitoring.ReportError(ctx, monitoring.CatModuleRevampILHRouting, monitoring.NoAvailableLane, req.PackageNo)
		return nil, srerr.New(srerr.DataErr, nil, "no available lanes after filtering by config")
	}

	input := ILHSelectionInput{
		ProductID:   req.ProductID,
		DGType:      req.DGType,
		OrderWeight: req.OrderWeight,
		OrderTime:   req.OrderTime,
	}

	// 2. 处理单一Lane情况
	if len(availableLanes) == 1 {
		return rs.handleSingleAvailableLane(ctx, availableLanes[0], req.PackageNo, input, req.ILHCapacitySettingMap)
	}

	// 3. 选择可用的ILH线路并计算容量使用量
	ilhResult, err := rs.selectAvailableILHLinesWithCapacityUsage(
		ctx, input, availableLanes, availableLineInfoMap, req.ILHCapacitySettingMap,
	)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Error selecting ILH line for packageNo=%s: %v", req.PackageNo, err)
		monitoring.ReportError(ctx, monitoring.CatModuleRevampILHRouting, monitoring.NoAvailableILH, req.PackageNo)
		return nil, err
	}

	if ilhResult.LineID == "" { // selectAvailableILHLinesWithCapacityUsage内部应该已经处理了找不到的情况并返回错误，这里是防御性检查
		logger.CtxLogErrorf(ctx, "No available ILH line ID returned for packageNo=%s", req.PackageNo)
		monitoring.ReportError(ctx, monitoring.CatModuleRevampILHRouting, monitoring.NoAvailableILH, req.PackageNo)
		return nil, srerr.New(srerr.DataErr, nil, "no available ILH lines found")
	}

	logger.CtxLogInfof(ctx, "Selected ILH line %s for packageNo=%s with mode %s (Slot: %s), Usage: ReservedBSA=%d, NonReservedBSA=%d, Adhoc=%d",
		ilhResult.LineID, req.PackageNo, ilhResult.CapacityMode, ilhResult.SlotID,
		ilhResult.ReservedBSAUsage, ilhResult.NonReservedBSAUsage, ilhResult.AdhocUsage)

	// 4. 按选中的ILH线路过滤可用Lane
	availableLanesAfterILH := filterLanesByResourceSubType(availableLanes, ilhResult.LineID, lfslib.ILHLine)
	if len(availableLanesAfterILH) == 0 {
		logger.CtxLogErrorf(ctx, "No available lanes found after filtering by selected ILH %s for packageNo=%s", ilhResult.LineID, req.PackageNo)
		monitoring.ReportError(ctx, monitoring.CatModuleRevampILHRouting, monitoring.NoAvailableLaneAfterILHFilter, req.PackageNo)
		return nil, srerr.New(srerr.DataErr, nil, "no available lanes after ILH filtering")
	}

	// 5. 处理按ILH过滤后单一Lane情况
	if len(availableLanesAfterILH) == 1 {
		return rs.handleSingleLaneAfterILHFiltering(ctx, availableLanesAfterILH[0], ilhResult, req.PackageNo)
	}

	// 6. 选择CC线路并过滤Lane
	finalLane, err := rs.selectCCAndFilterLanes(
		ctx, req.ProductID, req.PackageNo, availableLanesAfterILH, availableLineInfoMap, ilhResult.LineID,
	)
	if err != nil {
		monitoring.ReportError(ctx, monitoring.CatModuleRevampILHRouting, monitoring.NoAvailableCC, req.PackageNo)
		return nil, err
	}

	// 7. 构建最终的路由结果
	result := buildRoutingResult(
		finalLane, ilhResult.ReservedBSAUsage, ilhResult.NonReservedBSAUsage, ilhResult.AdhocUsage, ilhResult.CapacityMode, ilhResult.SlotID,
		ilhResult.InheritedBSASlotUsage, ilhResult.InheritedAdhocSlotUsage,
	)

	// 8. 保存订单使用情况
	usageInfo := &volume_counter.ILHOrderUsageInfo{
		PackageNo:               req.PackageNo,
		ILHLineID:               ilhResult.LineID,
		ReservedBSAUsage:        result.ReservedBSAUsage,
		NonReservedBSAUsage:     result.NonReservedBSAUsage,
		AdhocUsage:              result.AdhocUsage,
		CapacityMode:            result.CapacityMode,
		SlotID:                  result.SlotID,
		InheritedBSASlotUsage:   result.InheritedBSASlotUsage,
		InheritedAdhocSlotUsage: result.InheritedAdhocSlotUsage,
	}
	rs.saveOrderUsageInfo(ctx, usageInfo)

	// 上报成功
	monitoring.ReportSuccess(ctx, monitoring.CatModuleRevampILHRouting, monitoring.ILHRoutingSuccess, req.PackageNo)
	return result, nil
}

// selectCCLine 选择CC线路
func (rs *ILHRoutingServiceImpl) selectCCLine(
	ctx context.Context,
	productID int,
	availableLanes []*rule.RoutingLaneInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
) (string, *srerr.Error) {
	// 从可用的lanes中提取CC类型的线路
	ccLines := ExtractResourceLineIDs(availableLanes, lfslib.CCLine)

	// 处理没有CC线路或只有一条线路的简单情况
	if len(ccLines) == 0 {
		logger.CtxLogErrorf(ctx, "No available CC lines found from %d lanes", len(availableLanes))
		return "", srerr.New(srerr.DataErr, nil, "No available CC lines")
	}

	if len(ccLines) == 1 {
		ccLineID := ccLines[0]
		// 尝试获取CC线路的用量，用于日志记录
		ccWeight, err := rs.ilhWeightCounter.GetCCWeight(ctx, productID, ccLineID)
		if err != nil {
			logger.CtxLogDebugf(ctx, "Failed to get CC weight for single line=%s: %v", ccLineID, err)
		} else {
			logger.CtxLogInfof(ctx, "Single CC line %s weight: %v", ccLineID, ccWeight)
		}
		return ccLineID, nil
	}

	// 处理多条CC线路的情况
	return rs.selectOptimalCCLine(ctx, productID, ccLines, availableLineInfoMap)
}

// selectOptimalCCLine 从多条CC线路中选择最优的一条
func (rs *ILHRoutingServiceImpl) selectOptimalCCLine(
	ctx context.Context,
	productID int,
	ccLines []string,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
) (string, *srerr.Error) {
	// 获取各CC当前重量
	ccCurrentWeightMap := rs.getCCLinesCurrentWeight(ctx, productID, ccLines)

	// 按最大重量筛选（过滤掉已满的CC）
	notMeetMaxWeightCC := rs.filterCCLinesByMaxWeight(ctx, ccCurrentWeightMap, availableLineInfoMap)

	// 处理所有CC都已满的情况
	if len(notMeetMaxWeightCC) == 0 {
		logger.CtxLogErrorf(ctx, "All CC lines (%v) are full, selecting by weightage", ccLines)
		monitoring.ReportSuccess(ctx, monitoring.CatModuleRevampILHRouting, monitoring.CCStrategyWeightageForFullLines, "")
		return selectByWeightage(ctx, ccLines, availableLineInfoMap), nil
	}

	// 处理筛选后只剩一条CC的情况
	if len(notMeetMaxWeightCC) == 1 {
		logger.CtxLogInfof(ctx, "Single CC line %s found after filtering by max weight", notMeetMaxWeightCC[0])
		monitoring.ReportSuccess(ctx, monitoring.CatModuleRevampILHRouting, monitoring.CCStrategySingleLineAfterMaxFilter, "")
		return notMeetMaxWeightCC[0], nil
	}

	// 按最小重量筛选（优先选择未达到最小启运量的CC）
	notMeetMinWeightCC := rs.filterCCLinesByMinWeight(ctx, notMeetMaxWeightCC, ccCurrentWeightMap, availableLineInfoMap)

	// 处理所有CC都已达到最小启运量的情况
	if len(notMeetMinWeightCC) == 0 {
		logger.CtxLogErrorf(ctx, "All not meet max weight CC lines (%v) are meet min weight, selecting by weightage", notMeetMaxWeightCC)
		monitoring.ReportSuccess(ctx, monitoring.CatModuleRevampILHRouting, monitoring.CCStrategyWeightageForMinWeightLines, "")
		return selectByWeightage(ctx, notMeetMaxWeightCC, availableLineInfoMap), nil
	}

	// 处理筛选后只剩一条CC的情况
	if len(notMeetMinWeightCC) == 1 {
		logger.CtxLogInfof(ctx, "Single not meet max weight CC line %s found after filtering by min weight", notMeetMinWeightCC[0])
		monitoring.ReportSuccess(ctx, monitoring.CatModuleRevampILHRouting, monitoring.CCStrategySingleLineAfterMinFilter, "")
		return notMeetMinWeightCC[0], nil
	}

	// 多条候选CC，按权重选择
	logger.CtxLogInfof(ctx, "Multiple not meet min weight CC lines (%v) found, selecting by weightage", notMeetMinWeightCC)
	monitoring.ReportSuccess(ctx, monitoring.CatModuleRevampILHRouting, monitoring.CCStrategyWeightageForMultipleLines, "")
	return selectByWeightage(ctx, notMeetMinWeightCC, availableLineInfoMap), nil
}

// getCCLinesCurrentWeight 获取多条CC线路的当前重量
func (rs *ILHRoutingServiceImpl) getCCLinesCurrentWeight(
	ctx context.Context,
	productID int,
	ccLines []string,
) map[string]int64 {
	ccCurrentWeightMap := make(map[string]int64, len(ccLines))
	for _, ccLineID := range ccLines {
		ccWeight, err := rs.ilhWeightCounter.GetCCWeight(ctx, productID, ccLineID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "Failed to get CC weight for line=%s: %v", ccLineID, err)
			continue
		}
		ccCurrentWeightMap[ccLineID] = ccWeight
	}
	return ccCurrentWeightMap
}

// filterCCLinesByMaxWeight 根据最大重量筛选CC线路（过滤掉已满的线路）
func (rs *ILHRoutingServiceImpl) filterCCLinesByMaxWeight(
	ctx context.Context,
	ccCurrentWeightMap map[string]int64,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
) []string {
	var notMeetMaxWeightCC []string
	for cc, currentWeight := range ccCurrentWeightMap {
		maxWeight := int64(availableLineInfoMap[cc].MaxWeight * 1000) // KG -> G
		if maxWeight != 0 && currentWeight >= maxWeight {
			logger.CtxLogInfof(ctx, "CC line %s is full, current weight=%d, max weight=%d, skipping", cc, currentWeight, maxWeight)
			continue
		}
		notMeetMaxWeightCC = append(notMeetMaxWeightCC, cc)
	}
	return notMeetMaxWeightCC
}

// filterCCLinesByMinWeight 根据最小重量筛选CC线路（优先选择未达到最小启运量的线路）
func (rs *ILHRoutingServiceImpl) filterCCLinesByMinWeight(
	ctx context.Context,
	notMeetMaxWeightCC []string,
	ccCurrentWeightMap map[string]int64,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
) []string {
	var notMeetMinWeightCC []string
	for _, cc := range notMeetMaxWeightCC {
		minWeight := int64(availableLineInfoMap[cc].MinWeight * 1000) // KG -> G
		currentWeight := ccCurrentWeightMap[cc]
		if minWeight != 0 && currentWeight < minWeight {
			logger.CtxLogInfof(ctx, "CC line %s is not meet min weight, current weight=%d, min weight=%d, candidate",
				cc, currentWeight, minWeight)
			notMeetMinWeightCC = append(notMeetMinWeightCC, cc)
		}
	}
	return notMeetMinWeightCC
}

// filterAvailableLanesByConfig 根据配置过滤可用Lane
func (rs *ILHRoutingServiceImpl) filterAvailableLanesByConfig(
	ctx context.Context,
	availableLanes []*rule.RoutingLaneInfo,
	lmID string,
	availableLHRule entity.AvailableLHRule,
) ([]*rule.RoutingLaneInfo, *srerr.Error) {
	// 获取可用线路映射和组合设置
	availableLineInfoMap := availableLHRule.GetAvailableLineMap()
	availableCombination := GetAvailableCombination(ctx, availableLHRule.CombinationSettings)

	// 根据可用线路过滤Lane
	realAvailableLanes := make([]*rule.RoutingLaneInfo, 0)
	for _, availableLane := range availableLanes {
		// 检查Lane中的所有线路是否可用
		isAvailable := true
		for _, lineInfo := range availableLane.LineList {
			if !lineInfo.NeedRouting() {
				continue
			}

			if _, exist := availableLineInfoMap[lineInfo.LineId]; !exist {
				isAvailable = false
				break
			}
		}

		if !isAvailable {
			continue
		}

		// 检查ILH组合是否可用
		ilh, importILH := schedule_factor.GetIlhAndImportIlhFromLaneInfo(availableLane)
		combination := rule.IlhCombination{
			Ilh:       ilh,
			ImportIlh: importILH,
			Lm:        lmID,
		}

		if len(availableCombination) > 0 {
			// 如果配置了AvailableCombination，则需要进行检查
			if _, exist := availableCombination[combination]; !exist {
				continue
			}
		}

		realAvailableLanes = append(realAvailableLanes, availableLane)
	}

	if len(realAvailableLanes) == 0 {
		logger.CtxLogInfof(ctx, "No available lanes after filtering by configuration")
	} else {
		logger.CtxLogInfof(ctx, "Found available lanes after filtering by configuration: %s", objutil.JsonString(realAvailableLanes))
	}

	return realAvailableLanes, nil
}

// prepareILHCapacityInfo 提取获取ILH容量配置信息的公共逻辑
// 返回值：ILH线路ID，容量配置信息，时段ID，是否成功准备信息
func (rs *ILHRoutingServiceImpl) prepareILHCapacityInfo(
	ctx context.Context,
	ilh string,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	timestamp int64,
) (entity2.ILHCapacitySettingInfo, string, bool) {
	// 获取ILH线路的容量配置
	capacitySettingInfo, ok := ilhCapacitySettingMap[ilh]
	if !ok {
		logger.CtxLogDebugf(ctx, "ILH=%s has no capacity settings", ilh)
		return entity2.ILHCapacitySettingInfo{}, "", false
	}

	// 获取时间段ID
	slotID := capacitySettingInfo.GetRelevantSlotID(ctx, timestamp)
	return capacitySettingInfo, slotID, true
}

// checkCapacitySufficient 检查给定的可用容量是否足够承载订单重量
//
// 业务逻辑说明：
// 1. 容量充足性检查：确保 orderWeight <= availableCapacity
// 2. 日志记录：记录容量检查结果，便于问题排查和容量监控
// 3. 参数说明：
//   - orderWeight: 订单重量，需要消耗的容量
//   - availableCapacity: 实际可用容量（已扣除当前使用量）
//   - capacityMode: 容量模式标识，用于日志区分（ReservedBSA/BSA/Adhoc）
//
// 4. 返回值：true表示容量充足，false表示容量不足
func (rs *ILHRoutingServiceImpl) checkCapacitySufficient(
	ctx context.Context,
	ilh string,
	capacityMode string,
	orderWeight int64,
	availableCapacity int64,
	slotID string,
) bool {
	// 判断是否有足够的容量
	if orderWeight <= availableCapacity {
		logger.CtxLogDebugf(ctx, "ILH=%s has sufficient %s capacity in slot %s: orderWeight=%d, availableCapacity=%d",
			ilh, capacityMode, slotID, orderWeight, availableCapacity)
		return true
	}

	logger.CtxLogDebugf(ctx, "ILH=%s has insufficient %s capacity in slot %s: orderWeight=%d, availableCapacity=%d",
		ilh, capacityMode, slotID, orderWeight, availableCapacity)
	return false
}

// selectOptimalILHFromResults 从候选结果中选择最优ILH
func (rs *ILHRoutingServiceImpl) selectOptimalILHFromResults(
	ctx context.Context,
	availableILHResults map[string]*ILHSelectionResult,
	productID int,
	dgType int,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
	timestamp int64,
	capacityType string,
) (string, *ILHSelectionResult) {
	// 如果只有一个可用ILH，直接选择
	if len(availableILHResults) == 1 {
		for ilh, result := range availableILHResults {
			logger.CtxLogInfof(ctx, "Selected single available ILH=%s using %s capacity", ilh, capacityType)
			return ilh, result
		}
	}

	// 多个可用ILH，选择最优的
	candidateILHs := make([]string, 0, len(availableILHResults))
	for ilh := range availableILHResults {
		candidateILHs = append(candidateILHs, ilh)
	}

	var selectedILH string
	switch capacityType {
	case CapacityModeReservedBSA:
		selectedILH = rs.selectOptimalReservedBSAILH(ctx, candidateILHs, productID, dgType, timestamp, ilhCapacitySettingMap, availableLineInfoMap)
	case CapacityModeBSA:
		selectedILH = rs.selectOptimalBSAILH(ctx, candidateILHs, productID, dgType, ilhCapacitySettingMap, availableLineInfoMap, timestamp)
	case CapacityModeAdhoc:
		selectedILH = rs.selectOptimalAdhocILH(ctx, candidateILHs, productID, dgType, ilhCapacitySettingMap, availableLineInfoMap, timestamp)
	default:
		// 默认按权重选择
		selectedILH = selectByWeightage(ctx, candidateILHs, availableLineInfoMap)
	}

	logger.CtxLogInfof(ctx, "Selected optimal ILH=%s from candidates %v using %s capacity",
		selectedILH, candidateILHs, capacityType)

	return selectedILH, availableILHResults[selectedILH]
}

// selectILHByReservedBSACapacityWithUsage 根据产品预留BSA容量选择ILH并返回详细的容量使用情况
//
// 业务逻辑说明：
// 1. 预留BSA模式：只使用当前产品的专用预留BSA容量 + 共享的继承BSA容量
// 2. 容量检查：确保当前产品的BSA使用量 + 订单重量 <= 可用的预留BSA总容量
// 3. 优先级：优先使用继承时段的BSA容量，然后使用当前时段的产品预留BSA容量
func (rs *ILHRoutingServiceImpl) selectILHByReservedBSACapacityWithUsage(
	ctx context.Context,
	input ILHSelectionInput, // 使用结构体封装参数
	ilhLines []string,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
) (string, bool, *ILHSelectionResult, *srerr.Error) {
	logger.CtxLogInfof(ctx, "Selecting ILH by reserved BSA capacity, candidates=%v, order_weight=%d", ilhLines, input.OrderWeight)
	availableILHResults := make(map[string]*ILHSelectionResult)

	for _, ilh := range ilhLines {
		// 1. 准备ILH容量配置信息
		capacitySettingInfo, slotID, ok := rs.prepareILHCapacityInfo(ctx, ilh, ilhCapacitySettingMap, input.OrderTime)
		if !ok {
			continue
		}

		// 2. 获取继承时间段的可用BSA容量（历史时段未使用完的容量可以被当前时段继承使用）
		availableInheritedBSACapacity, _, err := rs.getInheritedSlotCapacities(ctx, ilh, capacitySettingInfo, input.DGType)
		if err != nil {
			logger.CtxLogErrorf(ctx, "selectILHByReservedBSACapacity: Failed to get inherited capacities for ILH=%s: %v", ilh, err)
			continue // 获取继承容量失败，跳过此ILH，继续尝试其他ILH
		}

		// 3. 获取当前产品的专用预留BSA容量配置
		capacitySetting := capacitySettingInfo.CapacitySetting
		reservedBSACapacity, _ := capacitySetting.GetProductReservedBSAWeightUnitG(input.ProductID)

		// 4. 获取当前产品在当前时段的BSA使用量（只包含该产品的使用量）
		currentSlotProductBSAWeight, err := rs.ilhWeightCounter.GetILHProductBSAWeight(ctx, input.ProductID, ilh, input.DGType, capacitySettingInfo.TWS, capacitySettingInfo.DestPorts, slotID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetILHProductBSAWeight error|ilh=%s|err=%v", ilh, err)
			continue // API调用失败，跳过此ILH，避免阻塞整个选择流程
		}

		// 5. 计算当前产品在预留BSA模式下的实际可用容量
		// 业务逻辑：预留BSA容量 = 当前时段产品专用预留BSA剩余 + 历史时段继承BSA剩余
		availableCurrentReservedBSA := mathutil.MaxInt64(0, reservedBSACapacity-currentSlotProductBSAWeight)

		// 计算继承BSA的总可用量
		totalAvailableInheritedBSA := int64(0)
		for _, capacity := range availableInheritedBSACapacity {
			totalAvailableInheritedBSA += capacity
		}

		// 当前产品实际可用的预留BSA总容量
		actualAvailableReservedBSACapacity := availableCurrentReservedBSA + totalAvailableInheritedBSA

		// 6. 业务规则检查：确保订单重量不超过当前产品可用的预留BSA容量
		hasSufficientCapacity := rs.checkCapacitySufficient(ctx, ilh, CapacityModeReservedBSA, input.OrderWeight, actualAvailableReservedBSACapacity, slotID)
		if !hasSufficientCapacity {
			continue // 容量不足，尝试下一个ILH
		}

		// 7. 计算详细的容量消耗分布（用于记录和监控）
		// 消耗顺序：继承BSA（按SlotID升序） -> 当前时段产品预留BSA
		reservedUsage, nonReservedUsage, inheritedUsage := rs.calculateDetailedBSAUsageDistribution(
			ctx, input.OrderWeight, availableCurrentReservedBSA, 0, availableInheritedBSACapacity,
		)

		// 8. 构建选择结果，记录容量使用情况
		availableILHResults[ilh] = &ILHSelectionResult{
			LineID:                  ilh,
			ReservedBSAUsage:        reservedUsage,
			NonReservedBSAUsage:     nonReservedUsage,
			AdhocUsage:              0, // 预留BSA模式不使用Adhoc容量
			InheritedBSASlotUsage:   inheritedUsage,
			InheritedAdhocSlotUsage: make(map[string]int64), // 预留BSA模式不使用继承的Adhoc容量
			CapacityMode:            CapacityModeReservedBSA,
			SlotID:                  slotID,
		}
	}

	if len(availableILHResults) > 0 {
		selectedILH, result := rs.selectOptimalILHFromResults(
			ctx, availableILHResults, input.ProductID, input.DGType, ilhCapacitySettingMap,
			availableLineInfoMap, input.OrderTime, CapacityModeReservedBSA,
		)
		return selectedILH, true, result, nil
	}

	logger.CtxLogInfof(ctx, "No ILH with sufficient reserved BSA capacity (including inherited) found")
	return "", false, nil, nil
}

// selectILHByBSACapacityWithUsage 根据BSA容量选择ILH并返回详细的容量使用情况
//
// 业务逻辑说明：
// 1. BSA模式：使用当前产品的专用预留BSA + 共享的非预留BSA + 共享的继承BSA容量
// 2. 容量类型区分：
//   - 产品预留BSA：只有该产品可以使用，确保重要产品的资源保障
//   - 非预留BSA：所有产品共享使用，提高资源利用率
//   - 继承BSA：历史时段未使用完的BSA容量，所有产品共享
//
// 3. 容量检查：确保订单重量 <= 当前产品实际可用的BSA总容量
// 4. 消耗优先级：继承BSA -> 产品预留BSA -> 非预留BSA
func (rs *ILHRoutingServiceImpl) selectILHByBSACapacityWithUsage(
	ctx context.Context,
	input ILHSelectionInput,
	ilhLines []string,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
) (string, bool, *ILHSelectionResult, *srerr.Error) {
	logger.CtxLogInfof(ctx, "Selecting ILH by BSA capacity, candidates=%v, order_weight=%d", ilhLines, input.OrderWeight)
	availableILHResults := make(map[string]*ILHSelectionResult)

	for _, ilh := range ilhLines {
		// 1. 准备ILH容量配置信息
		capacitySettingInfo, slotID, ok := rs.prepareILHCapacityInfo(ctx, ilh, ilhCapacitySettingMap, input.OrderTime)
		if !ok {
			continue
		}

		// 2. 获取继承时间段的可用BSA容量（历史时段的剩余容量）
		availableInheritedBSACapacity, _, err := rs.getInheritedSlotCapacities(ctx, ilh, capacitySettingInfo, input.DGType)
		if err != nil {
			logger.CtxLogErrorf(ctx, "selectILHByBSACapacity: Failed to get inherited capacities for ILH=%s: %v", ilh, err)
			continue // 获取继承容量失败，跳过此ILH，继续尝试其他ILH
		}

		// 3. 获取当前时段的BSA容量配置
		capacitySetting := capacitySettingInfo.CapacitySetting
		reservedBSACapacity, _ := capacitySetting.GetProductReservedBSAWeightUnitG(input.ProductID) // 当前产品专用容量
		nonReservedBSACapacity := capacitySetting.GetNonReservedBSAWeightUnitG()                    // 所有产品共享容量

		// 4. 获取当前时段的实际使用量（用于计算剩余可用容量）
		// 获取所有产品的BSA总使用量
		currentSlotBSAWeight, err := rs.ilhWeightCounter.GetILHBSAWeight(ctx, ilh, input.DGType, capacitySettingInfo.TWS, capacitySettingInfo.DestPorts, slotID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetILHBSAWeight error|ilh=%s|err=%v", ilh, err)
			continue // API调用失败，跳过此ILH
		}

		// 获取当前产品的BSA使用量
		currentSlotProductBSAWeight, err := rs.ilhWeightCounter.GetILHProductBSAWeight(ctx, input.ProductID, ilh, input.DGType, capacitySettingInfo.TWS, capacitySettingInfo.DestPorts, slotID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetILHProductBSAWeight error|ilh=%s|err=%v", ilh, err)
			continue // API调用失败，跳过此ILH
		}

		// 5. 计算当前产品在BSA模式下的实际可用容量
		// 业务逻辑：通过差值计算各类容量的剩余量，确保不会出现负数
		nonReservedBSAUsage := currentSlotBSAWeight - currentSlotProductBSAWeight                            // 其他产品使用的非预留BSA量
		availableCurrentReservedBSA := mathutil.MaxInt64(0, reservedBSACapacity-currentSlotProductBSAWeight) // 当前产品专用BSA剩余
		availableCurrentNonReservedBSA := mathutil.MaxInt64(0, nonReservedBSACapacity-nonReservedBSAUsage)   // 共享非预留BSA剩余

		// 计算继承BSA的总可用量
		totalAvailableInheritedBSA := int64(0)
		for _, capacity := range availableInheritedBSACapacity {
			totalAvailableInheritedBSA += capacity
		}

		// 当前产品实际可用的BSA总容量 = 专用容量剩余 + 共享容量剩余 + 继承容量剩余
		actualAvailableBSACapacity := availableCurrentReservedBSA + availableCurrentNonReservedBSA + totalAvailableInheritedBSA

		// 6. 业务规则检查：确保订单重量不超过当前产品可用的BSA总容量
		hasSufficientCapacity := rs.checkCapacitySufficient(ctx, ilh, CapacityModeBSA, input.OrderWeight, actualAvailableBSACapacity, slotID)
		if !hasSufficientCapacity {
			continue // 容量不足，尝试下一个ILH
		}

		// 7. 计算详细的容量消耗分布（用于记录和监控）
		// 消耗顺序：继承BSA -> 产品预留BSA -> 非预留BSA
		reservedUsage, nonReservedUsage, inheritedUsage := rs.calculateDetailedBSAUsageDistribution(
			ctx, input.OrderWeight, availableCurrentReservedBSA, availableCurrentNonReservedBSA, availableInheritedBSACapacity,
		)

		// 8. 构建选择结果，记录容量使用情况
		availableILHResults[ilh] = &ILHSelectionResult{
			LineID:                  ilh,
			ReservedBSAUsage:        reservedUsage,
			NonReservedBSAUsage:     nonReservedUsage,
			AdhocUsage:              0, // BSA模式不使用Adhoc容量
			InheritedBSASlotUsage:   inheritedUsage,
			InheritedAdhocSlotUsage: make(map[string]int64), // BSA模式不使用继承的Adhoc容量
			CapacityMode:            CapacityModeBSA,
			SlotID:                  slotID,
		}
	}

	if len(availableILHResults) > 0 {
		selectedILH, result := rs.selectOptimalILHFromResults(
			ctx, availableILHResults, input.ProductID, input.DGType, ilhCapacitySettingMap,
			availableLineInfoMap, input.OrderTime, CapacityModeBSA,
		)
		return selectedILH, true, result, nil
	}

	logger.CtxLogInfof(ctx, "No ILH with sufficient BSA capacity (including inherited) found")
	return "", false, nil, nil
}

// selectILHByAdhocCapacityWithUsage 根据Adhoc容量选择ILH并返回详细的容量使用情况
//
// 业务逻辑说明：
// 1. Adhoc模式：最宽松的容量模式，使用所有可用的容量类型
//   - 当前产品专用预留BSA + 共享非预留BSA + 共享Adhoc + 共享继承BSA + 共享继承Adhoc
//
// 2. 容量类型说明：
//   - Adhoc容量：临时/弹性容量，所有产品共享，用于处理突发需求
//   - BSA容量：业务保障容量，分为产品专用和共享两种
//   - 继承容量：历史时段未使用完的容量，提高整体资源利用率
//
// 3. 容量检查：确保订单重量 <= 当前产品实际可用的所有容量类型总和
// 4. 消耗优先级：继承BSA -> 产品预留BSA -> 非预留BSA -> 继承Adhoc -> 当前Adhoc
func (rs *ILHRoutingServiceImpl) selectILHByAdhocCapacityWithUsage(
	ctx context.Context,
	input ILHSelectionInput,
	ilhLines []string,
	ilhCapacitySettingMap map[string]entity2.ILHCapacitySettingInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
) (string, bool, *ILHSelectionResult, *srerr.Error) {
	logger.CtxLogInfof(ctx, "Selecting ILH by Adhoc capacity, candidates=%v, order_weight=%d", ilhLines, input.OrderWeight)
	availableILHResults := make(map[string]*ILHSelectionResult)

	for _, ilh := range ilhLines {
		// 1. 准备ILH容量配置信息
		capacitySettingInfo, slotID, ok := rs.prepareILHCapacityInfo(ctx, ilh, ilhCapacitySettingMap, input.OrderTime)
		if !ok {
			continue
		}

		// 2. 获取继承时间段的可用容量（包括BSA和Adhoc两种类型）
		availableInheritedBSACapacity, availableInheritedAdhocCapacity, err := rs.getInheritedSlotCapacities(ctx, ilh, capacitySettingInfo, input.DGType)
		if err != nil {
			logger.CtxLogErrorf(ctx, "selectILHByAdhocCapacity: Failed to get inherited capacities for ILH=%s: %v", ilh, err)
			continue // 获取继承容量失败，跳过此ILH，继续尝试其他ILH
		}

		// 3. 获取当前时段的所有容量配置
		capacitySetting := capacitySettingInfo.CapacitySetting
		adhocCapacity := capacitySetting.GetAdhocWeightUnitG()                                      // Adhoc容量（所有产品共享）
		reservedBSACapacity, _ := capacitySetting.GetProductReservedBSAWeightUnitG(input.ProductID) // 当前产品专用BSA容量
		nonReservedBSACapacity := capacitySetting.GetNonReservedBSAWeightUnitG()                    // 非预留BSA容量（所有产品共享）

		// 4. 获取当前时段的实际使用量（用于计算各类容量的剩余量）
		// 获取当前时段Adhoc使用量（所有产品的总使用量）
		currentSlotAdhocWeight, err := rs.ilhWeightCounter.GetILHAdhocWeight(ctx, ilh, input.DGType, capacitySettingInfo.TWS, capacitySettingInfo.DestPorts, slotID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetILHAdhocWeight error|ilh=%s|err=%v", ilh, err)
			continue // API调用失败，跳过此ILH，避免阻塞整个选择流程
		}

		// 获取当前时段BSA总使用量（所有产品的总使用量）
		currentSlotBSAWeight, err := rs.ilhWeightCounter.GetILHBSAWeight(ctx, ilh, input.DGType, capacitySettingInfo.TWS, capacitySettingInfo.DestPorts, slotID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetILHBSAWeight error|ilh=%s|err=%v", ilh, err)
			continue // API调用失败，跳过此ILH
		}

		// 获取当前产品的BSA使用量（只包含该产品的使用量）
		currentSlotProductBSAWeight, err := rs.ilhWeightCounter.GetILHProductBSAWeight(ctx, input.ProductID, ilh, input.DGType, capacitySettingInfo.TWS, capacitySettingInfo.DestPorts, slotID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "GetILHProductBSAWeight error|ilh=%s|err=%v", ilh, err)
			continue // API调用失败，跳过此ILH
		}

		// 5. 计算当前产品在Adhoc模式下的实际可用容量
		// 业务逻辑：计算各类容量的剩余量，确保不会出现负数，然后汇总所有可用容量
		nonReservedBSAUsage := currentSlotBSAWeight - currentSlotProductBSAWeight                            // 其他产品使用的非预留BSA量
		availableCurrentAdhoc := mathutil.MaxInt64(0, adhocCapacity-currentSlotAdhocWeight)                  // Adhoc容量剩余
		availableCurrentReservedBSA := mathutil.MaxInt64(0, reservedBSACapacity-currentSlotProductBSAWeight) // 产品专用BSA剩余
		availableCurrentNonReservedBSA := mathutil.MaxInt64(0, nonReservedBSACapacity-nonReservedBSAUsage)   // 共享非预留BSA剩余

		// 计算继承容量的总可用量
		totalAvailableInheritedBSA := int64(0)
		for _, capacity := range availableInheritedBSACapacity {
			totalAvailableInheritedBSA += capacity
		}

		totalAvailableInheritedAdhoc := int64(0)
		for _, capacity := range availableInheritedAdhocCapacity {
			totalAvailableInheritedAdhoc += capacity
		}

		// 当前产品实际可用的总容量 = 所有类型容量的剩余量之和
		actualAvailableCapacity := availableCurrentReservedBSA + availableCurrentNonReservedBSA + availableCurrentAdhoc + totalAvailableInheritedBSA + totalAvailableInheritedAdhoc

		// 6. 业务规则检查：确保订单重量不超过当前产品可用的总容量
		hasSufficientCapacity := rs.checkCapacitySufficient(ctx, ilh, CapacityModeAdhoc, input.OrderWeight, actualAvailableCapacity, slotID)
		if !hasSufficientCapacity {
			continue // 容量不足，尝试下一个ILH
		}

		// 7. 计算详细的容量消耗分布（用于记录和监控）
		// 消耗顺序：继承BSA -> 产品预留BSA -> 非预留BSA -> 继承Adhoc -> 当前Adhoc
		adhocUsage, reservedUsage, nonReservedUsage, inheritedAdhocUsage, inheritedBSAUsage := rs.calculateDetailedAdhocUsageDistribution(
			ctx, input.OrderWeight, availableCurrentAdhoc, availableCurrentReservedBSA, availableCurrentNonReservedBSA,
			availableInheritedAdhocCapacity, availableInheritedBSACapacity,
		)

		// 8. 构建选择结果，记录详细的容量使用情况
		availableILHResults[ilh] = &ILHSelectionResult{
			LineID:                  ilh,
			ReservedBSAUsage:        reservedUsage,
			NonReservedBSAUsage:     nonReservedUsage,
			AdhocUsage:              adhocUsage,
			InheritedBSASlotUsage:   inheritedBSAUsage,   // 记录继承BSA的使用分布
			InheritedAdhocSlotUsage: inheritedAdhocUsage, // 记录继承Adhoc的使用分布
			CapacityMode:            CapacityModeAdhoc,
			SlotID:                  slotID,
		}
	}

	if len(availableILHResults) > 0 {
		selectedILH, result := rs.selectOptimalILHFromResults(
			ctx, availableILHResults, input.ProductID, input.DGType, ilhCapacitySettingMap,
			availableLineInfoMap, input.OrderTime, CapacityModeAdhoc,
		)
		return selectedILH, true, result, nil
	}

	logger.CtxLogInfof(ctx, "No ILH with sufficient Adhoc capacity (including fallback and inherited) found")
	return "", false, nil, nil
}

// getInheritedSlotCapacities 获取所有继承时间段的可用BSA和Adhoc容量明细
func (rs *ILHRoutingServiceImpl) getInheritedSlotCapacities(ctx context.Context, ilh string, ilhCapacitySetting entity2.ILHCapacitySettingInfo, dgType int) (map[string]int64, map[string]int64, *srerr.Error) {
	availableInheritedBSACapacity := make(map[string]int64)
	availableInheritedAdhocCapacity := make(map[string]int64)

	if ilhCapacitySetting.CapacitySetting.TimeIntervalType != entity2.TimeIntervalTypeDefault || len(ilhCapacitySetting.InheritanceTimeSlotCapacity) == 0 {
		return availableInheritedBSACapacity, availableInheritedAdhocCapacity, nil // 不是默认时间间隔类型或没有继承时段，则直接返回空map
	}

	// 遍历继承的时间段容量列表
	for _, timeSlotCapacity := range ilhCapacitySetting.InheritanceTimeSlotCapacity {
		slotID := timeSlotCapacity.SlotID

		// 获取继承时段的BSA使用量
		bsaWeight, err := rs.ilhWeightCounter.GetILHBSAWeight(ctx, ilh, dgType, ilhCapacitySetting.TWS, ilhCapacitySetting.DestPorts, slotID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "getInheritedSlotCapacities: Failed to get BSA weight for ILH=%s, slotID=%s: %v", ilh, slotID, err)
			continue
		}

		// 计算继承时段的剩余BSA容量
		remainBSACapacity := timeSlotCapacity.BSACapacity - bsaWeight
		if remainBSACapacity > 0 {
			availableInheritedBSACapacity[slotID] = remainBSACapacity
		}

		// 获取继承时段的Adhoc使用量
		adhocWeight, err := rs.ilhWeightCounter.GetILHAdhocWeight(ctx, ilh, dgType, ilhCapacitySetting.TWS, ilhCapacitySetting.DestPorts, slotID)
		if err != nil {
			logger.CtxLogErrorf(ctx, "getInheritedSlotCapacities: Failed to get Adhoc weight for ILH=%s, slotID=%s: %v", ilh, slotID, err)
			continue
		}

		// 计算继承时段的剩余Adhoc容量
		remainAdhocCapacity := timeSlotCapacity.AdhocCapacity - adhocWeight
		if remainAdhocCapacity > 0 {
			availableInheritedAdhocCapacity[slotID] = remainAdhocCapacity
		}
	}

	return availableInheritedBSACapacity, availableInheritedAdhocCapacity, nil
}

// calculateDetailedBSAUsageDistribution 计算BSA模式下的详细容量消耗分布 (包括继承时段)
// 消耗顺序: 继承时段BSA (按SlotID升序) -> 当前时段产品预留BSA -> 当前时段非预留BSA
func (rs *ILHRoutingServiceImpl) calculateDetailedBSAUsageDistribution(
	ctx context.Context,
	orderWeight int64,
	availableReservedBSA int64,
	availableNonReservedBSA int64,
	availableInheritedBSACapacity map[string]int64,
) (int64, int64, map[string]int64) {

	reservedUsage := int64(0)
	nonReservedUsage := int64(0)
	inheritedUsage := make(map[string]int64)
	remainingWeight := orderWeight

	// 1. 消耗继承时段的BSA
	if remainingWeight > 0 && len(availableInheritedBSACapacity) > 0 {
		// 对继承时段按SlotID排序，确保消耗顺序一致性
		inheritedSlotIDs := make([]string, 0, len(availableInheritedBSACapacity))
		for slotID := range availableInheritedBSACapacity {
			inheritedSlotIDs = append(inheritedSlotIDs, slotID)
		}
		sort.Strings(inheritedSlotIDs) // 按Slot ID升序排序

		for _, slotID := range inheritedSlotIDs {
			if remainingWeight <= 0 {
				break
			}
			availableCapacity := availableInheritedBSACapacity[slotID]
			if availableCapacity > 0 {
				consume := mathutil.MinInt64(remainingWeight, availableCapacity)
				inheritedUsage[slotID] = consume
				remainingWeight -= consume
			}
		}
	}

	// 2. 消耗当前时段产品预留BSA
	if remainingWeight > 0 && availableReservedBSA > 0 {
		consume := mathutil.MinInt64(remainingWeight, availableReservedBSA)
		reservedUsage += consume
		remainingWeight -= consume
	}

	// 3. 消耗当前时段非预留BSA
	if remainingWeight > 0 && availableNonReservedBSA > 0 {
		consume := mathutil.MinInt64(remainingWeight, availableNonReservedBSA)
		nonReservedUsage += consume
		remainingWeight -= consume
	}

	if remainingWeight > 0 {
		// 这理论上不应该发生，因为调用此函数前已经检查过总容量足够
		// 但作为防御性编程，记录一个警告
		logger.CtxLogErrorf(ctx, "calculateDetailedBSAUsageDistribution: Remaining weight %d after distributing usage, orderWeight=%d", remainingWeight, orderWeight)
	}

	return reservedUsage, nonReservedUsage, inheritedUsage
}

// calculateDetailedAdhocUsageDistribution 计算Adhoc模式下的详细容量消耗分布 (包括继承时段)
// 消耗顺序: 继承时段BSA (SlotID升序) -> 当前时段产品预留BSA -> 当前时段非预留BSA -> 继承时段Adhoc (SlotID升序) -> 当前时段Adhoc
func (rs *ILHRoutingServiceImpl) calculateDetailedAdhocUsageDistribution(
	ctx context.Context,
	orderWeight int64,
	availableAdhoc int64,
	availableReservedBSA int64,
	availableNonReservedBSA int64,
	availableInheritedAdhocCapacity map[string]int64,
	availableInheritedBSACapacity map[string]int64,
) (int64, int64, int64, map[string]int64, map[string]int64) {

	adhocUsage := int64(0)
	reservedUsage := int64(0)
	nonReservedUsage := int64(0)
	inheritedAdhocUsage := make(map[string]int64)
	inheritedBSAUsage := make(map[string]int64)
	remainingWeight := orderWeight

	// 1. 消耗继承时段的BSA
	if remainingWeight > 0 && len(availableInheritedBSACapacity) > 0 {
		inheritedSlotIDs := make([]string, 0, len(availableInheritedBSACapacity))
		for slotID := range availableInheritedBSACapacity {
			inheritedSlotIDs = append(inheritedSlotIDs, slotID)
		}
		sort.Strings(inheritedSlotIDs)

		for _, slotID := range inheritedSlotIDs {
			if remainingWeight <= 0 {
				break
			}
			availableCapacity := availableInheritedBSACapacity[slotID]
			if availableCapacity > 0 {
				consume := mathutil.MinInt64(remainingWeight, availableCapacity)
				inheritedBSAUsage[slotID] = consume
				remainingWeight -= consume
			}
		}
	}

	// 2. 消耗当前时段产品预留BSA
	if remainingWeight > 0 && availableReservedBSA > 0 {
		consume := mathutil.MinInt64(remainingWeight, availableReservedBSA)
		reservedUsage += consume
		remainingWeight -= consume
	}

	// 3. 消耗当前时段非预留BSA
	if remainingWeight > 0 && availableNonReservedBSA > 0 {
		consume := mathutil.MinInt64(remainingWeight, availableNonReservedBSA)
		nonReservedUsage += consume
		remainingWeight -= consume
	}

	// 4. 消耗继承时段的Adhoc
	if remainingWeight > 0 && len(availableInheritedAdhocCapacity) > 0 {
		inheritedSlotIDs := make([]string, 0, len(availableInheritedAdhocCapacity))
		for slotID := range availableInheritedAdhocCapacity {
			inheritedSlotIDs = append(inheritedSlotIDs, slotID)
		}
		sort.Strings(inheritedSlotIDs)

		for _, slotID := range inheritedSlotIDs {
			if remainingWeight <= 0 {
				break
			}
			availableCapacity := availableInheritedAdhocCapacity[slotID]
			if availableCapacity > 0 {
				consume := mathutil.MinInt64(remainingWeight, availableCapacity)
				inheritedAdhocUsage[slotID] = consume
				remainingWeight -= consume
			}
		}
	}

	// 5. 消耗当前时段Adhoc
	if remainingWeight > 0 && availableAdhoc > 0 {
		consume := mathutil.MinInt64(remainingWeight, availableAdhoc)
		adhocUsage += consume
		remainingWeight -= consume
	}

	if remainingWeight > 0 {
		// 防御性编程：记录警告
		logger.CtxLogErrorf(ctx, "calculateDetailedAdhocUsageDistribution: Remaining weight %d after distributing usage, orderWeight=%d", remainingWeight, orderWeight)
	}

	return adhocUsage, reservedUsage, nonReservedUsage, inheritedAdhocUsage, inheritedBSAUsage
}

// selectCCAndFilterLanes 选择CC线路并过滤可用Lane
func (rs *ILHRoutingServiceImpl) selectCCAndFilterLanes(
	ctx context.Context,
	productID int,
	packageNo string,
	availableLanes []*rule.RoutingLaneInfo,
	availableLineInfoMap map[string]entity.AvailableLineBaseInfo,
	ilhLineID string,
) (*rule.RoutingLaneInfo, *srerr.Error) {
	// 1. 选择CC线路
	ccLineID, err := rs.selectCCLine(ctx, productID, availableLanes, availableLineInfoMap)
	if err != nil {
		logger.CtxLogErrorf(ctx, "Error selecting CC line for packageNo=%s after ILH %s: %v", packageNo, ilhLineID, err)
		return nil, err
	}

	if ccLineID == "" { // selectCCLine内部应该已经处理了找不到的情况并返回错误，这里是防御性检查
		logger.CtxLogErrorf(ctx, "No available CC line ID returned for packageNo=%s after ILH %s", packageNo, ilhLineID)
		return nil, srerr.New(srerr.DataErr, nil, "no available CC lines found")
	}
	logger.CtxLogInfof(ctx, "Selected CC line %s for packageNo=%s", ccLineID, packageNo)

	// 2. 按选中的CC线路过滤可用Lane
	resultLanes := filterLanesByResourceSubType(availableLanes, ccLineID, lfslib.CCLine)
	if len(resultLanes) == 0 {
		logger.CtxLogErrorf(ctx, "No available lanes found after filtering by selected CC %s (ILH %s) for packageNo=%s",
			ccLineID, ilhLineID, packageNo)
		return nil, srerr.New(srerr.DataErr, nil, "no available lanes after CC filtering")
	}

	// 通常CC过滤后应该只剩一个Lane，但以防万一取第一个
	finalLane := resultLanes[0]
	logger.CtxLogInfof(ctx, "Final selected lane %s for packageNo=%s (ILH: %s, CC: %s)",
		finalLane.LaneCode, packageNo, ilhLineID, ccLineID)

	return finalLane, nil
}
