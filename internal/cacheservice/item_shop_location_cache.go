package cacheservice

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"git.garena.com/shopee/common/ulog"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_preview/common"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_preview/internal/config"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_preview/internal/logger"
	fpigsPb "git.garena.com/shopee/marketplace-logistics/fulfilment_preview/internal/proto/spex/gen/go/marketplace_logistics_prefulfilment_fulfilment_planning.pb"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_preview/modules/fulfilment_preview/sbsdm"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_preview/modules/httpapi/oms"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_preview/modules/sbsutils"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_preview/modules/spex/account"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_preview/modules/spex/orderFulfilment"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_preview/modules/spex/seller"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_preview/modules/spex/shopinfo"
	"git.garena.com/shopee/marketplace-logistics/fulfilment_preview/modules/translator"
	"git.garena.com/shopee/mts/go-application-server/spi/cache"
)

type ItemShopLocationCache interface {
	GetBuyerDeliveryAddress(ctx context.Context, buyerId int64) (*account.BuyerDefaultDeliveryAddress, error)
	GetShopPFFFeature(ctx context.Context, items []*sbsdm.ShopLocationItem) (map[int64]bool, error)
	GetOMSWarehousePriority(ctx context.Context, userAddress *account.BuyerDefaultDeliveryAddress, mapShopIdToIsPFFShop map[int64]bool, items []*sbsdm.ShopLocationItem) ([]*sbsdm.ShopLocationItem, error)
	GetSellerWHPriority(ctx context.Context, buyerAddress *account.BuyerDefaultDeliveryAddress, items []*sbsdm.ShopLocationItem) (map[int64]map[string]int32, map[int64]map[string]uint32, error)
	GetShop3PFMWHFlag(ctx context.Context, shopIds []int64) (map[int64]bool, error)
	GetShopByMultiWarehouseFeature(ctx context.Context, shopIds []int64) (map[int64]bool, error)
	GetSellerWHFullAddressByAddressId(ctx context.Context, items []*sbsdm.ShopLocationItem) (map[uint32]common.LocalisedContentTranslation, error)
	GetSellerDefaultPickupAddress(ctx context.Context, items []*sbsdm.ShopLocationItem) (map[int64]common.LocalisedContentTranslation, error)
	GetSellerOverseaShopLocation(ctx context.Context, items []*sbsdm.ShopLocationItem) (map[int64]common.LocalisedContentTranslation, error)
	GetShopeeWHLocation(ctx context.Context, items []*sbsdm.ShopLocationItem) (map[string]common.LocalisedContentTranslation, map[string]int64, error)
}

//nolint:unused // field `configAccessor` is unused but required in IoC
type ItemShopLocationCacheImpl struct {
	configAccessor *config.Accessor      `inject:"config_accessor"`
	AccountApi     account.AccountApi    `inject:"account_api"`
	SellerApi      seller.SellerApi      `inject:"seller_api"`
	OmsApi         oms.OMSApi            `inject:"oms_api"`
	ShopApi        shopinfo.ShopInfoApi  `inject:"shop_api"`
	OFApi          orderFulfilment.OFApi `inject:"of_api"`
	CacheHandler   cache.Cache           `inject:"multilayer_cache"`
	MapAPI         MapCache              `inject:"map_cache"`
}

func (cacheImpl *ItemShopLocationCacheImpl) GetShopPFFFeature(ctx context.Context, items []*sbsdm.ShopLocationItem) (map[int64]bool, error) {
	logCtx := logger.WithCtx(ctx)
	allShopIds := GetItemShopIds(items)
	mapAllShopIdToIsPFFShop := make(map[int64]bool)

	skipPFFCheck := config.SkipThisRegion()
	if skipPFFCheck {
		for _, item := range items {
			mapAllShopIdToIsPFFShop[item.GetShopInfo().GetShopId()] = false
		}
		return mapAllShopIdToIsPFFShop, nil
	}

	if config.GetUpgradeToBetterCache(ctx) {
		res, err := sbsutils.CacheLoadManyFromMap[int64, *common.ShopIsPFFShopCache](
			ctx,
			cacheImpl.CacheHandler,
			allShopIds,
			CacheShopIdWhitelistedKey,
			func(ctx context.Context, shopIds []int64) (map[int64]*common.ShopIsPFFShopCache, error) {
				toReturn := make(map[int64]*common.ShopIsPFFShopCache)
				res, err := cacheImpl.SellerApi.MapShopIDToIsPFFShopStatus(ctx, shopIds)
				if err != nil {
					return nil, err
				}
				for shopId, isPff := range res {
					toReturn[shopId] = &common.ShopIsPFFShopCache{
						IsPFFShop: isPff,
					}
				}
				return toReturn, nil
			},
			config.GetShopIsPFFShopFeatureCacheTime(),
		)

		if err != nil {
			return nil, err
		}
		shopIdToIsPFFShop := make(map[int64]bool)
		for shopId, cacheItem := range res {
			shopIdToIsPFFShop[shopId] = cacheItem.IsPFFShop
		}
		return shopIdToIsPFFShop, nil
	}

	listNotCacheShopIds := make([]int64, 0, len(items))
	for _, shopId := range allShopIds {
		cacheKey := CacheShopIdWhitelistedKey(shopId)
		cacheValue := &common.ShopIsPFFShopCache{}
		err := cacheImpl.CacheHandler.Get(ctx, cacheKey, cacheValue)
		if err == nil {
			logCtx.Info("get_shop_pff_feature_cache.success")
			mapAllShopIdToIsPFFShop[shopId] = cacheValue.IsPFFShop
		} else {
			logCtx.Info("get_shop_pff_feature_cache.failed")
			listNotCacheShopIds = append(listNotCacheShopIds, shopId)
			LogCacheError(ctx, cacheKey, err)
		}
	}

	if len(listNotCacheShopIds) > 0 {
		mapShopIdToIsPFFShop, err := cacheImpl.SellerApi.MapShopIDToIsPFFShopStatus(ctx, listNotCacheShopIds)
		if err != nil {
			return nil, err
		}

		for shopId, isPFFShop := range mapShopIdToIsPFFShop {
			mapAllShopIdToIsPFFShop[shopId] = isPFFShop

			cacheKey := CacheShopIdWhitelistedKey(shopId)
			cacheValue := &common.ShopIsPFFShopCache{IsPFFShop: isPFFShop}
			cacheTime := config.GetShopIsPFFShopFeatureCacheTime()
			err = cacheImpl.CacheHandler.Set(ctx, cacheKey, cacheValue, cacheTime)
			if err != nil {
				logger.WithCtx(ctx).Withs("shop_id", shopId, "isPFFShop", isPFFShop).Error("cache_shop_multi_wh_feature.error")
			} else {
				logger.WithCtx(ctx).Withs("shopId", shopId, "isPFFShop", isPFFShop, "cache_time", cacheTime).Info("cache_shop_multi_wh_feature.success")
			}
		}
	}
	logCtx.Withs("mapAllShopIdToIsPFFShop", mapAllShopIdToIsPFFShop).Info("cache.get_shop_pff_feature")
	return mapAllShopIdToIsPFFShop, nil
}

func (cacheImpl *ItemShopLocationCacheImpl) GetBuyerDeliveryAddress(ctx context.Context, buyerId int64) (*account.BuyerDefaultDeliveryAddress, error) {
	logCtx := logger.WithCtx(ctx)
	if buyerId == 0 {
		return nil, nil
	}

	if config.GetUpgradeToBetterCache(ctx) {
		res, err := sbsutils.CacheLoad[int64, *account.BuyerDefaultDeliveryAddress](
			ctx,
			cacheImpl.CacheHandler,
			buyerId,
			CacheBuyerDeliveryAddressKey,
			cacheImpl.AccountApi.GetBuyerDefaultDeliveryAddress,
			config.GetBuyerDeliveryAddressCacheTime(),
		)
		if err != nil {
			return nil, err
		}
		return res, nil
	}

	cacheKey := CacheBuyerDeliveryAddressKey(buyerId)
	cacheValue := &account.BuyerDefaultDeliveryAddress{}
	err := cacheImpl.CacheHandler.Get(ctx, cacheKey, cacheValue)
	if err == nil {
		logCtx.Withs("buyerId", buyerId, "address", cacheValue).Info("get_buyer_delivery_address.success")
		if cacheValue.GetUserId() == 0 {
			return nil, nil
		}
		return cacheValue, nil
	} else {
		logCtx.Withs("buyerId", buyerId).Info("get_buyer_delivery_address.failed")
		LogCacheError(ctx, cacheKey, err)
	}

	userDeliveryAddress, err := cacheImpl.AccountApi.GetBuyerDefaultDeliveryAddress(ctx, buyerId)
	if err != nil {
		return nil, err
	}
	cacheTime := config.GetBuyerDeliveryAddressCacheTime()
	err = cacheImpl.CacheHandler.Set(ctx, cacheKey, userDeliveryAddress, cacheTime)
	if err != nil {
		logger.WithCtx(ctx).Withs("buyer_id", buyerId, "cache_time", cacheTime).Error("cache_buyer_delivery_address.error")
	} else {
		logger.WithCtx(ctx).Withs("buyer_id", buyerId, "cache_time", cacheTime).Info("cache_buyer_delivery_address.success")
	}

	return userDeliveryAddress, nil
}

func getWHCodeToLocation(whCodes []string, primaryLanguage string, secondaryLanguage string) map[string]common.LocalisedContentTranslation {
	mapAllWHCodeToLocation := make(map[string]common.LocalisedContentTranslation)

	for _, whCode := range whCodes {
		if _, ok := mapAllWHCodeToLocation[whCode]; !ok {
			translatedContent := common.LocalisedContentTranslation{}
			translatedContent.SetPrimaryContent(primaryLanguage, "")
			translatedContent.SetBuyerLocalisedContent(primaryLanguage, secondaryLanguage, "")
			mapAllWHCodeToLocation[whCode] = translatedContent
		}
	}

	return mapAllWHCodeToLocation
}

func (cacheImpl *ItemShopLocationCacheImpl) GetShopeeWHLocation(ctx context.Context, items []*sbsdm.ShopLocationItem) (map[string]common.LocalisedContentTranslation, map[string]int64, error) {
	// Not implementing improved caching for this, see TD

	logCtx := logger.WithCtx(ctx)
	whCodes := GetShopeeWHIdFromItems(items)
	userLang := sbsutils.GetLang(ctx)

	languageConfigs := config.GetLangConfig()
	regionPrimaryLanguage := languageConfigs.GetRegionPrimaryLanguage(config.Region())

	sameLang := strings.EqualFold(regionPrimaryLanguage.Language, userLang)

	listNotCachedWHCodes := make([]string, 0, len(whCodes))
	mapAllWHCodeToLocation := getWHCodeToLocation(whCodes, regionPrimaryLanguage.Language, userLang)
	mapAllWHCodeToAddressId := make(map[string]int64)
	// track raw WarehouseLocation structs for TTL refresh after translation
	mapAllRaw := make(map[string]*sbsdm.WarehouseLocation)

	mapNeedTranslation := make(map[string]string)

	for _, whCode := range whCodes {
		cacheKey := CacheWarehouseLocationKey(whCode)
		cacheValue := &sbsdm.WarehouseLocation{}
		err := cacheImpl.CacheHandler.Get(ctx, cacheKey, cacheValue)
		if err == nil {
			// record raw struct
			mapAllRaw[whCode] = cacheValue
			logCtx.Withs("whCode", whCode, "cache_value", cacheValue).Info("GetShopeeWHLocation|get_warehouse_location_cache.success")

			whLocationPrimary, whLocationSecondary := formatWarehouseLocation(ctx, cacheValue, regionPrimaryLanguage.Language, userLang)

			translatedContent := common.LocalisedContentTranslation{}
			translatedContent.SetBuyerLocalisedContent(regionPrimaryLanguage.Language, userLang, whLocationSecondary)
			translatedContent.SetPrimaryContent(regionPrimaryLanguage.Language, whLocationPrimary)
			mapAllWHCodeToLocation[whCode] = translatedContent

			mapAllWHCodeToAddressId[whCode] = cacheValue.GetAddressId()
			if whLocationSecondary == "" && whLocationPrimary != "" {
				mapNeedTranslation[whCode] = whLocationPrimary
			}

		} else {
			logCtx.Withs("whCode", whCode).Info("GetShopeeWHLocation|get_warehouse_location_cache.failed")
			LogCacheError(ctx, cacheKey, err)
			listNotCachedWHCodes = append(listNotCachedWHCodes, whCode)
		}
	}

	listNotCachedWHCodes = deleteDuplicatedWHId(listNotCachedWHCodes)
	if len(listNotCachedWHCodes) != 0 {
		logCtx.Info("GetShopeeWHLocation|shopee_wh_query.continue")
		whList, err := cacheImpl.AccountApi.GetWarehouseList(ctx, listNotCachedWHCodes, "")
		if err != nil {
			return nil, nil, err
		}

		for _, wh := range whList {
			whId := wh.GetCode()
			location := wh.GetLocation()

			whLocation := &sbsdm.WarehouseLocation{
				WarehouseId: whId,
				Country:     location.GetCountry(),
				State:       location.GetState(),
				City:        location.GetCity(),
				Town:        location.GetTown(),
				District:    location.GetDistrict(),
				AddressId:   wh.GetId(),
			}
			// record raw struct from fallback
			mapAllRaw[whId] = whLocation

			whLocationPrimary, whLocationSecondary := formatWarehouseLocation(ctx, whLocation, regionPrimaryLanguage.Language, userLang)

			translatedContent := common.LocalisedContentTranslation{}
			translatedContent.SetBuyerLocalisedContent(regionPrimaryLanguage.Language, userLang, whLocationSecondary)
			translatedContent.SetPrimaryContent(regionPrimaryLanguage.Language, whLocationPrimary)
			mapAllWHCodeToLocation[whId] = translatedContent

			mapAllWHCodeToAddressId[whId] = wh.GetId()
			cacheKey := CacheWarehouseLocationKey(whId)
			cacheTime := config.GetWarehouseFullAddressCacheTime()
			err = cacheImpl.CacheHandler.Set(ctx, cacheKey, whLocation, cacheTime)

			if whLocationSecondary == "" && whLocationPrimary != "" {
				mapNeedTranslation[whId] = whLocationPrimary
			}

			if err != nil {
				logger.WithCtx(ctx).Withs("whId", whId, "whLocation", whLocation, "cache_time", cacheTime).Error("GetShopeeWHLocation|cache_warehouse_full_address.error")
			} else {
				logger.WithCtx(ctx).Withs("whId", whId, "whLocation", whLocation, "cache_time", cacheTime).Info("GetShopeeWHLocation|cache_warehouse_full_address.success")
			}
		}
	}

	// Should not need to call this as all WHLocation are handled by transify key. Just in case there is edge case.
	whLocations := common.GetValues(mapNeedTranslation)
	translatedLocations, err := cacheImpl.MapAPI.DivisonAutocomplete(ctx, whLocations, userLang)
	if err == nil {
		for whCode, whLocTranslated := range mapAllWHCodeToLocation {
			whLoc := whLocTranslated.GetPrimaryLocalisedContent()
			if translated, ok := translatedLocations[whLoc]; ok && !sameLang {
				whLocTranslated.SetBuyerLocalisedContent(regionPrimaryLanguage.Language, userLang, translated)
				mapAllWHCodeToLocation[whCode] = whLocTranslated
			}
			// refresh TTL of the raw WarehouseLocation struct in cache
			if raw, found := mapAllRaw[whCode]; found {
				cacheKey := CacheWarehouseLocationKey(whCode)
				cacheTime := config.GetWarehouseFullAddressCacheTime()
				if err2 := cacheImpl.CacheHandler.Set(ctx, cacheKey, raw, cacheTime); err2 != nil {
					logger.WithCtx(ctx).Withs("whCode", whCode, "cache_time", cacheTime).
						Error("GetShopeeWHLocation|cache_warehouse_full_address_translation.error")
				} else {
					logger.WithCtx(ctx).Withs("whCode", whCode, "cache_time", cacheTime).
						Info("GetShopeeWHLocation|cache_warehouse_full_address_translation.success")
				}
			}

		}
	} else {
		logCtx.Error("GetShopeeWHLocation|get_location_translation.error", ulog.Error(err))
	}

	logCtx.Withs("listNotCachedWHCodes", listNotCachedWHCodes, "mapAllWHCodeToLocation", mapAllWHCodeToLocation,
		"mapAllWHCodeToAddressId", mapAllWHCodeToAddressId).Info("cache.get_warehouse_full_address")
	return mapAllWHCodeToLocation, mapAllWHCodeToAddressId, nil
}

type OMSResultsStruct struct {
	OMSShopID  int64
	WHPriority map[string]int32
}

func (cacheImpl *ItemShopLocationCacheImpl) GetOMSWarehousePriority(ctx context.Context,
	userAddress *account.BuyerDefaultDeliveryAddress, mapShopIdToIsPFFShop map[int64]bool, items []*sbsdm.ShopLocationItem) ([]*sbsdm.ShopLocationItem, error) {
	logCtx := logger.WithCtx(ctx)
	if len(items) == 0 || userAddress == nil {
		logCtx.Withs("item_ids", GetItemIdByItems(items)).Info("GetOMSWarehousePriority|get_oms_wh_priority.empty_user_address_or_items")
		return items, nil
	}
	country := userAddress.Country
	state := userAddress.State
	city := userAddress.City
	district := userAddress.District
	if country == "" && state == "" && city == "" {
		logCtx.Withs("item_ids", GetItemIdByItems(items)).Info("GetOMSWarehousePriority|get_oms_wh_priority.empty_buyer_country_state_city")
		return items, nil
	}

	if config.GetUpgradeToBetterCache(ctx) {
		// Determine the OMS shop ID for each item and record unique OMS shop IDs.
		itemOMSShopIDs := make([]int64, len(items))
		omsShopIDSet := make(map[int64]struct{})
		for i, item := range items {
			shopID := item.GetShopInfo().ShopId
			var omsShopID int64
			if mapShopIdToIsPFFShop[shopID] {
				omsShopID = shopID
			} else {
				omsShopID = dummyShopOMSID
			}
			itemOMSShopIDs[i] = omsShopID
			omsShopIDSet[omsShopID] = struct{}{}
		}
		uniqueOMSShopIDs := make([]int64, 0, len(omsShopIDSet))
		for id := range omsShopIDSet {
			uniqueOMSShopIDs = append(uniqueOMSShopIDs, id)
		}

		keyConverter := func(omsShopID int64) string {
			return CacheAllWarehousePrioritiesKeyV2(country, state, city, district, omsShopID)
		}

		res, err := sbsutils.CacheLoadManyFromMap[int64, *oms.CacheAllWHPriorities](
			ctx,
			cacheImpl.CacheHandler,
			uniqueOMSShopIDs,
			keyConverter,
			func(ctx context.Context, omsShopIDs []int64) (map[int64]*oms.CacheAllWHPriorities, error) {
				var (
					wg          sync.WaitGroup
					resultsChan = make(chan OMSResultsStruct, len(omsShopIDs))
				)
				for _, omsShopID := range omsShopIDs {
					wg.Add(1)
					go func(omsShopID int64) {
						defer wg.Done()
						priorities, err := cacheImpl.OmsApi.GetOMSWarehousePriorityV2(ctx, omsShopID, nil, userAddress)
						if err != nil {
							logCtx.Withs("omsShopID", omsShopID, "error", err).
								Error("GetOMSWarehousePriority|OMsApi call failed")
							return
						}
						resultsChan <- OMSResultsStruct{
							OMSShopID:  omsShopID,
							WHPriority: priorities,
						}
					}(omsShopID)
				}
				wg.Wait()
				close(resultsChan)

				resultMap := make(map[int64]*oms.CacheAllWHPriorities, len(omsShopIDs))
				for r := range resultsChan {
					resultMap[r.OMSShopID] = &oms.CacheAllWHPriorities{
						WhIDToPriority: r.WHPriority,
					}
				}
				return resultMap, nil
			},
			config.GetOMSWarehousePriorityCacheTime(),
		)
		if err != nil {
			return nil, err
		}

		for i, item := range items {
			if item == nil {
				continue
			}
			omsShopID := itemOMSShopIDs[i]
			var shopLevelWHToPriority map[string]int32
			if cached, ok := res[omsShopID]; ok {
				shopLevelWHToPriority = cached.WhIDToPriority
			}
			for m, model := range item.GetModels() {
				for s, stock := range model.GetStockBreakdowns() {
					items[i].Models[m].StockBreakDowns[s].Priority = shopLevelWHToPriority[stock.GetWHId()]
				}
			}
			logCtx.Withs("item_id", item.GetItemId(), "whToPriority", shopLevelWHToPriority).
				Info("GetOMSWarehousePriority|upgraded branch")
		}
		return items, nil
	}

	for itemSeq, item := range items {
		var omsShopID int64
		shopID := item.GetShopInfo().ShopId
		if mapShopIdToIsPFFShop[shopID] {
			omsShopID = shopID
		} else {
			omsShopID = dummyShopOMSID
		}
		var shopLevelWHToPriority map[string]int32
		cacheKey := CacheAllWarehousePrioritiesKeyV2(country, state, city, district, omsShopID)
		cacheValue := &oms.CacheAllWHPriorities{}
		err := cacheImpl.CacheHandler.Get(ctx, cacheKey, cacheValue)
		if err == nil {
			logCtx.Withs("cache_value", cacheValue).Info("GetOMSWarehousePriority|get_all_warehouse_priorities_cache.success")
			shopLevelWHToPriority = cacheValue.WhIDToPriority
		} else {
			WHIdToPriority, apiErr := cacheImpl.OmsApi.GetOMSWarehousePriorityV2(ctx, omsShopID, nil, userAddress)
			if apiErr != nil {
				return nil, apiErr
			}
			cacheKey := CacheAllWarehousePrioritiesKeyV2(country, state, city, district, omsShopID)
			cacheTime := config.GetOMSWarehousePriorityCacheTime()
			err = cacheImpl.CacheHandler.Set(ctx, cacheKey, &oms.CacheAllWHPriorities{WhIDToPriority: WHIdToPriority}, cacheTime)
			if err != nil {
				logCtx.Withs("cache_time", cacheTime).WithTypedFields(ulog.Error(err)).Error("GetOMSWarehousePriority|cache_wh_priorities.error")
			} else {
				logCtx.Withs("cache_time", cacheTime).Info("GetOMSWarehousePriority|cache_wh_priority.success")
			}
			shopLevelWHToPriority = WHIdToPriority
		}

		for modelSeq, model := range item.GetModels() {
			for stockSeq, stock := range model.GetStockBreakdowns() {
				items[itemSeq].Models[modelSeq].StockBreakDowns[stockSeq].Priority = shopLevelWHToPriority[stock.GetWHId()]
			}
		}

		logCtx.Withs("item_ids", GetItemIdByItems(items), "whToPriority", shopLevelWHToPriority).Info("cache.get_warehouse_priority")
	}

	return items, nil
}

func getMapAllShopIdToShopLocation(items []*sbsdm.ShopLocationItem, primaryLanguage string, secondaryLanguage string) map[int64]common.LocalisedContentTranslation {
	mapAllShopIdToShopLocation := make(map[int64]common.LocalisedContentTranslation)

	for _, item := range items {
		if item == nil {
			continue
		}

		shopId := item.GetShopInfo().GetShopId()
		if _, ok := mapAllShopIdToShopLocation[shopId]; !ok {
			translatedContent := common.LocalisedContentTranslation{}
			translatedContent.SetPrimaryContent(primaryLanguage, "")
			translatedContent.SetBuyerLocalisedContent(primaryLanguage, secondaryLanguage, "")
			mapAllShopIdToShopLocation[shopId] = translatedContent
		}
	}

	return mapAllShopIdToShopLocation
}

func (cacheImpl *ItemShopLocationCacheImpl) GetSellerOverseaShopLocation(ctx context.Context, items []*sbsdm.ShopLocationItem) (map[int64]common.LocalisedContentTranslation, error) {
	userLang := sbsutils.GetLang(ctx)
	logCtx := logger.WithCtx(ctx)

	languageConfigs := config.GetLangConfig()
	regionPrimaryLanguage := languageConfigs.GetRegionPrimaryLanguage(config.Region())
	sameLangs := strings.EqualFold(regionPrimaryLanguage.Language, userLang)

	mapAllShopIdToShopLocation := make(map[int64]common.LocalisedContentTranslation)
	mapAllShopIdToShopLocation = getMapAllShopIdToShopLocation(items, regionPrimaryLanguage.Language, userLang)
	notCachedShopId := make([]int64, 0, len(items))
	shopIdRequireTranslation := make([]int64, 0)
	cacheTime := config.GetSellerOverseaShopLocationCacheTime()

	for _, item := range items {
		shopId := item.GetShopInfo().GetShopId()
		cacheKeySecondary := CacheShopIdOverseaLocationKey(shopId, userLang)
		cacheValueSecondary := &seller.ShopOverseaLocationCache{}
		cacheKeyPrimary := CacheShopIdOverseaLocationKey(shopId, regionPrimaryLanguage.Language)
		cacheValuePrimary := &seller.ShopOverseaLocationCache{}

		translatedContent := common.LocalisedContentTranslation{}

		err1 := cacheImpl.CacheHandler.Get(ctx, cacheKeyPrimary, cacheValuePrimary)
		err2 := cacheImpl.CacheHandler.Get(ctx, cacheKeySecondary, cacheValueSecondary)

		if err1 == nil && err2 == nil {
			logCtx.Withs(
				"cache_key_primary", cacheKeyPrimary,
				"cache_value_primary", cacheValuePrimary,
				"cache_key_secondary", cacheKeySecondary,
				"cache_value_secondary", cacheValueSecondary,
			).Info("get_seller_oversea_location.success")
			translatedContent.SetPrimaryContent(regionPrimaryLanguage.Language, cacheValuePrimary.OverseaShopLocation)
			translatedContent.SetBuyerLocalisedContent(regionPrimaryLanguage.Language, userLang, cacheValueSecondary.OverseaShopLocation)
			mapAllShopIdToShopLocation[shopId] = translatedContent
		} else if err1 == nil && err2 != nil {
			logCtx.Withs(
				"cache_key_primary", cacheKeyPrimary,
				"cache_value_primary", cacheValuePrimary,
				"cache_key_secondary", cacheKeySecondary,
				"cache_value_secondary", cacheValueSecondary,
			).Info("get_seller_oversea_location.partial_success")
			translatedContent.SetPrimaryContent(regionPrimaryLanguage.Language, cacheValuePrimary.OverseaShopLocation)
			shopIdRequireTranslation = append(shopIdRequireTranslation, item.GetShopInfo().GetShopId())
			mapAllShopIdToShopLocation[shopId] = translatedContent
		} else {
			logCtx.Withs(
				"cache_key_primary", cacheKeyPrimary,
				"cache_value_primary", cacheValuePrimary,
				"cache_key_secondary", cacheKeySecondary,
				"cache_value_secondary", cacheValueSecondary,
			).Info("get_seller_oversea_location.fail")
			notCachedShopId = append(notCachedShopId, shopId)
			LogCacheError(ctx, cacheKeyPrimary, err2)
		}
	}

	notCachedShopId = deleteDuplicatedId(notCachedShopId)
	if len(notCachedShopId) != 0 {
		locationMap, err := cacheImpl.SellerApi.GetSellerOverseaShopLocation(ctx, notCachedShopId)
		if err != nil {
			return nil, err
		}
		for shopId, translation := range locationMap {
			cacheKey := CacheShopIdOverseaLocationKey(shopId, userLang)
			location := translation.GetLocalisedContentByLangWithFallback(userLang)

			err = cacheImpl.CacheHandler.Set(ctx, cacheKey, &seller.ShopOverseaLocationCache{OverseaShopLocation: location}, cacheTime)
			if err != nil {
				logger.WithCtx(ctx).Withs("shopId", shopId, "location", location, "cache_time",
					cacheTime).WithTypedFields(ulog.Error(err)).Error("cache_shopId_and_region.error")
			} else {
				logger.WithCtx(ctx).Withs("shopId", shopId, "location", location, "cache_time",
					cacheTime).Info("cache_shopId_and_region.success")
			}
			if !sameLangs {
				cacheKey = CacheShopIdOverseaLocationKey(shopId, regionPrimaryLanguage.Language)
				locationPrimary := translation.GetPrimaryLocalisedContent()
				err = cacheImpl.CacheHandler.Set(ctx, cacheKey, &seller.ShopOverseaLocationCache{OverseaShopLocation: locationPrimary}, cacheTime)
				if err != nil {
					logger.WithCtx(ctx).Withs("shopId", shopId, "location", location, "cache_time",
						cacheTime).WithTypedFields(ulog.Error(err)).Error("cache_shopId_and_region.error")
				} else {
					logger.WithCtx(ctx).Withs("shopId", shopId, "location", location, "cache_time",
						cacheTime).Info("cache_shopId_and_region.success")
				}
			}
			// Pass up the user's translation
			mapAllShopIdToShopLocation[shopId] = translation
		}
	}

	for _, shopId := range shopIdRequireTranslation {
		translations := mapAllShopIdToShopLocation[shopId]
		primary := translations.GetPrimaryLocalisedContent()

		secondary := translator.TranslateCountryWithSpecifiedLang(ctx, primary, config.Region(), userLang)
		translations.SetBuyerLocalisedContent(regionPrimaryLanguage.Language, userLang, secondary)
		mapAllShopIdToShopLocation[shopId] = translations

		cacheKey := CacheShopIdOverseaLocationKey(shopId, userLang)
		cacheValue := &seller.ShopOverseaLocationCache{OverseaShopLocation: secondary}
		err := cacheImpl.CacheHandler.Set(ctx, cacheKey, cacheValue, cacheTime)
		if err != nil {
			logger.WithCtx(ctx).Withs("shopId", shopId, "location", secondary, "cache_time",
				cacheTime).WithTypedFields(ulog.Error(err)).Error("cache_shopId_and_region.error")
		} else {
			logger.WithCtx(ctx).Withs("shopId", shopId, "location", secondary, "cache_time",
				cacheTime).Info("cache_shopId_and_region.success")
		}
	}

	logCtx.Withs("notCachedShopId", notCachedShopId, "shopIdRequireTranslation", shopIdRequireTranslation,
		"mapAllShopIdToShopLocation", mapAllShopIdToShopLocation).Info("cache.get_seller_oversea_shop_location")
	return mapAllShopIdToShopLocation, nil
}

func (cacheImpl *ItemShopLocationCacheImpl) GetSellerWHPriority(ctx context.Context,
	buyerAddress *account.BuyerDefaultDeliveryAddress, items []*sbsdm.ShopLocationItem) (map[int64]map[string]int32, map[int64]map[string]uint32, error) {
	logCtx := logger.WithCtx(ctx)
	if buyerAddress == nil {
		logCtx.Info("get_seller_wh_priority.buyer_didnt_login")
		return nil, nil, nil
	}

	mapAllShopIdToWHPriority := make(map[int64]map[string]int32)
	mapAllShopIdToWHAddressId := make(map[int64]map[string]uint32)
	listNotCachedShopIds := make([]int64, 0, len(items))
	shopIds := GetItemShopIds(items)

	if config.GetUpgradeToBetterCache(ctx) {
		uniqueShopIDs := deleteDuplicatedId(shopIds)

		keyFunc := func(shopID int64) string {
			return CacheSellerWarehouseKeyV2(buyerAddress.GetCountry(), buyerAddress.GetState(), buyerAddress.GetCity(), buyerAddress.GetDistrict(), buyerAddress.GetTown(), shopID)
		}

		res, err := sbsutils.CacheLoadManyFromSlice[int64, *common.SellerWHPriority](
			ctx,
			cacheImpl.CacheHandler,
			uniqueShopIDs,
			keyFunc,
			func(ctx context.Context, inputIDs []int64) ([]*common.SellerWHPriority, error) {

				res, err := cacheImpl.OFApi.BatchGetSellerWarehousePriority(ctx, buyerAddress, inputIDs)
				if err != nil {
					logCtx.Withs("BatchGetSellerWarehousePriority Failed").Error("BatchGetSellerWarehousePriority|upgraded branch")
					return nil, err
				}
				toCache, _, _ := calculateSellerWHPriority(res)
				return toCache, nil
			},
			config.GetSellerWHPriorityCacheTime(),
		)
		if err != nil {
			logCtx.Error("Cannot get cache from BatchGetSellerWarehousePriority", ulog.Any("Error: ", err))
			return nil, nil, err
		}

		for shopID, cacheValue := range res {
			mapWarehouseToPriorityValue := make(map[string]int32)
			mapWarehouseToAddressId := make(map[string]uint32)

			for _, p := range cacheValue.Priorities {
				if p == nil {
					continue
				}
				mapWarehouseToPriorityValue[p.WHId] = p.Priority
				mapWarehouseToAddressId[p.WHId] = p.AddressId

			}
			mapAllShopIdToWHPriority[shopID] = mapWarehouseToPriorityValue
			mapAllShopIdToWHAddressId[shopID] = mapWarehouseToAddressId

		}

		return mapAllShopIdToWHPriority, mapAllShopIdToWHAddressId, nil
	}

	for _, shopId := range shopIds {
		cacheKey := CacheSellerWarehouseKey(buyerAddress.GetCountry(), buyerAddress.GetState(), buyerAddress.GetCity(), buyerAddress.GetDistrict(), buyerAddress.GetTown(), shopId)
		cacheValue := &common.SellerWHPriority{}
		err := cacheImpl.CacheHandler.Get(ctx, cacheKey, cacheValue)
		if err == nil {
			logCtx.Withs("cache_key", cacheKey, "cache_value", cacheValue).Info("get_seller_WH_priority.success")
			mapWarehouseToPriorityValue := make(map[string]int32)
			mapWarehouseToAddressId := make(map[string]uint32)
			for _, priority := range cacheValue.Priorities {
				if priority == nil {
					continue
				}
				mapWarehouseToPriorityValue[priority.WHId] = priority.Priority
				mapWarehouseToAddressId[priority.WHId] = priority.AddressId
			}
			mapAllShopIdToWHPriority[shopId] = mapWarehouseToPriorityValue
			mapAllShopIdToWHAddressId[shopId] = mapWarehouseToAddressId
		} else {
			logCtx.Withs("cache_key", cacheKey, "cache_value", cacheValue).Info("get_seller_WH_priority.failed")
			listNotCachedShopIds = append(listNotCachedShopIds, shopId)
			LogCacheError(ctx, cacheKey, err)
		}
	}

	listNotCachedShopIds = deleteDuplicatedId(listNotCachedShopIds)
	if len(listNotCachedShopIds) > 0 {
		sellerWarehousePriorities, err := cacheImpl.OFApi.BatchGetSellerWarehousePriority(ctx, buyerAddress, listNotCachedShopIds)
		if err != nil {
			return nil, nil, err
		}
		for _, sellerWarehousePriority := range sellerWarehousePriorities {
			mapWHIdToPriority := make(map[string]int32)
			mapWHIdToAddressId := make(map[string]uint32)
			shopId := int64(sellerWarehousePriority.GetShopId())
			numSellerWarehouse := len(sellerWarehousePriority.GetLocations())

			var whPriorities []*common.PriorityInfo
			for idx, location := range sellerWarehousePriority.GetLocations() {
				// seller WH priority does not explicitly return the priority but the index position of the seller warehouse implicitly declares the priority
				// so we calculate manually based on number of location returned, the lower the index position, the higher the priority
				priority := numSellerWarehouse - idx
				mapWHIdToPriority[location.GetLocationId()] = int32(priority)
				mapWHIdToAddressId[location.GetLocationId()] = uint32(location.GetAddressId())

				whPriority := &common.PriorityInfo{
					WHId:      location.GetLocationId(),
					Priority:  int32(priority),
					AddressId: uint32(location.GetAddressId()),
				}
				whPriorities = append(whPriorities, whPriority)
			}
			mapAllShopIdToWHPriority[shopId] = mapWHIdToPriority
			mapAllShopIdToWHAddressId[shopId] = mapWHIdToAddressId

			if len(whPriorities) == 0 {
				continue
			}
			cacheKey := CacheSellerWarehouseKey(buyerAddress.GetCountry(), buyerAddress.GetState(), buyerAddress.GetCity(), buyerAddress.GetDistrict(), buyerAddress.GetTown(), shopId)
			cacheValue := &common.SellerWHPriority{Priorities: whPriorities}
			cacheTime := config.GetSellerWHPriorityCacheTime()
			err = cacheImpl.CacheHandler.Set(ctx, cacheKey, cacheValue, cacheTime)
			if err != nil {
				logCtx.Withs("cache_time", cacheTime, "cache_key", cacheKey, "cache_value", cacheValue).
					WithTypedFields(ulog.Error(err)).Error("cache_seller_wh_priority.error")
			} else {
				logCtx.Withs("cache_time", cacheTime).Info("cache_seller_wh_priority.success")
			}
		}
	}
	logCtx.Withs("listNotCachedShopIds", listNotCachedShopIds, "mapAllShopIdToWHAddressId", mapAllShopIdToWHAddressId,
		"mapAllShopIdToWHPriority", mapAllShopIdToWHPriority).Info("cache.get_seller_warehouse_priority")
	return mapAllShopIdToWHPriority, mapAllShopIdToWHAddressId, nil
}

func getAllSellerIDToPickupAddress(items []*sbsdm.ShopLocationItem, regionPrimaryLang string, buyerLang string) map[int64]common.LocalisedContentTranslation {
	mapAllSellerIdToPickupAddress := make(map[int64]common.LocalisedContentTranslation)
	for _, item := range items {
		sellerId := item.GetShopInfo().GetItemShopUserId()
		translatedContent := common.LocalisedContentTranslation{}
		translatedContent.SetPrimaryContent(regionPrimaryLang, "")
		translatedContent.SetBuyerLocalisedContent(regionPrimaryLang, buyerLang, "")
		mapAllSellerIdToPickupAddress[sellerId] = translatedContent
	}
	return mapAllSellerIdToPickupAddress
}

func (cacheImpl *ItemShopLocationCacheImpl) translateSellerDefaultPickupAddress(ctx context.Context, addresses map[int64]common.LocalisedContentTranslation, regionPrimaryLang string, buyerLang string) map[int64]common.LocalisedContentTranslation {
	logCtx := logger.WithCtx(ctx)
	addressesToTranslate := make([]string, 0, len(addresses))

	for idx, _ := range addresses {
		translation := common.LocalisedContentTranslation{}
		translation.SetPrimaryContent(regionPrimaryLang, addresses[idx].Primary.LocalisedContent)
		if regionPrimaryLang == buyerLang {
			translation.SetBuyerLocalisedContent(regionPrimaryLang, buyerLang, addresses[idx].Primary.LocalisedContent)
		}
		translation.SetBuyerLocalisedContent(regionPrimaryLang, buyerLang, "")
		addresses[idx] = translation
	}

	if regionPrimaryLang == buyerLang {
		logCtx.Info("translateSellerDefaultPickupAddress|primary_lang_equals_secondary_lang")
		return addresses
	}

	for _, translation := range addresses {
		primaryContent := translation.GetPrimaryLocalisedContent()
		if primaryContent != "" {
			addressesToTranslate = append(addressesToTranslate, primaryContent)
		}
	}

	addressesToTranslate = common.RemoveDuplicateStrings(addressesToTranslate)
	translatedAddresses, err := cacheImpl.MapAPI.DivisonAutocomplete(ctx, addressesToTranslate, buyerLang)
	if err != nil {
		logCtx.Error("translateSellerDefaultPickupAddress|get_location_translation.error", ulog.Error(err))
		return addresses
	}

	for sellerId, translation := range addresses {
		primaryContent := translation.GetPrimaryLocalisedContent()
		if translated, ok := translatedAddresses[primaryContent]; ok {
			translation.SetBuyerLocalisedContent(regionPrimaryLang, buyerLang, translated)
			addresses[sellerId] = translation
		}
	}

	return addresses
}

func (cacheImpl *ItemShopLocationCacheImpl) GetSellerDefaultPickupAddress(ctx context.Context, items []*sbsdm.ShopLocationItem) (map[int64]common.LocalisedContentTranslation, error) {
	logCtx := logger.WithCtx(ctx)
	userLang := sbsutils.GetLang(ctx)

	languageConfigs := config.GetLangConfig()
	regionPrimaryLanguage := languageConfigs.GetRegionPrimaryLanguage(config.Region())

	sameLang := strings.EqualFold(regionPrimaryLanguage.Language, userLang)

	mapAllSellerIdToPickupAddress := getAllSellerIDToPickupAddress(items, regionPrimaryLanguage.Language, userLang)
	listNotCachedShopIds := make([]uint64, 0, len(items))
	cacheTime := config.GetSellerDefaultPickupAddressCacheTime()

	sellerIdRequireTranslation := make([]int64, 0)

	seenShopIds := make(map[int64]bool)
	seenSellerIds := make(map[int64]bool)

	if config.GetUpgradeToBetterCache(ctx) {
		for _, item := range items {
			if _, found := seenShopIds[item.GetShopInfo().GetShopId()]; !found {
				seenShopIds[item.GetShopInfo().GetShopId()] = true
			}
			if _, found := seenSellerIds[item.GetShopInfo().GetItemShopUserId()]; !found {
				seenSellerIds[item.GetShopInfo().GetItemShopUserId()] = true
			}
		}

		shopIds := common.GetKeys(seenShopIds)

		res, err := sbsutils.CacheLoadManyFromMap[int64, *common.ShopFlagAndDefaultAddressCache](
			ctx,
			cacheImpl.CacheHandler,
			shopIds,
			CacheDefaultPickupAddressAndWHFlagKey,
			func(ctx context.Context, shopIds []int64) (map[int64]*common.ShopFlagAndDefaultAddressCache, error) {
				res, err := cacheImpl.GetShopFlagAndAddress(ctx, shopIds)
				if err != nil {
					return nil, err
				}
				return res, nil
			},
			config.GetBuyerDeliveryAddressCacheTime(),
			cache.WithSkipCodec(),
		)
		if err != nil {
			return nil, err
		}

		for _, shopId := range shopIds {
			mapAllSellerIdToPickupAddress[res[shopId].SellerId] = common.LocalisedContentTranslation{
				Primary: common.LocalisedContent{
					Language:         regionPrimaryLanguage.Language,
					LocalisedContent: res[shopId].Address,
					IsPrimary:        true,
				},
			}
		}

		return cacheImpl.translateSellerDefaultPickupAddress(ctx, mapAllSellerIdToPickupAddress, regionPrimaryLanguage.Language, userLang), nil
	}

	for _, item := range items {
		shopId := item.GetShopInfo().GetShopId()
		sellerId := item.GetShopInfo().GetItemShopUserId()

		cacheKeyPrimary := CacheSellerPickupAddressKey(sellerId, regionPrimaryLanguage.Language)
		cacheValuePrimary := &common.SellerPickupAddressCache{}
		cacheKeySecondary := CacheSellerPickupAddressKey(sellerId, userLang)
		cacheValueSecondary := &common.SellerPickupAddressCache{}

		err1 := cacheImpl.CacheHandler.Get(ctx, cacheKeyPrimary, cacheValuePrimary)
		err2 := cacheImpl.CacheHandler.Get(ctx, cacheKeySecondary, cacheValueSecondary)

		translatedContent := common.LocalisedContentTranslation{}

		if err1 == nil && err2 == nil {
			logCtx.Withs(
				"shopId", shopId,
				"sellerId", sellerId,
				"primary_cache_key", cacheKeyPrimary,
				"primary_cache_value", cacheValuePrimary,
				"secondary_cache_key", cacheKeySecondary,
				"secondary_cache_value", cacheValueSecondary,
			).Info("get_seller_pickup_address.success")
			translatedContent.SetPrimaryContent(regionPrimaryLanguage.Language, cacheValuePrimary.Address)
			translatedContent.SetBuyerLocalisedContent(regionPrimaryLanguage.Language, userLang, cacheValueSecondary.Address)
			mapAllSellerIdToPickupAddress[sellerId] = translatedContent
		} else {
			if err1 == nil && err2 != nil {
				logCtx.Withs(
					"cache_key_primary", cacheKeyPrimary,
					"cache_value_primary", cacheValuePrimary,
					"cache_key_secondary", cacheKeySecondary,
					"cache_value_secondary", cacheValueSecondary,
				).Info("get_seller_pickup_address.partial_success")
				translatedContent.SetPrimaryContent(regionPrimaryLanguage.Language, cacheValuePrimary.Address)
				sellerIdRequireTranslation = append(sellerIdRequireTranslation, item.GetShopInfo().GetItemShopUserId())
				mapAllSellerIdToPickupAddress[sellerId] = translatedContent
			} else {
				logCtx.Withs(
					"cache_key_primary", cacheKeyPrimary,
					"cache_value_primary", cacheValuePrimary,
					"cache_key_secondary", cacheKeySecondary,
					"cache_value_secondary", cacheValueSecondary,
				).Info("get_seller_pickup_address.fail")
				listNotCachedShopIds = append(listNotCachedShopIds, uint64(shopId))
				LogCacheError(ctx, cacheKeyPrimary, err2)
			}
		}

	}

	sellerWithNoPickupAddress := make(map[int64]bool)
	listNotCachedShopIds = deleteDuplicatedShopId(listNotCachedShopIds)
	if len(listNotCachedShopIds) > 0 {
		shopsInfo, err := cacheImpl.ShopApi.GetShopListByShopIdListLimitSize(ctx, listNotCachedShopIds, false)
		if err != nil {
			return nil, err
		}

		for _, shopInfo := range shopsInfo {
			curSellerId := int64(shopInfo.GetUserId())
			sellerPickupAddressId := shopInfo.GetSellerPickupAddressId()
			if sellerPickupAddressId == 0 {
				sellerWithNoPickupAddress[curSellerId] = true
				continue
			}

			// Verified: addressID is always in the primary lang of the region
			mapSellerIdToPickupAddress, err := cacheImpl.AccountApi.GetSellerDefaultPickUpAddressByAddressId(ctx, curSellerId, sellerPickupAddressId)
			if err != nil {
				return nil, err
			}
			for sellerId, address := range mapSellerIdToPickupAddress {

				translation := common.LocalisedContentTranslation{}
				translation.SetPrimaryContent(regionPrimaryLanguage.Language, address)

				mapAllSellerIdToPickupAddress[sellerId] = translation
				sellerIdRequireTranslation = append(sellerIdRequireTranslation, sellerId)
				cacheKey := CacheSellerPickupAddressKey(sellerId, regionPrimaryLanguage.Language)
				err = cacheImpl.CacheHandler.Set(ctx, cacheKey, &common.SellerPickupAddressCache{Address: address}, cacheTime)
				if err != nil {
					logger.WithCtx(ctx).Withs("cache_time", cacheTime).Error("cache_seller_pickup_address.error")
				} else {
					logger.WithCtx(ctx).Withs("cache_time", cacheTime).Info("cache_seller_pickup_address.success")
				}
			}
		}
	}
	sellerIdRequireTranslation = deleteDuplicatedId(sellerIdRequireTranslation)
	pickupAddresses := make([]string, 0, len(sellerIdRequireTranslation))

	for _, shopId := range sellerIdRequireTranslation {
		translation := mapAllSellerIdToPickupAddress[shopId]
		pickupAddresses = append(pickupAddresses, translation.GetPrimaryLocalisedContent())
	}

	pickupAddresses = common.RemoveDuplicateStrings(pickupAddresses)

	translatedPickupAddresses, err := cacheImpl.MapAPI.DivisonAutocomplete(ctx, pickupAddresses, userLang)
	if err == nil {
		for sellerId, translation := range mapAllSellerIdToPickupAddress {
			pickupAddress := translation.GetPrimaryLocalisedContent()
			if translated, ok := translatedPickupAddresses[pickupAddress]; ok && !sameLang {
				translation.SetBuyerLocalisedContent(regionPrimaryLanguage.Language, userLang, translated)
				mapAllSellerIdToPickupAddress[sellerId] = translation

				cacheKey := CacheSellerPickupAddressKey(sellerId, userLang)
				cacheValue := &common.SellerPickupAddressCache{
					Address: translated,
				}
				err = cacheImpl.CacheHandler.Set(ctx, cacheKey, cacheValue, cacheTime)
				if err != nil {
					logger.WithCtx(ctx).Withs("cache_key", cacheKey, "cache_value", cacheValue,
						"cache_time", cacheTime).Error("cache_seller_wh_location.error")
				} else {
					logger.WithCtx(ctx).Withs("cache_key", cacheKey, "cache_value", cacheValue,
						"cache_time", cacheTime).Info("cache_seller_wh_location.success")
				}
			}
		}
	} else {
		logCtx.Error("GetSellerDefaultPickupAddress|get_location_translation.error", ulog.Error(err))
	}

	logCtx.Withs("listNotCachedShopIds", listNotCachedShopIds, "sellerWithNoPickupAddress", sellerWithNoPickupAddress,
		"sellerIdRequireTranslation", sellerIdRequireTranslation,
		"mapAllSellerIdToPickupAddress", mapAllSellerIdToPickupAddress).Info("cache.get_seller_default_pickup_address")

	return mapAllSellerIdToPickupAddress, nil
}

type SellerIDWHAddressIDPair struct {
	SellerId    int64
	WHAddressID int64
}

type SellerIDToMap struct {
	SellerID int64
	ResMap   map[int64]string
}

func getMapAllWHAddressIdToWHAddress(items []*sbsdm.ShopLocationItem, primaryLanguage string, secondaryLanguage string) map[uint32]common.LocalisedContentTranslation {
	mapAllWHAddressIdToWHAddress := make(map[uint32]common.LocalisedContentTranslation)

	for _, item := range items {
		if item == nil {
			continue
		}

		allWHAddressIds := GetAllWHAddressIdsFromSingleItem(item)
		for _, whAddressId := range allWHAddressIds {
			if _, ok := mapAllWHAddressIdToWHAddress[uint32(whAddressId)]; !ok {
				translatedContent := common.LocalisedContentTranslation{}
				translatedContent.SetPrimaryContent(primaryLanguage, "")
				translatedContent.SetBuyerLocalisedContent(primaryLanguage, secondaryLanguage, "")
				mapAllWHAddressIdToWHAddress[uint32(whAddressId)] = translatedContent
			}
		}
	}

	return mapAllWHAddressIdToWHAddress
}

func (cacheImpl *ItemShopLocationCacheImpl) GetSellerWHFullAddressByAddressId(ctx context.Context, items []*sbsdm.ShopLocationItem) (map[uint32]common.LocalisedContentTranslation, error) {
	logCtx := logger.WithCtx(ctx).Withs("func", "SellerWHFullAddressByAddressIds")

	languageConfigs := config.GetLangConfig()
	regionPrimaryLanguage := languageConfigs.GetRegionPrimaryLanguage(config.Region())
	userLang := sbsutils.GetLang(ctx)
	cacheTime := config.GetSellerDefaultPickupAddressCacheTime()

	sameLang := strings.EqualFold(regionPrimaryLanguage.Language, userLang)

	mapAllWHAddressIdToWHAddress := getMapAllWHAddressIdToWHAddress(items, regionPrimaryLanguage.Language, userLang)
	listNotCachedItem := make([]*sbsdm.ShopLocationItem, 0, len(items))
	whIdRequireTranslation := make([]uint32, 0)

	if config.GetUpgradeToBetterCache(ctx) {

		sellerIDWHAddressIDPair := make([]string, 0)
		sellerIDToWHAddressIDs := make(map[int64][]int64)

		for _, item := range items {
			allWHAddressIds := GetAllWHAddressIdsFromSingleItem(item)
			sellerId := item.GetShopInfo().GetItemShopUserId()

			if _, found := sellerIDToWHAddressIDs[sellerId]; found {
				sellerIDToWHAddressIDs[sellerId] = append(sellerIDToWHAddressIDs[sellerId], allWHAddressIds...)
			} else {
				sellerIDToWHAddressIDs[sellerId] = allWHAddressIds
			}

			for _, addressID := range allWHAddressIds {
				sellerIDWHAddressIDPair = append(sellerIDWHAddressIDPair, fmt.Sprintf("%d_%d", sellerId, addressID))
			}
		}
		res, err := sbsutils.CacheLoadManyFromMap[string, *common.SellerWHAddressCache](
			ctx,
			cacheImpl.CacheHandler,
			sellerIDWHAddressIDPair,
			CacheSellerWHAddressIdKeyWithString,
			func(ctx context.Context, pairs []string) (map[string]*common.SellerWHAddressCache, error) {
				toReturn := make(map[string]*common.SellerWHAddressCache)
				wg := sync.WaitGroup{}

				resultsChan := make(chan SellerIDToMap, len(sellerIDToWHAddressIDs))

				for sellerID, whAddressIDs := range sellerIDToWHAddressIDs {
					whAddressIDs = deleteDuplicatedId(whAddressIDs)
					wg.Add(1)

					go func(sellerID int64, whAddressIDs []int64) {
						defer wg.Done()

						mapWHAddressIdToLocation, err := cacheImpl.AccountApi.GetSellerWHFullAddressByAddressIds(ctx, sellerID, whAddressIDs)
						if err != nil {
							return
						}
						resultsChan <- SellerIDToMap{
							SellerID: sellerID,
							ResMap:   mapWHAddressIdToLocation,
						}

					}(sellerID, whAddressIDs)
				}

				wg.Wait()
				close(resultsChan)
				for result := range resultsChan {
					for whAddrID, curWHLocation := range result.ResMap {
						toReturn[fmt.Sprintf("%d_%d", result.SellerID, whAddrID)] = &common.SellerWHAddressCache{
							Address: curWHLocation,
						}
					}
				}

				return toReturn, nil
			},
			config.GetSellerDefaultPickupAddressCacheTime(),
		)

		if err != nil {
			logCtx.Error("Cannot get cache from GetSellerWHFullAddressByAddressIds", ulog.Any("Error: ", err))
			return nil, err
		}

		for partialKey, cacheValue := range res {
			parts := strings.Split(partialKey, "_")
			whAddrID, err := strconv.ParseUint(parts[1], 10, 32)
			if err != nil {
				continue
			}
			mapAllWHAddressIdToWHAddress[uint32(whAddrID)] = common.LocalisedContentTranslation{
				Primary: common.LocalisedContent{
					IsPrimary:        true,
					Language:         regionPrimaryLanguage.Language,
					LocalisedContent: cacheValue.Address,
				},
			}
		}
		return mapAllWHAddressIdToWHAddress, nil
	}

	for _, item := range items {
		sellerId := item.GetShopInfo().GetItemShopUserId()
		allWHAddressIds := GetAllWHAddressIdsFromSingleItem(item)
		for _, whAddressId := range allWHAddressIds {

			cacheKeyPrimary := CacheSellerWHAddressIdKey(sellerId, whAddressId, regionPrimaryLanguage.Language)
			cacheValuePrimary := &common.SellerWHAddressCache{}
			cacheKeySecondary := CacheSellerWHAddressIdKey(sellerId, whAddressId, userLang)
			cacheValueSecondary := &common.SellerWHAddressCache{}

			err1 := cacheImpl.CacheHandler.Get(ctx, cacheKeyPrimary, cacheValuePrimary)
			err2 := cacheImpl.CacheHandler.Get(ctx, cacheKeySecondary, cacheValueSecondary)

			translatedContent := common.LocalisedContentTranslation{}

			if err1 == nil && err2 == nil {
				logCtx.Withs(
					"sellerId", sellerId,
					"whAddressId", whAddressId,
					"primary_cache_key", cacheKeyPrimary,
					"primary_cache_value", cacheValuePrimary,
					"secondary_cache_key", cacheKeySecondary,
					"secondary_cache_value", cacheValueSecondary,
				).Info("get_seller_wh_full_address_cache.success")
				translatedContent.SetPrimaryContent(regionPrimaryLanguage.Language, cacheValuePrimary.Address)
				translatedContent.SetBuyerLocalisedContent(regionPrimaryLanguage.Language, userLang, cacheValueSecondary.Address)
				mapAllWHAddressIdToWHAddress[uint32(whAddressId)] = translatedContent
			} else if err1 == nil && err2 != nil {
				logCtx.Withs(
					"cache_key_primary", cacheKeyPrimary,
					"cache_value_primary", cacheValuePrimary,
					"cache_key_secondary", cacheKeySecondary,
					"cache_value_secondary", cacheValueSecondary,
				).Info("get_seller_wh_full_address_cache.partial_success")
				translatedContent.SetPrimaryContent(regionPrimaryLanguage.Language, cacheValuePrimary.Address)
				whIdRequireTranslation = append(whIdRequireTranslation, uint32(whAddressId))
				mapAllWHAddressIdToWHAddress[uint32(whAddressId)] = translatedContent
			} else {
				logCtx.Withs(
					"cache_key_primary", cacheKeyPrimary,
					"cache_value_primary", cacheValuePrimary,
					"cache_key_secondary", cacheKeySecondary,
					"cache_value_secondary", cacheValueSecondary,
				).Info("get_seller_wh_full_address_cache.fail")
				listNotCachedItem = append(listNotCachedItem, item)
				LogCacheError(ctx, cacheKeyPrimary, err2)
			}
		}
	}

	if len(listNotCachedItem) > 0 {
		for _, item := range listNotCachedItem {
			sellerId := item.GetShopInfo().GetItemShopUserId()
			whAddressIds := GetAllWHAddressIdsFromSingleItem(item)
			if sellerId == 0 || len(whAddressIds) == 0 {
				continue
			}
			mapWHAddressIdToLocation, err := cacheImpl.AccountApi.GetSellerWHFullAddressByAddressIds(ctx, sellerId, whAddressIds)
			if err != nil {
				return nil, err
			}
			for curWHAddressId, curWHLocation := range mapWHAddressIdToLocation {

				translation := common.LocalisedContentTranslation{}

				cacheKey := CacheSellerWHAddressIdKey(sellerId, curWHAddressId, regionPrimaryLanguage.Language)
				cacheValue := &common.SellerWHAddressCache{Address: curWHLocation}
				err = cacheImpl.CacheHandler.Set(ctx, cacheKey, cacheValue, cacheTime)
				if err != nil {
					logger.WithCtx(ctx).Withs("cache_key", cacheKey, "cache_value", cacheValue,
						"cache_time", cacheTime).Error("cache_seller_wh_location.error")
				} else {
					logger.WithCtx(ctx).Withs("cache_key", cacheKey, "cache_value", cacheValue,
						"cache_time", cacheTime).Info("cache_seller_wh_location.success")
				}

				translation.SetPrimaryContent(regionPrimaryLanguage.Language, curWHLocation)
				mapAllWHAddressIdToWHAddress[uint32(curWHAddressId)] = translation
				whIdRequireTranslation = append(whIdRequireTranslation, uint32(curWHAddressId))
			}
		}
	}

	whIdRequireTranslation = DeleteDuplicate(whIdRequireTranslation)
	whAddresses := make([]string, 0, len(whIdRequireTranslation))
	for _, whId := range whIdRequireTranslation {
		translation := mapAllWHAddressIdToWHAddress[whId]
		whAddresses = append(whAddresses, translation.GetPrimaryLocalisedContent())
	}

	translatedPickupAddresses, err := cacheImpl.MapAPI.DivisonAutocomplete(ctx, whAddresses, userLang)
	if err == nil {
		for _, item := range items {
			sellerId := item.GetShopInfo().GetItemShopUserId()
			whAddressIds := GetAllWHAddressIdsFromSingleItem(item)
			for _, whId := range whAddressIds {
				translation := mapAllWHAddressIdToWHAddress[uint32(whId)]
				whAddress := translation.GetPrimaryLocalisedContent()
				if translated, ok := translatedPickupAddresses[whAddress]; ok && !sameLang { // only cache if secondary is not the same as primary
					translation.SetBuyerLocalisedContent(regionPrimaryLanguage.Language, userLang, translated)
					mapAllWHAddressIdToWHAddress[uint32(whId)] = translation

					cacheKey := CacheSellerWHAddressIdKey(sellerId, whId, userLang)
					cacheValue := &common.SellerWHAddressCache{Address: translated}
					err = cacheImpl.CacheHandler.Set(ctx, cacheKey, cacheValue, cacheTime)
					if err != nil {
						logger.WithCtx(ctx).Withs("cache_key", cacheKey, "cache_value", cacheValue,
							"cache_time", cacheTime).Error("cache_seller_wh_location.error")
					} else {
						logger.WithCtx(ctx).Withs("cache_key", cacheKey, "cache_value", cacheValue,
							"cache_time", cacheTime).Info("cache_seller_wh_location.success")
					}
				}
			}
		}
	} else {
		logCtx.Error("GetSellerDefaultPickupAddress|get_location_translation.error", ulog.Error(err))
	}

	logCtx.Withs("mapAllWHAddressIdToWHAddress", mapAllWHAddressIdToWHAddress, "whIdRequireTranslation", whIdRequireTranslation).Info("cache.get_seller_warehouse_full_address")
	return mapAllWHAddressIdToWHAddress, nil
}

func (cacheImpl *ItemShopLocationCacheImpl) GetShopFlagAndAddress(ctx context.Context, shopIds []int64) (map[int64]*common.ShopFlagAndDefaultAddressCache, error) {
	shopIds = deleteDuplicatedId(shopIds)
	shopIdsUint64 := make([]uint64, 0, len(shopIds))

	shopIdToResult := make(map[int64]*common.ShopFlagAndDefaultAddressCache)

	for _, shopId := range shopIds {
		shopIdsUint64 = append(shopIdsUint64, uint64(shopId))
	}
	logCtx := logger.WithCtx(ctx)

	shopIdTo3PFAndMWHFlagFuture := sbsutils.GoFuture(func() (map[int64]bool, error) {
		return cacheImpl.SellerApi.MapShopIDTo3PFMWHFlag(ctx, shopIds)
	})

	shopInfoStructFuture := sbsutils.GoFuture(func() (ShopInfo, error) {
		return cacheImpl.BatchGetShopInfoFromShopID(ctx, shopIdsUint64)
	})

	err := sbsutils.FutureFirstError(ctx, shopIdTo3PFAndMWHFlagFuture, shopInfoStructFuture)
	if err != nil {
		logCtx.Withs("shop_ids", shopIds).Error("MapShopIDTo3PFMWHFlag_fail")
		return shopIdToResult, err
	}

	shopIdTo3PFAndMWHFlag := shopIdTo3PFAndMWHFlagFuture.Val(ctx)
	shopInfo := shopInfoStructFuture.Val(ctx)

	mapShopIdToPickupAddress := shopInfo.MapShopIdToPickupAddress
	mapShopIdToSellerId := shopInfo.MapShopIdToSellerId
	mapSellerIdToPickup := shopInfo.MapSellerIdToPickup

	if shopIdTo3PFAndMWHFlag == nil || mapShopIdToPickupAddress == nil || mapShopIdToSellerId == nil || mapSellerIdToPickup == nil {
		logCtx.Withs("shopIdTo3PFAndMWHFlag", shopIdTo3PFAndMWHFlag, "mapShopIdToPickupAddress", mapShopIdToPickupAddress, "mapShopIdToSellerId", mapShopIdToSellerId, "mapSellerIdToPickup", mapSellerIdToPickup).Error("GetShopFlagAndAddress fail due to dependency failure")
		return shopIdToResult, errors.New(fmt.Sprintf("dependency returned nil"))
	}

	for _, shopId := range shopIds {
		shopIdToResult[shopId] = &common.ShopFlagAndDefaultAddressCache{
			Is3PFOrMWHShop: shopIdTo3PFAndMWHFlag[shopId],
			Address:        mapSellerIdToPickup[mapShopIdToSellerId[shopId]],
			SellerId:       mapShopIdToSellerId[shopId],
		}
	}
	return shopIdToResult, nil
}

func (cacheImpl *ItemShopLocationCacheImpl) GetShop3PFMWHFlag(ctx context.Context, shopIds []int64) (map[int64]bool, error) {
	logCtx := logger.WithCtx(ctx)
	shopIds = deleteDuplicatedId(shopIds)

	if config.GetUpgradeToBetterCache(ctx) {
		res, err := sbsutils.CacheLoadManyFromMap[int64, *common.ShopFlagAndDefaultAddressCache](
			ctx,
			cacheImpl.CacheHandler,
			shopIds,
			CacheDefaultPickupAddressAndWHFlagKey,
			func(ctx context.Context, shopIds []int64) (map[int64]*common.ShopFlagAndDefaultAddressCache, error) {
				return cacheImpl.GetShopFlagAndAddress(ctx, shopIds)
			},
			config.GetBuyerDeliveryAddressCacheTime(),
			cache.WithSkipCodec(),
		)
		if err != nil {
			return nil, err
		}
		toReturn := make(map[int64]bool, len(shopIds))
		for _, shopId := range shopIds {
			toReturn[shopId] = res[shopId].Is3PFOrMWHShop
		}
		return toReturn, nil
	}

	shopIdToIs3PFMWHShop := make(map[int64]bool)
	notCachedShopIds := make([]int64, 0, len(shopIds))
	for _, shopId := range shopIds {
		cacheKey := Shop3PFAndWWHFlagCacheKey(shopId)
		cacheValue := &common.Shop3PFAndMWHFlagCache{}
		err := cacheImpl.CacheHandler.Get(ctx, cacheKey, cacheValue)
		if err == nil {
			logCtx.Withs("cache_key", cacheKey, "cache_value", cacheValue).Info("get_shop_3PF_flag_cache.success")
			shopIdToIs3PFMWHShop[shopId] = cacheValue.Is3PFOrMWHShop
		} else {
			logCtx.Withs("cache_key", cacheKey, "cache_value", cacheValue).Info("get_shop_3PF_flag_cache.failed")
			notCachedShopIds = append(notCachedShopIds, shopId)
		}
	}

	if len(notCachedShopIds) > 0 {
		shopIdTo3PFAndMWHFlag, err := cacheImpl.SellerApi.MapShopIDTo3PFMWHFlag(ctx, notCachedShopIds)
		if err != nil {
			return nil, err
		}
		for shopId, shop3PFMWHFlag := range shopIdTo3PFAndMWHFlag {
			shopIdToIs3PFMWHShop[shopId] = shop3PFMWHFlag
			cacheKey := Shop3PFAndWWHFlagCacheKey(shopId)
			cacheValue := &common.Shop3PFAndMWHFlagCache{Is3PFOrMWHShop: shop3PFMWHFlag}
			err = cacheImpl.CacheHandler.Set(ctx, cacheKey, cacheValue, config.GetShop3PFMWHFlagCacheTime())
			if err != nil {
				logger.WithCtx(ctx).Error("cache_shop_3pf_and_mwh_flag.error")
			} else {
				logger.WithCtx(ctx).Info("cache_shop_3pf_and_mwh_flag.success")
			}
		}
	}
	logCtx.Withs("NotCachedShopIds", notCachedShopIds,
		"shopIdToIs3PFOrMWH", shopIdToIs3PFMWHShop).Info("cache.GetShop3PFMWHFlag")

	return shopIdToIs3PFMWHShop, nil
}

func (cacheImpl *ItemShopLocationCacheImpl) GetShopByMultiWarehouseFeature(ctx context.Context, shopIds []int64) (map[int64]bool, error) {
	logCtx := logger.WithCtx(ctx)

	shopIdToIsSellerMWHShop := make(map[int64]bool)
	listNotCachedShopIds := make([]int64, 0, len(shopIds))

	if config.GetUpgradeToBetterCache(ctx) {
		uniqueShopIds := deleteDuplicatedId(shopIds)

		res, err := sbsutils.CacheLoadManyFromMap[int64, *common.ShopMultiWHFeatureCache](
			ctx,
			cacheImpl.CacheHandler,
			uniqueShopIds,
			CacheShopMultiWHFeatureKey,
			func(ctx context.Context, shopIDs []int64) (map[int64]*common.ShopMultiWHFeatureCache, error) {
				inWhitelist, notInWhitelist, err := cacheImpl.ShopApi.GetShopByMultiWarehouseFeature(ctx, shopIDs)
				if err != nil {
					return nil, err
				}
				toReturn := make(map[int64]*common.ShopMultiWHFeatureCache)
				for _, shopId := range inWhitelist {
					toReturn[shopId] = &common.ShopMultiWHFeatureCache{
						IsInMultiWHWhitelist: true,
					}
				}
				for _, shopId := range notInWhitelist {
					toReturn[shopId] = &common.ShopMultiWHFeatureCache{
						IsInMultiWHWhitelist: false,
					}
				}

				return toReturn, nil
			},
			config.GetShop3PFMWHFlagCacheTime(),
		)

		if err != nil {
			return nil, err
		}

		for shopID, isInWhitelist := range res {
			shopIdToIsSellerMWHShop[shopID] = isInWhitelist.IsInMultiWHWhitelist
		}

		return shopIdToIsSellerMWHShop, nil

	}

	for _, shopId := range shopIds {
		cacheKey := CacheShopMultiWHFeatureKey(shopId)
		cacheValue := &common.ShopMultiWHFeatureCache{}
		err := cacheImpl.CacheHandler.Get(ctx, cacheKey, cacheValue)
		if err == nil {
			logCtx.Withs("cache_key", cacheKey, "cache_value", cacheValue).Info("get_shop_multi_wh_feature_cache.success")
			shopIdToIsSellerMWHShop[shopId] = cacheValue.IsInMultiWHWhitelist
		} else {
			logCtx.Withs("cache_key", cacheKey, "cache_value", cacheValue).Info("get_shop_multi_wh_feature_cache.failed")
			listNotCachedShopIds = append(listNotCachedShopIds, shopId)
			LogCacheError(ctx, cacheKey, err)
		}
	}

	listNotCachedShopIds = deleteDuplicatedId(listNotCachedShopIds)
	if len(listNotCachedShopIds) > 0 {
		listShopIdInWhitelist, listShopIdNotInWhitelist, err := cacheImpl.ShopApi.GetShopByMultiWarehouseFeature(ctx, listNotCachedShopIds)
		if err != nil {
			return nil, err
		}

		for _, shopId := range listShopIdInWhitelist {
			shopIdToIsSellerMWHShop[shopId] = true
			cacheKey := CacheShopMultiWHFeatureKey(shopId)
			cacheValue := &common.ShopMultiWHFeatureCache{IsInMultiWHWhitelist: true}
			err = cacheImpl.CacheHandler.Set(ctx, cacheKey, cacheValue, config.GetShop3PFMWHFlagCacheTime())
			if err != nil {
				logger.WithCtx(ctx).Error("cache_shop_multi_wh_feature.error")
			} else {
				logger.WithCtx(ctx).Info("cache_shop_multi_wh_feature.success")
			}
		}

		for _, shopId := range listShopIdNotInWhitelist {
			shopIdToIsSellerMWHShop[shopId] = false
			cacheKey := CacheShopMultiWHFeatureKey(shopId)
			cacheValue := &common.ShopMultiWHFeatureCache{IsInMultiWHWhitelist: false}
			cacheTime := config.GetShop3PFMWHFlagCacheTime()
			err = cacheImpl.CacheHandler.Set(ctx, cacheKey, cacheValue, cacheTime)
			if err != nil {
				logger.WithCtx(ctx).Withs("shopId", shopId, "cache_time", cacheTime).Error("cache_shop_multi_wh_feature.error")
			} else {
				logger.WithCtx(ctx).Withs("shopId", shopId, "cache_time", cacheTime).Info("cache_shop_multi_wh_feature.success")
			}
		}
	}

	logCtx.Withs("listNotCachedShopIds", listNotCachedShopIds,
		"shopIdToIsSellerMWHShop", shopIdToIsSellerMWHShop).Info("cache.check_shop_multi_warehouse_feature")
	return shopIdToIsSellerMWHShop, nil
}

func calculateSellerWHPriority(sellerWarehousePriorities []*fpigsPb.PriorityInfo) ([]*common.SellerWHPriority, map[int64]map[string]int32, map[int64]map[string]uint32) {
	toCache := make([]*common.SellerWHPriority, 0)
	mapAllShopIdToWHPriority := make(map[int64]map[string]int32)
	mapAllShopIdToWHAddressId := make(map[int64]map[string]uint32)
	for _, sellerWarehousePriority := range sellerWarehousePriorities {
		mapWHIdToPriority := make(map[string]int32)
		mapWHIdToAddressId := make(map[string]uint32)
		shopId := int64(sellerWarehousePriority.GetShopId())
		numSellerWarehouse := len(sellerWarehousePriority.GetLocations())

		var whPriorities []*common.PriorityInfo
		for idx, location := range sellerWarehousePriority.GetLocations() {
			// seller WH priority does not explicitly return the priority but the index position of the seller warehouse implicitly declares the priority
			// so we calculate manually based on number of location returned, the lower the index position, the higher the priority
			priority := numSellerWarehouse - idx
			mapWHIdToPriority[location.GetLocationId()] = int32(priority)
			mapWHIdToAddressId[location.GetLocationId()] = uint32(location.GetAddressId())

			whPriority := &common.PriorityInfo{
				WHId:      location.GetLocationId(),
				Priority:  int32(priority),
				AddressId: uint32(location.GetAddressId()),
			}
			whPriorities = append(whPriorities, whPriority)
		}
		mapAllShopIdToWHPriority[shopId] = mapWHIdToPriority
		mapAllShopIdToWHAddressId[shopId] = mapWHIdToAddressId
		toCache = append(toCache, &common.SellerWHPriority{
			Priorities: whPriorities,
		})

	}
	return toCache, mapAllShopIdToWHPriority, mapAllShopIdToWHAddressId
}

type ShopInfo struct {
	MapSellerIdToPickup      map[int64]string
	MapShopIdToSellerId      map[int64]int64
	MapShopIdToPickupAddress map[int64]string
}

func GetShopInfo() ShopInfo {
	return ShopInfo{
		MapSellerIdToPickup:      make(map[int64]string),
		MapShopIdToSellerId:      make(map[int64]int64),
		MapShopIdToPickupAddress: make(map[int64]string),
	}
}

func (cacheImpl *ItemShopLocationCacheImpl) BatchGetShopInfoFromShopID(ctx context.Context, shopIDs []uint64) (ShopInfo, error) {
	logCtx := logger.WithCtx(ctx)
	shopInfos, err := cacheImpl.ShopApi.GetShopListByShopIdListLimitSize(ctx, shopIDs, false)
	shopInfo := GetShopInfo()
	if err != nil {
		logCtx.Withs("shop_ids", shopIDs).Error("GetShopListByShopIdListLimitSize_fail")
		return shopInfo, err
	}

	apiFailure := common.APIFailure{}
	called := make(map[string]bool) // reduce duplicate call
	futuresList := make([]sbsutils.Future[ShopInfo], 0, len(shopInfos))

	for _, info := range shopInfos {
		curSellerId := int64(info.GetUserId())
		sellerPickupAddressId := info.GetSellerPickupAddressId()
		combined := fmt.Sprintf("%d_%d", curSellerId, sellerPickupAddressId)
		if _, found := called[combined]; found {
			continue
		}
		called[combined] = true

		if sellerPickupAddressId == 0 {
			continue
		}
		shopID := int64(info.GetShopId())
		futuresList = append(futuresList, sbsutils.GoFuture(func() (ShopInfo, error) {
			return cacheImpl.BuildShopInfoFromSellerData(ctx, curSellerId, sellerPickupAddressId, shopID)
		}))
	}
	futureErrors := make([]sbsutils.FutureError, len(futuresList))
	for i, future := range futuresList {
		futureErrors[i] = future
	}
	err = sbsutils.FutureFirstError(ctx, futureErrors...)
	if err != nil {
		return shopInfo, errors.New(fmt.Sprintf("GetShopFlagAndAddress fail due to dependency failure %v", apiFailure))
	}

	for _, futureRes := range futuresList {
		res := futureRes.Val(ctx)
		for sellerId, address := range res.MapSellerIdToPickup {
			shopInfo.MapSellerIdToPickup[sellerId] = address
		}
		for shopId, address := range res.MapShopIdToPickupAddress {
			shopInfo.MapShopIdToPickupAddress[shopId] = address
		}
		for shopId, sellerId := range res.MapShopIdToSellerId {
			shopInfo.MapShopIdToSellerId[shopId] = sellerId
		}
	}

	return shopInfo, nil
}

func (cacheImpl *ItemShopLocationCacheImpl) BuildShopInfoFromSellerData(ctx context.Context, sellerID, pickupAddressID, shopID int64) (ShopInfo, error) {
	logCtx := logger.WithCtx(ctx)
	apiResult := GetShopInfo()
	// there should be 1 seller returned
	sellerPickupMap, err := cacheImpl.AccountApi.GetSellerDefaultPickUpAddressByAddressId(ctx, sellerID, pickupAddressID)
	if err != nil {
		logCtx.Withs("seller_id", sellerID).Error("GetSellerDefaultPickUpAddressByAddressId failed")
		return apiResult, err
	}
	mapSellerIdToPickupAddress := make(map[int64]string)
	mapShopIdToPickupAddress := make(map[int64]string)
	mapShopIdToSellerId := make(map[int64]int64)

	for sellerId, address := range sellerPickupMap {
		mapSellerIdToPickupAddress[sellerId] = address
	}
	mapShopIdToPickupAddress[shopID] = sellerPickupMap[sellerID]
	mapShopIdToSellerId[shopID] = sellerID

	apiResult.MapShopIdToPickupAddress = mapShopIdToPickupAddress
	apiResult.MapSellerIdToPickup = mapSellerIdToPickupAddress
	apiResult.MapShopIdToSellerId = mapShopIdToSellerId

	return apiResult, nil
}
