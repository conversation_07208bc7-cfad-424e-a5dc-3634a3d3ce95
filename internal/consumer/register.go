package consumer

import (
	"git.garena.com/shopee/mts/go-application-server/gas"
	"git.garena.com/shopee/mts/go-application-server/spi/kafka"
)

func Register() gas.Option {
	return gas.Options(
		kafka.ProvideMessageProcessor(new(SalesOrderMessageProcessor), "sales_order_processor"),
		kafka.RegisterConsumerOptions("order_change_consumer",
			kafka.WithMessageProcessor("sales_order_processor"),
		),
	)
}
